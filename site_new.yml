---
- name: Konfiguracja nowych serwerów
  hosts: serwery_nowe
  become: true
  roles:
    - role: common
      tags: [common, base, system]

    - role: security_hardening
      tags: [security, hardening, firewall]

    - role: docker_setup
      tags: [docker, containers]

    - role: baserow_app
      tags: [baserow, application]

    - role: nginx_setup
      tags: [nginx, proxy, web]

    - role: ssl_setup
      tags: [ssl, certificates, https]
      when: ssl_setup_enabled | default(true)

    - role: backup_setup
      tags: [backup, maintenance]

    - role: logging_agent
      tags: [logging, monitoring]