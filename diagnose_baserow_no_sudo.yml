---
# Playbook diagnostyczny bez uprawnień sudo
# Większość informacji możemy uzyskać bez root

- name: Diagnoza Baserow na OVH (bez sudo)
  hosts: simetria-ovh
  become: false
  gather_facts: true
  
  tasks:
    # ==========================================================================
    # KROK 1: Podstawowe informacje o systemie
    # ==========================================================================
    - name: Sprawdź informacje o systemie
      debug:
        msg:
          - "Hostname: {{ ansible_hostname }}"
          - "OS: {{ ansible_distribution }} {{ ansible_distribution_version }}"
          - "Architektura: {{ ansible_architecture }}"
          - "Pam<PERSON>ęć RAM: {{ ansible_memtotal_mb }}MB"
          - "Użytkownik: {{ ansible_user_id }}"

    # ==========================================================================
    # KROK 2: Sprawdzenie kontenerów Docker (jako użytkownik)
    # ==========================================================================
    - name: Sprawdź wszystkie kontenery Docker
      shell: docker ps -a
      register: docker_containers
      ignore_errors: true
      
    - name: Wyświetl kontenery Docker
      debug:
        msg: 
          - "=== KONTENERY DOCKER ==="
          - "{{ docker_containers.stdout_lines }}"
      when: docker_containers is success

    # ==========================================================================
    # KROK 3: Sprawdzenie volumes Docker
    # ==========================================================================
    - name: Sprawdź volumes Docker
      shell: docker volume ls
      register: docker_volumes
      ignore_errors: true
      
    - name: Wyświetl volumes Docker
      debug:
        msg:
          - "=== VOLUMES DOCKER ==="
          - "{{ docker_volumes.stdout_lines }}"
      when: docker_volumes is success

    # ==========================================================================
    # KROK 4: Sprawdzenie kontenerów Baserow
    # ==========================================================================
    - name: Sprawdź kontenery Baserow
      shell: docker ps -a | grep -i baserow || echo "Brak kontenerów Baserow"
      register: baserow_containers
      ignore_errors: true
      
    - name: Wyświetl kontenery Baserow
      debug:
        msg:
          - "=== KONTENERY BASEROW ==="
          - "{{ baserow_containers.stdout_lines }}"

    # ==========================================================================
    # KROK 5: Sprawdzenie kontenerów PostgreSQL
    # ==========================================================================
    - name: Sprawdź kontenery PostgreSQL
      shell: docker ps -a | grep -i postgres || echo "Brak kontenerów PostgreSQL"
      register: postgres_containers
      ignore_errors: true
      
    - name: Wyświetl kontenery PostgreSQL
      debug:
        msg:
          - "=== KONTENERY POSTGRESQL ==="
          - "{{ postgres_containers.stdout_lines }}"

    # ==========================================================================
    # KROK 6: Sprawdzenie plików docker-compose w katalogu domowym
    # ==========================================================================
    - name: Szukaj plików docker-compose w /home/<USER>
      find:
        paths: 
          - "/home/<USER>"
        patterns: "docker-compose*.yml"
        recurse: true
      register: compose_files_home
      
    - name: Wyświetl znalezione pliki docker-compose
      debug:
        msg:
          - "=== PLIKI DOCKER-COMPOSE (HOME) ==="
          - "{{ item.path }}"
      loop: "{{ compose_files_home.files }}"
      when: compose_files_home.files | length > 0

    # ==========================================================================
    # KROK 7: Sprawdzenie użycia dysku w katalogu domowym
    # ==========================================================================
    - name: Sprawdź użycie dysku w katalogu domowym
      shell: du -sh /home/<USER>/* 2>/dev/null || echo "Brak dostępu"
      register: home_disk_usage
      
    - name: Wyświetl użycie dysku
      debug:
        msg:
          - "=== UŻYCIE DYSKU (HOME) ==="
          - "{{ home_disk_usage.stdout_lines }}"

    # ==========================================================================
    # KROK 8: Sprawdzenie procesów użytkownika
    # ==========================================================================
    - name: Sprawdź procesy użytkownika
      shell: ps aux | grep {{ ansible_user_id }}
      register: user_processes
      
    - name: Wyświetl procesy użytkownika
      debug:
        msg:
          - "=== PROCESY UŻYTKOWNIKA ==="
          - "{{ user_processes.stdout_lines }}"

    # ==========================================================================
    # KROK 9: Sprawdzenie portów dostępnych dla użytkownika
    # ==========================================================================
    - name: Sprawdź otwarte porty (użytkownik)
      shell: netstat -tulpn 2>/dev/null | grep {{ ansible_user_id }} || ss -tulpn 2>/dev/null | grep {{ ansible_user_id }} || echo "Brak informacji o portach"
      register: user_ports
      
    - name: Wyświetl porty użytkownika
      debug:
        msg:
          - "=== PORTY UŻYTKOWNIKA ==="
          - "{{ user_ports.stdout_lines }}"

    # ==========================================================================
    # KROK 10: Sprawdzenie logów Docker (jeśli dostępne)
    # ==========================================================================
    - name: Sprawdź logi ostatnich kontenerów
      shell: docker logs --tail 50 $(docker ps -q | head -1) 2>/dev/null || echo "Brak dostępu do logów"
      register: docker_logs
      ignore_errors: true
      
    - name: Wyświetl próbkę logów Docker
      debug:
        msg:
          - "=== PRÓBKA LOGÓW DOCKER ==="
          - "{{ docker_logs.stdout_lines }}"
      when: docker_logs is success
