---
- name: Test backup verification functionality
  hosts: baserow_servers
  become: yes
  gather_facts: yes
  
  vars:
    ansible_ssh_timeout: 30
    ansible_command_timeout: 300
    backup_test_dir: /tmp/test_backup_verification
    
  tasks:
    - name: Ensure Docker is running
      systemd:
        name: docker
        state: started
        enabled: yes
        
    - name: Create test backup directory
      file:
        path: "{{ backup_test_dir }}"
        state: directory
        mode: '0755'
        
    - name: Create test database backup file
      copy:
        content: |
          --
          -- PostgreSQL database dump
          --
          
          SET statement_timeout = 0;
          SET lock_timeout = 0;
          SET idle_in_transaction_session_timeout = 0;
          SET client_encoding = 'UTF8';
          SET standard_conforming_strings = on;
          SELECT pg_catalog.set_config('search_path', '', false);
          SET check_function_bodies = false;
          SET xmloption = content;
          SET client_min_messages = warning;
          SET row_security = off;
          
          CREATE DATABASE baserow_test;
          
          \connect baserow_test;
          
          CREATE TABLE test_data (
              id integer NOT NULL,
              name character varying(100),
              created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
          );
          
          INSERT INTO test_data (id, name) VALUES (1, 'test_record_1');
          INSERT INTO test_data (id, name) VALUES (2, 'test_record_2');
          INSERT INTO test_data (id, name) VALUES (3, 'test_record_3');
        dest: "{{ backup_test_dir }}/test_database_backup.sql"
        mode: '0644'
        
    - name: Create test media backup file
      copy:
        content: |
          This is a test media file for backup verification
          Created at: {{ ansible_date_time.iso8601 }}
        dest: "{{ backup_test_dir }}/test_media_file.txt"
        mode: '0644'
        
    - name: Test backup file integrity check
      shell: |
        cd {{ backup_test_dir }}
        # Check if SQL file has valid content
        if grep -q "PostgreSQL database dump" test_database_backup.sql; then
          echo "✅ SQL backup file validation: PASSED"
        else
          echo "❌ SQL backup file validation: FAILED"
          exit 1
        fi
        
        # Check if media file exists and has content
        if [ -s test_media_file.txt ]; then
          echo "✅ Media backup file validation: PASSED"
        else
          echo "❌ Media backup file validation: FAILED"
          exit 1
        fi
      register: integrity_check
      
    - name: Display integrity check results
      debug:
        var: integrity_check.stdout_lines
        
    - name: Test backup size verification
      shell: |
        cd {{ backup_test_dir }}
        SQL_SIZE=$(du -b test_database_backup.sql | cut -f1)
        MEDIA_SIZE=$(du -b test_media_file.txt | cut -f1)
        
        echo "📊 Database backup size: $SQL_SIZE bytes"
        echo "📊 Media backup size: $MEDIA_SIZE bytes"
        
        # Check if files are not empty
        if [ $SQL_SIZE -gt 0 ] && [ $MEDIA_SIZE -gt 0 ]; then
          echo "✅ Backup size verification: PASSED"
        else
          echo "❌ Backup size verification: FAILED"
          exit 1
        fi
      register: size_check
      
    - name: Display size check results
      debug:
        var: size_check.stdout_lines
        
    - name: Test backup compression
      shell: |
        cd {{ backup_test_dir }}
        
        # Create compressed backup
        tar -czf test_backup_compressed.tar.gz test_database_backup.sql test_media_file.txt
        
        ORIGINAL_SIZE=$(du -b test_database_backup.sql test_media_file.txt | awk '{sum+=$1} END {print sum}')
        COMPRESSED_SIZE=$(du -b test_backup_compressed.tar.gz | cut -f1)
        
        echo "📊 Original total size: $ORIGINAL_SIZE bytes"
        echo "📊 Compressed size: $COMPRESSED_SIZE bytes"
        
        # Calculate compression ratio (simplified without bc)
        if [ $ORIGINAL_SIZE -gt 0 ]; then
          RATIO=$((COMPRESSED_SIZE * 100 / ORIGINAL_SIZE))
          echo "📊 Compression ratio: ${RATIO}%"
          
          if [ $COMPRESSED_SIZE -le $ORIGINAL_SIZE ]; then
            echo "✅ Backup compression: PASSED"
          else
            echo "❌ Backup compression: FAILED"
            exit 1
          fi
        else
          echo "❌ Cannot calculate compression ratio"
          exit 1
        fi
      register: compression_check
      
    - name: Display compression check results
      debug:
        var: compression_check.stdout_lines
        
    - name: Create restoration directory
      file:
        path: "{{ backup_test_dir }}/restored"
        state: directory
        mode: '0755'
        
    - name: Test backup restoration simulation
      shell: |
        cd {{ backup_test_dir }}
        
        # Extract compressed backup
        tar -xzf test_backup_compressed.tar.gz -C restored/
        
        # Verify extracted files
        if [ -f restored/test_database_backup.sql ] && [ -f restored/test_media_file.txt ]; then
          echo "✅ Backup extraction: PASSED"
        else
          echo "❌ Backup extraction: FAILED"
          exit 1
        fi
        
        # Compare original and restored files
        if diff test_database_backup.sql restored/test_database_backup.sql >/dev/null 2>&1; then
          echo "✅ Database file integrity after restoration: PASSED"
        else
          echo "❌ Database file integrity after restoration: FAILED"
          exit 1
        fi
        
        if diff test_media_file.txt restored/test_media_file.txt >/dev/null 2>&1; then
          echo "✅ Media file integrity after restoration: PASSED"
        else
          echo "❌ Media file integrity after restoration: FAILED"
          exit 1
        fi
      register: restoration_check
      
    - name: Display restoration check results
      debug:
        var: restoration_check.stdout_lines
        
    - name: Display final test results
      debug:
        msg:
          - "🎯 WYNIKI TESTÓW WERYFIKACJI BACKUPÓW:"
          - "✅ Test integralności plików: PASSED"
          - "✅ Test weryfikacji rozmiaru: PASSED"
          - "✅ Test kompresji: PASSED"
          - "✅ Test przywracania: PASSED"
          - "📊 Wszystkie testy weryfikacji backupów: UKOŃCZONE POMYŚLNIE"
          
    - name: Cleanup test files
      file:
        path: "{{ backup_test_dir }}"
        state: absent
      ignore_errors: yes