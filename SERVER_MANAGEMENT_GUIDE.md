# 📚 PRZEWODNIK ZARZĄDZANIA SERWERAMI ANSIBLE

## 🎯 PRZEGLĄD SYSTEMU

### 📊 MAPA INFRASTRUKTURY

| Serwer | Grupa | IP | Port SSH | Aplikacje | Status |
|--------|-------|----|---------|-----------| -------|
| **simetria-hetzner001** | `serwery_nowe` | *********** | 22 | Baserow, Monitoring | ✅ Nowoczesny |
| **simetria-ovh** | `serwery_legacy` | ************** | 1988 | n8n, Qdrant | ⚠️ Legacy |
| **sms-server** | `serwery_legacy` | ssh-hp.simetria.cc | 22 | SMS, CloudFlare | ⚠️ Legacy |

### 🔧 RÓŻNICE KONFIGURACYJNE

#### **Serwery Nowe** (`serwery_nowe`)
- ✅ **Pełna automatyzacja** - Wszystkie role Ansible
- ✅ **Standardowe konfiguracje** - Porty, ścieżki, usługi
- ✅ **Centralne logowanie** - Loki + Grafana
- ✅ **Backup z GPG** - Automatyczne szyfrowane backupy
- ✅ **Monitoring** - Prometheus + Alertmanager

#### **Serwery Legacy** (`serwery_legacy`)
- ⚠️ **Ręczna konfiguracja** - Różne aplikacje i ścieżki
- ⚠️ **Niestandardowe porty** - SSH:1988 (OVH), CloudFlare Tunnel (SMS)
- ⚠️ **Lokalne logi** - Brak centralnego logowania
- ⚠️ **Różne systemy backup** - Wymagają indywidualnego podejścia

## 🚀 GŁÓWNE PLAYBOOKI

### 1️⃣ **AKTUALIZACJA SERWERÓW** (`update_all_servers.yml`)

**Cel:** Bezpieczna aktualizacja systemu na wszystkich serwerach

```bash
# Podstawowa aktualizacja (tryb bezpieczny)
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass

# Pełna aktualizacja (dist-upgrade)
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass -e "update_mode=full"

# Aktualizacja równoległa (2 serwery naraz)
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass -e "update_serial=2"

# Tylko serwery nowe
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass --tags "new_servers"

# Tylko serwery legacy
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass --tags "legacy_servers"
```

**Funkcje:**
- ✅ Aktualizacja po kolei (serial: 1) dla bezpieczeństwa
- ✅ Różne tryby: `safe` (domyślny) i `full`
- ✅ Sprawdzenie stanu przed i po aktualizacji
- ✅ Weryfikacja kluczowych usług
- ✅ Informacja o wymaganym restarcie

### 2️⃣ **PRZEGLĄD SERWERÓW** (`server_overview.yml`)

**Cel:** Bezpieczny przegląd stanu wszystkich serwerów bez ingerencji

```bash
# Pełny przegląd wszystkich serwerów
ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass

# Tylko informacje systemowe
ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass --tags "system"

# Tylko bezpieczeństwo
ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass --tags "security"

# Tylko aplikacje
ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass --tags "applications"
```

**Funkcje:**
- ✅ Informacje systemowe (CPU, RAM, dysk, sieć)
- ✅ Status aplikacji i usług
- ✅ Analiza bezpieczeństwa i aktualizacji
- ✅ Monitoring i logi
- ✅ Generowanie raportu HTML
- ✅ Raporty Markdown dla każdego serwera

### 3️⃣ **KONTROLA ZDROWIA** (`health_check.yml`)

**Cel:** Kompleksowa analiza zdrowia serwerów i aplikacji

```bash
# Podstawowa kontrola zdrowia
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass

# Z analizą AI (jeśli dostępne Claude/Gemini CLI)
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass -e "ai_analysis=true"

# Tylko zasoby systemowe
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass --tags "resources"

# Tylko aplikacje
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass --tags "applications"

# Tylko analiza logów
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass --tags "logs"
```

**Funkcje:**
- ✅ Analiza zasobów systemowych (CPU, RAM, dysk, I/O)
- ✅ Sprawdzenie kluczowych usług
- ✅ Health check aplikacji (Baserow, n8n, Qdrant)
- ✅ Analiza logów systemowych i aplikacji
- ✅ Wykrywanie problematycznych logów
- ✅ Opcjonalna analiza AI (Claude/Gemini)
- ✅ Raport HTML z podsumowaniem

## 📋 SCENARIUSZE UŻYCIA

### 🔄 **RUTYNOWA KONSERWACJA**

```bash
# 1. Przegląd stanu serwerów
ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass

# 2. Kontrola zdrowia
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass

# 3. Aktualizacja (jeśli potrzeba)
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass

# 4. Ponowna kontrola zdrowia
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass
```

### 🚨 **DIAGNOZA PROBLEMÓW**

```bash
# 1. Szybka kontrola zdrowia
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass

# 2. Szczegółowa analiza logów
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass --tags "logs"

# 3. Analiza AI (jeśli dostępne)
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass -e "ai_analysis=true"

# 4. Sprawdzenie konkretnego serwera
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass --limit "simetria-ovh"
```

### 🔧 **AKTUALIZACJA BEZPIECZNA**

```bash
# 1. Sprawdź stan przed aktualizacją
ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass

# 2. Aktualizacja po kolei (domyślnie)
ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass

# 3. Sprawdź stan po aktualizacji
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass

# 4. W razie problemów - rollback lub naprawa
```

## 📊 INTERPRETACJA RAPORTÓW

### 🟢 **RAPORT PRZEGLĄDU** (`server_overview_report.html`)

**Lokalizacja:** `/tmp/server_overview_YYYYMMDD/`

**Zawiera:**
- 📊 Podsumowanie wszystkich serwerów
- 📄 Linki do szczegółowych raportów Markdown
- 🔧 Rekomendowane następne kroki

### 🏥 **RAPORT ZDROWIA** (`health_check_report.html`)

**Lokalizacja:** `/tmp/health_check_YYYYMMDD/`

**Zawiera:**
- 📊 Podsumowanie zdrowia (🟢 zdrowe, 🟡 ostrzeżenia, 🔴 krytyczne)
- 📄 Szczegółowe raporty dla każdego serwera
- 🤖 Analiza AI (jeśli włączona)
- 🚨 Alerty i rekomendacje

### 📋 **STATUSY ZDROWIA**

| Status | Znaczenie | Działanie |
|--------|-----------|-----------|
| 🟢 **ZDROWY** | Wszystko działa poprawnie | Monitoring rutynowy |
| 🟡 **OSTRZEŻENIA** | Drobne problemy, wymaga uwagi | Sprawdź szczegóły, zaplanuj naprawę |
| 🔴 **KRYTYCZNY** | Poważne problemy | Natychmiastowa interwencja |

## 🤖 ANALIZA AI (OPCJONALNA)

### 📦 **WYMAGANIA**

```bash
# Instalacja Claude CLI (jeśli dostępne)
pip install claude-cli

# Instalacja Gemini CLI (jeśli dostępne)
pip install google-generativeai-cli

# Konfiguracja API keys
export CLAUDE_API_KEY="your-api-key"
export GEMINI_API_KEY="your-api-key"
```

### 🔍 **UŻYCIE**

```bash
# Włącz analizę AI
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass -e "ai_analysis=true"
```

**Funkcje AI:**
- 🔍 Analiza problematycznych logów
- 💡 Rekomendacje naprawcze
- 📊 Priorytetyzacja problemów
- 🛠️ Sugestie optymalizacji

## ⚠️ WAŻNE UWAGI BEZPIECZEŃSTWA

### 🔒 **SERWERY LEGACY**

- ⚠️ **Ostrożność:** Serwery legacy mają różne konfiguracje
- ⚠️ **Restart:** Nie restartuj automatycznie - zawsze ręcznie
- ⚠️ **Backup:** Przed większymi zmianami wykonaj backup
- ⚠️ **Test:** Testuj zmiany na jednym serwerze

### 🔐 **HASŁA I KLUCZE**

```bash
# Zawsze używaj vault password file
--vault-password-file .vault_pass

# Sprawdź czy plik vault istnieje
ls -la .vault_pass

# W razie problemów z hasłem
ansible-vault edit group_vars/all/vault.yml
```

## 🛠️ ROZWIĄZYWANIE PROBLEMÓW

### ❌ **BŁĘDY POŁĄCZENIA SSH**

```bash
# Test połączenia
ansible all -i inventory -m ping --vault-password-file .vault_pass

# Sprawdź konkretny serwer
ansible simetria-ovh -i inventory -m ping --vault-password-file .vault_pass

# Debug SSH
ansible simetria-ovh -i inventory -m ping --vault-password-file .vault_pass -vvv
```

### ❌ **BŁĘDY VAULT**

```bash
# Sprawdź vault
ansible-vault view group_vars/all/vault.yml --vault-password-file .vault_pass

# Edytuj vault
ansible-vault edit group_vars/all/vault.yml --vault-password-file .vault_pass
```

### ❌ **PROBLEMY Z APLIKACJAMI**

```bash
# Sprawdź konkretną aplikację
ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass --tags "applications" --limit "simetria-ovh"

# Diagnostyka n8n
ansible simetria-ovh -i inventory -m shell -a "cd /home/<USER>/projekty/n8n && docker-compose ps" --vault-password-file .vault_pass

# Diagnostyka Baserow
ansible simetria-hetzner001 -i inventory -m shell -a "cd /opt/baserow && docker-compose ps" --vault-password-file .vault_pass
```

## 📞 WSPARCIE

### 🆘 **W RAZIE PROBLEMÓW**

1. **Sprawdź raporty** - HTML i Markdown
2. **Uruchom diagnostykę** - `health_check.yml`
3. **Sprawdź logi** - sekcja logów w raportach
4. **Użyj AI analizy** - jeśli dostępne
5. **Sprawdź dokumentację** - ten przewodnik

### 📋 **PRZYDATNE KOMENDY**

```bash
# Szybki test wszystkich serwerów
ansible all -i inventory -m ping --vault-password-file .vault_pass

# Sprawdź uptime
ansible all -i inventory -m shell -a "uptime" --vault-password-file .vault_pass

# Sprawdź miejsce na dysku
ansible all -i inventory -m shell -a "df -h" --vault-password-file .vault_pass

# Sprawdź pamięć
ansible all -i inventory -m shell -a "free -h" --vault-password-file .vault_pass
```

---

**📝 Ostatnia aktualizacja:** 2025-01-09  
**🔄 Wersja:** 1.0  
**👨‍💻 Autor:** Ansible Management System
