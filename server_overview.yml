---
# ============================================================================
# PLAYBOOK PRZEGLĄDU WSZYSTKICH SERWERÓW
# ============================================================================
# Cel: Bezpieczny przegląd stanu wszystkich serwerów bez ingerencji
# Zbiera informacje o systemie, aplikacjach, zasobach i bezpieczeństwie
# Generuje raport HTML dla łatwego przeglądu
# ============================================================================

- name: "Informacje o przeglądzie serwerów"
  hosts: localhost
  gather_facts: false
  vars:
    report_timestamp: "{{ ansible_date_time.epoch }}"
    report_date: "{{ ansible_date_time.iso8601_basic_short }}"
    report_dir: "/tmp/server_overview_{{ report_date }}"
    
  tasks:
    - name: "Rozpoczęcie przeglądu serwerów"
      debug:
        msg:
          - "=== PRZEGLĄD WSZYSTKICH SERWERÓW ==="
          - "Data: {{ ansible_date_time.iso8601 }}"
          - "Raport zostanie zapisany w: {{ report_dir }}"
          - ""
          - "SERWERY DO PRZEGLĄDU:"
          - "- Nowe (Hetzner): simetria-hetzner001"
          - "- Legacy (OVH): simetria-ovh"
          - "- Legacy (SMS): sms-server"
      tags: [info]

    - name: "Stwórz katalog raportu"
      file:
        path: "{{ report_dir }}"
        state: directory
        mode: '0755'
      delegate_to: localhost
      tags: [setup]

# ============================================================================
# PRZEGLĄD SERWERÓW NOWYCH
# ============================================================================

- name: "Przegląd serwerów nowych (Hetzner)"
  hosts: serwery_nowe
  gather_facts: true
  become: true
  
  vars:
    server_type: "nowy"
    report_timestamp: "{{ hostvars['localhost']['report_timestamp'] }}"
    report_date: "{{ hostvars['localhost']['report_date'] }}"
    report_dir: "{{ hostvars['localhost']['report_dir'] }}"
    
  tasks:
    - name: "=== PRZEGLĄD SERWERA NOWEGO ==="
      debug:
        msg:
          - "Przeglądam serwer: {{ inventory_hostname }}"
          - "Typ: {{ server_type }}"
          - "System: {{ ansible_distribution }} {{ ansible_distribution_version }}"
          - "Architektura: {{ ansible_architecture }}"
      tags: [info]

    # ========================================================================
    # INFORMACJE SYSTEMOWE
    # ========================================================================
    
    - name: "Zbierz informacje systemowe"
      shell: |
        echo "=== INFORMACJE SYSTEMOWE ==="
        echo "Hostname: $(hostname -f)"
        echo "Uptime: $(uptime -p)"
        echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo "Kernel: $(uname -r)"
        echo "Timezone: $(timedatectl | grep 'Time zone' | awk '{print $3}')"
        echo "Last Boot: $(who -b | awk '{print $3, $4}')"
        echo ""
        echo "=== ZASOBY SYSTEMOWE ==="
        echo "CPU Info:"
        lscpu | grep -E "Model name|CPU\(s\)|Thread|Core"
        echo ""
        echo "Pamięć:"
        free -h
        echo ""
        echo "Dyski:"
        df -h | grep -E "^/dev|^tmpfs" | head -10
        echo ""
        echo "=== SIEĆ ==="
        ip route | grep default
        echo "Interfejsy:"
        ip -4 addr show | grep -E "inet " | grep -v "127.0.0.1"
      register: system_info
      tags: [system]

    # ========================================================================
    # BEZPIECZEŃSTWO I AKTUALIZACJE
    # ========================================================================
    
    - name: "Sprawdź bezpieczeństwo i aktualizacje"
      shell: |
        echo "=== BEZPIECZEŃSTWO ==="
        echo "Dostępne aktualizacje bezpieczeństwa:"
        apt list --upgradable 2>/dev/null | grep -i security | wc -l
        echo "Wszystkie dostępne aktualizacje:"
        apt list --upgradable 2>/dev/null | wc -l
        echo ""
        echo "Ostatnia aktualizacja systemu:"
        stat /var/log/apt/history.log | grep Modify | awk '{print $2, $3}'
        echo ""
        echo "=== FIREWALL ==="
        ufw status 2>/dev/null || echo "UFW niedostępny"
        echo ""
        echo "=== USŁUGI SSH ==="
        systemctl is-active ssh
        ss -tlnp | grep :22 || echo "SSH nie nasłuchuje na porcie 22"
        echo ""
        echo "=== NIEUDANE LOGOWANIA ==="
        journalctl -u ssh --since "24 hours ago" | grep "Failed password" | wc -l
      register: security_info
      tags: [security]

    # ========================================================================
    # APLIKACJE I USŁUGI
    # ========================================================================
    
    - name: "Sprawdź aplikacje i usługi"
      shell: |
        echo "=== KLUCZOWE USŁUGI ==="
        for service in ssh nginx docker; do
          echo "$service: $(systemctl is-active $service 2>/dev/null || echo 'INACTIVE')"
        done
        echo ""
        echo "=== DOCKER ==="
        if command -v docker >/dev/null 2>&1; then
          echo "Docker version: $(docker --version)"
          echo "Działające kontenery:"
          docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "Brak kontenerów"
          echo ""
          echo "Obrazy Docker:"
          docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | head -10
          echo ""
          echo "Woluminy Docker:"
          docker volume ls | wc -l
          echo "Sieci Docker:"
          docker network ls | wc -l
        else
          echo "Docker nie jest zainstalowany"
        fi
        echo ""
        echo "=== BASEROW (dla serwerów nowych) ==="
        if [ -d "/opt/baserow" ]; then
          echo "Katalog Baserow istnieje: /opt/baserow"
          cd /opt/baserow && docker-compose ps 2>/dev/null || echo "docker-compose niedostępny"
          echo "Rozmiar katalogu Baserow:"
          du -sh /opt/baserow 2>/dev/null || echo "Nie można sprawdzić rozmiaru"
        else
          echo "Baserow nie jest zainstalowany"
        fi
      register: applications_info
      tags: [applications]

    # ========================================================================
    # MONITORING I LOGI
    # ========================================================================
    
    - name: "Sprawdź monitoring i logi"
      shell: |
        echo "=== MONITORING ==="
        if systemctl is-active prometheus >/dev/null 2>&1; then
          echo "Prometheus: ACTIVE"
        else
          echo "Prometheus: INACTIVE"
        fi
        if systemctl is-active grafana-server >/dev/null 2>&1; then
          echo "Grafana: ACTIVE"
        else
          echo "Grafana: INACTIVE"
        fi
        echo ""
        echo "=== LOGI SYSTEMOWE ==="
        echo "Rozmiar journald:"
        journalctl --disk-usage 2>/dev/null || echo "Nie można sprawdzić"
        echo ""
        echo "Błędy w ostatnich 24h:"
        journalctl --since "24 hours ago" --priority=err | wc -l
        echo "Ostrzeżenia w ostatnich 24h:"
        journalctl --since "24 hours ago" --priority=warning | wc -l
        echo ""
        echo "=== BACKUP ==="
        if [ -d "/opt/baserow/backups" ]; then
          echo "Katalog backup istnieje"
          echo "Ostatni backup:"
          ls -la /opt/baserow/backups/ | tail -5
        else
          echo "Katalog backup nie istnieje"
        fi
      register: monitoring_info
      tags: [monitoring]

    # ========================================================================
    # ZAPIS RAPORTU
    # ========================================================================
    
    - name: "Zapisz raport serwera nowego"
      copy:
        content: |
          # RAPORT SERWERA: {{ inventory_hostname }}
          **Typ:** {{ server_type }}
          **Data:** {{ ansible_date_time.iso8601 }}
          **System:** {{ ansible_distribution }} {{ ansible_distribution_version }}
          
          ## Informacje Systemowe
          ```
          {{ system_info.stdout }}
          ```
          
          ## Bezpieczeństwo
          ```
          {{ security_info.stdout }}
          ```
          
          ## Aplikacje i Usługi
          ```
          {{ applications_info.stdout }}
          ```
          
          ## Monitoring i Logi
          ```
          {{ monitoring_info.stdout }}
          ```
        dest: "{{ report_dir }}/{{ inventory_hostname }}_{{ server_type }}.md"
      delegate_to: localhost
      tags: [report]

  tags: [new_servers]

# ============================================================================
# PRZEGLĄD SERWERÓW LEGACY
# ============================================================================

- name: "Przegląd serwerów legacy"
  hosts: serwery_legacy
  gather_facts: true
  become: true
  
  vars:
    server_type: "legacy"
    report_timestamp: "{{ hostvars['localhost']['report_timestamp'] }}"
    report_date: "{{ hostvars['localhost']['report_date'] }}"
    report_dir: "{{ hostvars['localhost']['report_dir'] }}"
    
  tasks:
    - name: "=== PRZEGLĄD SERWERA LEGACY ==="
      debug:
        msg:
          - "Przeglądam serwer: {{ inventory_hostname }}"
          - "Typ: {{ server_type }}"
          - "System: {{ ansible_distribution }} {{ ansible_distribution_version }}"
      tags: [info]

    # ========================================================================
    # INFORMACJE SYSTEMOWE LEGACY
    # ========================================================================
    
    - name: "Zbierz informacje systemowe (legacy)"
      shell: |
        echo "=== INFORMACJE SYSTEMOWE (LEGACY) ==="
        echo "Hostname: $(hostname -f)"
        echo "Uptime: $(uptime -p)"
        echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo "Kernel: $(uname -r)"
        echo "Last Boot: $(who -b | awk '{print $3, $4}' 2>/dev/null || echo 'Nieznany')"
        echo ""
        echo "=== ZASOBY SYSTEMOWE ==="
        echo "Pamięć:"
        free -h
        echo ""
        echo "Dyski:"
        df -h | head -10
        echo ""
        echo "=== SIEĆ ==="
        ip route | grep default 2>/dev/null || route -n | grep "^0.0.0.0"
      register: legacy_system_info
      ignore_errors: true
      tags: [system]

    # ========================================================================
    # APLIKACJE SPECYFICZNE DLA LEGACY
    # ========================================================================
    
    - name: "Sprawdź aplikacje specyficzne (legacy)"
      shell: |
        echo "=== APLIKACJE LEGACY ==="
        echo "Serwer: {{ inventory_hostname }}"
        
        if [ "{{ inventory_hostname }}" = "simetria-ovh" ]; then
          echo ""
          echo "=== N8N (OVH) ==="
          if [ -d "/home/<USER>/projekty/n8n" ]; then
            echo "Katalog n8n istnieje"
            cd /home/<USER>/projekty/n8n
            docker-compose ps 2>/dev/null || echo "docker-compose niedostępny"
            echo "Wersja n8n:"
            docker exec n8n n8n --version 2>/dev/null || echo "Nie można sprawdzić wersji"
          else
            echo "Katalog n8n nie istnieje"
          fi
          
          echo ""
          echo "=== QDRANT (OVH) ==="
          docker ps | grep qdrant || echo "Qdrant nie działa"
          
        elif [ "{{ inventory_hostname }}" = "sms-server" ]; then
          echo ""
          echo "=== SMS SERVER ==="
          echo "CloudFlare Tunnel:"
          systemctl is-active cloudflared 2>/dev/null || echo "CloudFlare Tunnel nieaktywny"
          echo "Aplikacje SMS:"
          ps aux | grep -i sms | grep -v grep || echo "Brak procesów SMS"
        fi
        
        echo ""
        echo "=== DOCKER (LEGACY) ==="
        if command -v docker >/dev/null 2>&1; then
          echo "Docker version: $(docker --version)"
          echo "Działające kontenery:"
          docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Brak kontenerów"
        else
          echo "Docker nie jest zainstalowany"
        fi
      register: legacy_applications_info
      ignore_errors: true
      tags: [applications]

    # ========================================================================
    # BEZPIECZEŃSTWO LEGACY
    # ========================================================================
    
    - name: "Sprawdź bezpieczeństwo (legacy)"
      shell: |
        echo "=== BEZPIECZEŃSTWO (LEGACY) ==="
        echo "Dostępne aktualizacje:"
        apt list --upgradable 2>/dev/null | wc -l || echo "Nie można sprawdzić"
        echo ""
        echo "=== SSH ==="
        echo "Port SSH: {{ ansible_port | default(22) }}"
        systemctl is-active ssh 2>/dev/null || echo "SSH status nieznany"
        echo ""
        echo "=== FIREWALL ==="
        ufw status 2>/dev/null || iptables -L | head -5 || echo "Firewall status nieznany"
        echo ""
        echo "=== LOGI ==="
        echo "Błędy w ostatnich 24h:"
        journalctl --since "24 hours ago" --priority=err 2>/dev/null | wc -l || echo "Nie można sprawdzić"
      register: legacy_security_info
      ignore_errors: true
      tags: [security]

    # ========================================================================
    # ZAPIS RAPORTU LEGACY
    # ========================================================================
    
    - name: "Zapisz raport serwera legacy"
      copy:
        content: |
          # RAPORT SERWERA: {{ inventory_hostname }}
          **Typ:** {{ server_type }}
          **Data:** {{ ansible_date_time.iso8601 }}
          **System:** {{ ansible_distribution }} {{ ansible_distribution_version }}
          **Port SSH:** {{ ansible_port | default(22) }}
          
          ## Informacje Systemowe
          ```
          {{ legacy_system_info.stdout }}
          ```
          
          ## Aplikacje Specyficzne
          ```
          {{ legacy_applications_info.stdout }}
          ```
          
          ## Bezpieczeństwo
          ```
          {{ legacy_security_info.stdout }}
          ```
        dest: "{{ report_dir }}/{{ inventory_hostname }}_{{ server_type }}.md"
      delegate_to: localhost
      tags: [report]

  tags: [legacy_servers]

# ============================================================================
# GENEROWANIE RAPORTU ZBIORCZEGO
# ============================================================================

- name: "Generowanie raportu zbiorczego"
  hosts: localhost
  gather_facts: false
  vars:
    report_timestamp: "{{ hostvars['localhost']['report_timestamp'] }}"
    report_date: "{{ hostvars['localhost']['report_date'] }}"
    report_dir: "{{ hostvars['localhost']['report_dir'] }}"
    
  tasks:
    - name: "Generuj raport zbiorczy HTML"
      copy:
        content: |
          <!DOCTYPE html>
          <html>
          <head>
              <title>Przegląd Serwerów - {{ report_date }}</title>
              <meta charset="UTF-8">
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
                  .server { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                  .new-server { border-left: 5px solid #28a745; }
                  .legacy-server { border-left: 5px solid #ffc107; }
                  .status-ok { color: #28a745; }
                  .status-warning { color: #ffc107; }
                  .status-error { color: #dc3545; }
                  pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
              </style>
          </head>
          <body>
              <div class="header">
                  <h1>🖥️ Przegląd Wszystkich Serwerów</h1>
                  <p><strong>Data generowania:</strong> {{ ansible_date_time.iso8601 }}</p>
                  <p><strong>Timestamp:</strong> {{ report_timestamp }}</p>
              </div>
              
              <h2>📊 Podsumowanie</h2>
              <ul>
                  <li><span class="status-ok">✅ Serwery nowe:</span> 1 (simetria-hetzner001)</li>
                  <li><span class="status-warning">⚠️ Serwery legacy:</span> 2 (simetria-ovh, sms-server)</li>
              </ul>
              
              <h2>📁 Szczegółowe Raporty</h2>
              <p>Szczegółowe raporty dla każdego serwera znajdują się w plikach:</p>
              <ul>
                  <li><code>simetria-hetzner001_nowy.md</code></li>
                  <li><code>simetria-ovh_legacy.md</code></li>
                  <li><code>sms-server_legacy.md</code></li>
              </ul>
              
              <h2>🔧 Następne Kroki</h2>
              <ol>
                  <li>Przejrzyj szczegółowe raporty każdego serwera</li>
                  <li>Sprawdź dostępne aktualizacje bezpieczeństwa</li>
                  <li>Wykonaj health check: <code>ansible-playbook health_check.yml</code></li>
                  <li>W razie problemów uruchom diagnostykę</li>
              </ol>
              
              <hr>
              <p><small>Raport wygenerowany automatycznie przez Ansible</small></p>
          </body>
          </html>
        dest: "{{ report_dir }}/server_overview_report.html"
      tags: [report]

    - name: "=== PODSUMOWANIE PRZEGLĄDU ==="
      debug:
        msg:
          - "Przegląd wszystkich serwerów zakończony"
          - ""
          - "RAPORTY WYGENEROWANE:"
          - "📁 Katalog: {{ report_dir }}"
          - "📄 Raport HTML: server_overview_report.html"
          - "📄 Raporty szczegółowe: *_nowy.md, *_legacy.md"
          - ""
          - "NASTĘPNE KROKI:"
          - "1. Otwórz raport HTML w przeglądarce"
          - "2. Przejrzyj szczegółowe raporty"
          - "3. Wykonaj health check jeśli potrzeba"
          - ""
          - "KOMENDY:"
          - "open {{ report_dir }}/server_overview_report.html"
          - "ansible-playbook health_check.yml"
      tags: [summary]
