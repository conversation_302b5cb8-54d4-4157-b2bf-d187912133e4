# 🤖 PRZEWODNIK DLA AGENTÓW AI - ZARZĄDZANIE SERWERAMI ANSIBLE

## 🎯 KONTEKST SYSTEMU

### 📊 INFRASTRUKTURA
```yaml
serwery_nowe:
  - simetria-hetzner001: 
      ip: ***********
      ssh_port: 22
      aplikacje: [baserow, monitoring]
      status: nowoczesny
      automatyzacja: pełna

serwery_legacy:
  - simetria-ovh:
      ip: **************
      ssh_port: 1988
      aplikacje: [n8n, qdrant]
      status: legacy
      automatyzacja: ograniczona
  - sms-server:
      ip: ssh-hp.simetria.cc
      ssh_port: 22
      aplikacje: [sms, cloudflare_tunnel]
      status: legacy
      automatyzacja: ograniczona
```

### 🔧 KLUCZOWE RÓŻNICE
- **Serwery nowe:** Pełna automatyzacja, standardowe konfiguracje
- **Serwery legacy:** Ręczna konfiguracja, niestandardowe porty, różne aplikacje

## 🚀 DOSTĘPNE PLAYBOOKI

### 1️⃣ `update_all_servers.yml`
**Cel:** Aktualizacja systemu na wszystkich serwerach
**Bezpieczeństwo:** ✅ Bezpieczny - tylko aktualizacje pakietów
**Parametry:**
```yaml
update_mode: "safe" | "full"  # domyślnie: safe
update_serial: 1-N           # domyślnie: 1 (po kolei)
```

### 2️⃣ `server_overview.yml`
**Cel:** Przegląd stanu serwerów bez ingerencji
**Bezpieczeństwo:** ✅ Całkowicie bezpieczny - tylko odczyt
**Wyjście:** Raporty HTML + Markdown

### 3️⃣ `health_check.yml`
**Cel:** Analiza zdrowia serwerów i aplikacji
**Bezpieczeństwo:** ✅ Bezpieczny - tylko diagnostyka
**Parametry:**
```yaml
ai_analysis: true | false    # domyślnie: false
```

## 🤖 INSTRUKCJE DLA AI AGENTÓW

### ✅ **DOZWOLONE DZIAŁANIA**

1. **Uruchamianie playbooków diagnostycznych:**
   ```bash
   ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass
   ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass
   ```

2. **Sprawdzanie stanu serwerów:**
   ```bash
   ansible all -i inventory -m ping --vault-password-file .vault_pass
   ansible all -i inventory -m shell -a "uptime" --vault-password-file .vault_pass
   ```

3. **Analiza raportów:**
   - Odczyt plików z `/tmp/server_overview_*/`
   - Odczyt plików z `/tmp/health_check_*/`
   - Interpretacja statusów zdrowia

### ⚠️ **WYMAGANA ZGODA UŻYTKOWNIKA**

1. **Aktualizacje systemu:**
   ```bash
   ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass
   ```

2. **Zmiany konfiguracji:**
   - Edycja plików konfiguracyjnych
   - Restart usług
   - Instalacja pakietów

3. **Operacje na serwerach legacy:**
   - Wszelkie zmiany na `simetria-ovh` i `sms-server`
   - Restart kontenerów Docker
   - Modyfikacja aplikacji

### 🚫 **ZABRONIONE DZIAŁANIA**

1. **Restart serwerów:**
   ```bash
   # NIGDY nie wykonuj
   ansible all -m reboot
   ```

2. **Usuwanie danych:**
   ```bash
   # NIGDY nie wykonuj
   ansible all -m file -a "path=/path state=absent"
   ```

3. **Zmiany bez zgody:**
   - Modyfikacja plików systemowych
   - Instalacja nowego oprogramowania
   - Zmiany w firewall

## 📋 PROTOKOŁY DZIAŁANIA

### 🔍 **PROTOKÓŁ DIAGNOSTYKI**

1. **Sprawdź połączenie:**
   ```bash
   ansible all -i inventory -m ping --vault-password-file .vault_pass
   ```

2. **Wykonaj przegląd:**
   ```bash
   ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass
   ```

3. **Sprawdź zdrowie:**
   ```bash
   ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass
   ```

4. **Przeanalizuj raporty:**
   - Otwórz pliki HTML
   - Sprawdź statusy (🟢🟡🔴)
   - Zidentyfikuj problemy

### 🔄 **PROTOKÓŁ AKTUALIZACJI**

1. **Sprawdź stan przed:**
   ```bash
   ansible-playbook server_overview.yml -i inventory --vault-password-file .vault_pass
   ```

2. **Poproś o zgodę użytkownika**

3. **Wykonaj aktualizację:**
   ```bash
   ansible-playbook update_all_servers.yml -i inventory --vault-password-file .vault_pass
   ```

4. **Sprawdź stan po:**
   ```bash
   ansible-playbook health_check.yml -i inventory --vault-password-file .vault_pass
   ```

## 📊 INTERPRETACJA WYNIKÓW

### 🟢 **STATUS ZDROWY**
```yaml
oznaki:
  - "✅" w raportach
  - "ACTIVE" dla usług
  - "Up" dla kontenerów
  - Brak błędów w logach
działanie: Monitoring rutynowy
```

### 🟡 **STATUS OSTRZEŻENIA**
```yaml
oznaki:
  - "⚠️" w raportach
  - Wysokie zużycie zasobów (>80%)
  - Ostrzeżenia w logach
  - Dostępne aktualizacje
działanie: Zaplanuj naprawę, poinformuj użytkownika
```

### 🔴 **STATUS KRYTYCZNY**
```yaml
oznaki:
  - "🔴" w raportach
  - "FAILED" lub "INACTIVE" dla usług
  - "Exited" dla kontenerów
  - Błędy w logach
działanie: Natychmiastowa interwencja, poinformuj użytkownika
```

## 🛠️ ROZWIĄZYWANIE PROBLEMÓW

### ❌ **BŁĄD POŁĄCZENIA SSH**
```yaml
problem: "UNREACHABLE" lub "FAILED"
rozwiązanie:
  1. Sprawdź czy serwer jest dostępny
  2. Sprawdź port SSH (22 vs 1988 dla OVH)
  3. Sprawdź klucze SSH
  4. Poinformuj użytkownika
```

### ❌ **BŁĄD VAULT**
```yaml
problem: "vault password" lub "decrypt"
rozwiązanie:
  1. Sprawdź czy plik .vault_pass istnieje
  2. Sprawdź uprawnienia pliku
  3. Poinformuj użytkownika o problemie z hasłem
```

### ❌ **APLIKACJA NIE DZIAŁA**
```yaml
problem: Kontener "Exited" lub usługa "INACTIVE"
rozwiązanie:
  1. Sprawdź logi aplikacji
  2. Sprawdź zasoby systemowe
  3. Zaproponuj restart (po zgodzie użytkownika)
  4. Zaproponuj diagnostykę szczegółową
```

## 🤖 WZORCE KOMUNIKACJI

### 📢 **RAPORTOWANIE STANU**
```markdown
## 📊 STATUS SERWERÓW

### 🟢 Serwery Zdrowe
- simetria-hetzner001: Wszystkie usługi działają

### 🟡 Serwery z Ostrzeżeniami  
- simetria-ovh: Wysokie zużycie dysku (85%)

### 🔴 Serwery Krytyczne
- Brak

### 🔧 Rekomendacje
1. Sprawdź miejsce na dysku na simetria-ovh
2. Rozważ czyszczenie logów
```

### 🚨 **ZGŁASZANIE PROBLEMÓW**
```markdown
## 🚨 WYKRYTY PROBLEM

**Serwer:** simetria-ovh
**Problem:** N8N nie odpowiada
**Priorytet:** 🔴 Krytyczny

**Szczegóły:**
- Kontener n8n w stanie "Exited"
- Błędy w logach: "Database connection failed"

**Rekomendowane działania:**
1. Sprawdź status PostgreSQL
2. Restart kontenerów n8n
3. Sprawdź logi szczegółowe

**Czy mogę wykonać restart kontenerów?**
```

### 💡 **PROPONOWANIE ROZWIĄZAŃ**
```markdown
## 💡 PROPOZYCJA AKTUALIZACJI

**Wykryte:** 15 dostępnych aktualizacji bezpieczeństwa

**Serwery do aktualizacji:**
- simetria-hetzner001: 8 aktualizacji
- simetria-ovh: 7 aktualizacji

**Szacowany czas:** 10-15 minut
**Ryzyko:** Niskie (tylko aktualizacje bezpieczeństwa)

**Czy mogę wykonać aktualizację?**
```

## 🔄 AUTOMATYZACJA AI

### 🤖 **RUTYNOWE ZADANIA**
```yaml
codziennie:
  - Sprawdź status serwerów (server_overview.yml)
  - Sprawdź zdrowie aplikacji (health_check.yml)
  - Raportuj problemy użytkownikowi

tygodniowo:
  - Sprawdź dostępne aktualizacje
  - Zaproponuj aktualizacje bezpieczeństwa
  - Przeanalizuj trendy wydajności

miesięcznie:
  - Pełny przegląd konfiguracji
  - Optymalizacja zasobów
  - Planowanie konserwacji
```

### 🎯 **PRIORYTETY DZIAŁAŃ**
```yaml
priorytet_1_natychmiastowy:
  - Serwery niedostępne
  - Aplikacje nie działają
  - Błędy krytyczne w logach

priorytet_2_pilny:
  - Wysokie zużycie zasobów (>90%)
  - Aktualizacje bezpieczeństwa
  - Ostrzeżenia w logach

priorytet_3_planowany:
  - Optymalizacja wydajności
  - Aktualizacje funkcjonalne
  - Czyszczenie logów
```

## 📝 DOKUMENTACJA ZMIAN

### 📋 **SZABLON RAPORTU**
```markdown
# RAPORT DZIAŁAŃ AI - {{ data }}

## 🔍 Wykonane Diagnostyki
- [ ] server_overview.yml
- [ ] health_check.yml
- [ ] Analiza raportów

## 🚨 Wykryte Problemy
1. Problem 1: Opis
2. Problem 2: Opis

## 🔧 Wykonane Działania
1. Działanie 1: Wynik
2. Działanie 2: Wynik

## 💡 Rekomendacje
1. Rekomendacja 1
2. Rekomendacja 2

## 📊 Podsumowanie
- Serwery zdrowe: X
- Serwery z ostrzeżeniami: Y
- Serwery krytyczne: Z
```

---

**🤖 Przewodnik dla AI Agentów v1.0**  
**📅 Ostatnia aktualizacja:** 2025-01-09  
**🔒 Poziom dostępu:** Diagnostyka + Aktualizacje (za zgodą)
