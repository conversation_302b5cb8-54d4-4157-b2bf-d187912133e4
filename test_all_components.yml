---
# Test wszystkich komponentów systemu Baserow
# Uruchamia wszystkie testy w od<PERSON><PERSON><PERSON><PERSON><PERSON> kolejn<PERSON>ci

- name: "Informacje o testach"
  hosts: localhost
  gather_facts: false
  tasks:
    - name: "Rozpoczęcie testów wszystkich komponentów"
      debug:
        msg:
          - "=== TESTY WSZYSTKICH KOMPONENTÓW BASEROW ==="
          - "Cel: {{ target_host | default('serwery_nowe') }}"
          - "Data: {{ ansible_date_time.iso8601 }}"
          - ""
          - "Kolejność testów:"
          - "1. Test dostępności serwera"
          - "2. Test Docker"
          - "3. Test Nginx"
          - "4. Test Baserow"
      tags: [info]

# Test 1: Dostę<PERSON>ność serwera
- import_playbook: test_server_reachability.yml
  vars:
    target_host: "{{ target_host | default('serwery_nowe') }}"
  tags: [server, connectivity]

# Test 2: Docker
- import_playbook: test_docker.yml
  vars:
    target_host: "{{ target_host | default('serwery_nowe') }}"
  tags: [docker, containers]

# Test 3: Nginx
- import_playbook: test_nginx.yml
  vars:
    target_host: "{{ target_host | default('serwery_nowe') }}"
  tags: [nginx, web]

# Test 4: Baserow
- import_playbook: test_baserow.yml
  vars:
    target_host: "{{ target_host | default('serwery_nowe') }}"
  tags: [baserow, application]

# Podsumowanie końcowe
- name: "Podsumowanie testów"
  hosts: localhost
  gather_facts: false
  tasks:
    - name: "Zakończenie testów wszystkich komponentów"
      debug:
        msg:
          - "=== TESTY ZAKOŃCZONE ==="
          - ""
          - "✅ Wszystkie testy zostały wykonane"
          - ""
          - "📋 WYKONANE TESTY:"
          - "  1. Test dostępności serwera"
          - "  2. Test Docker"
          - "  3. Test Nginx"
          - "  4. Test Baserow"
          - ""
          - "📝 INSTRUKCJE URUCHAMIANIA:"
          - "  Wszystkie testy: ansible-playbook test_all_components.yml -e target_host=simetria-hetzner001"
          - "  Tylko serwer: ansible-playbook test_server_reachability.yml -e target_host=simetria-hetzner001"
          - "  Tylko Docker: ansible-playbook test_docker.yml -e target_host=simetria-hetzner001"
          - "  Tylko Nginx: ansible-playbook test_nginx.yml -e target_host=simetria-hetzner001"
          - "  Tylko Baserow: ansible-playbook test_baserow.yml -e target_host=simetria-hetzner001"
          - ""
          - "🏷️ UŻYCIE TAGÓW:"
          - "  Tylko testy połączenia: --tags connectivity"
          - "  Tylko testy zdrowia: --tags health"
          - "  Tylko informacje: --tags info"
          - "  Pomiń testy SSL: --skip-tags ssl"
      tags: [info, summary]
