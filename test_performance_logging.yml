---
- name: Test logging performance
  hosts: all
  become: yes
  vars:
    test_log_entries: 1000
    test_log_dir: /tmp/logging_performance_test
    
  tasks:
    - name: Create test logging directory
      file:
        path: "{{ test_log_dir }}"
        state: directory
        mode: '0755'

    - name: Generate test log data
      shell: |
        echo "📊 Generating {{ test_log_entries }} test log entries..."
        
        # Create test log file with sample entries
        for i in $(seq 1 {{ test_log_entries }}); do
          timestamp=$(date '+%Y-%m-%d %H:%M:%S')
          level=$([ $((i % 4)) -eq 0 ] && echo "ERROR" || [ $((i % 3)) -eq 0 ] && echo "WARN" || [ $((i % 2)) -eq 0 ] && echo "INFO" || echo "DEBUG")
          message="Test log entry $i"
          echo "$timestamp [$level] $message" >> {{ test_log_dir }}/test_app.log
        done
        
        echo "✅ Test log data generated successfully"
        echo "Total log entries: $(wc -l < {{ test_log_dir }}/test_app.log)"
        echo "File size: $(du -h {{ test_log_dir }}/test_app.log | cut -f1)"
      register: log_generation

    - name: Test log parsing performance
      shell: |
        echo "🔍 Testing log parsing performance..."
        start_time=$(date +%s)
        
        # Count ERROR entries
        error_count=$(grep -c "ERROR" {{ test_log_dir }}/test_app.log || echo 0)
        
        # Count WARN entries
        warn_count=$(grep -c "WARN" {{ test_log_dir }}/test_app.log || echo 0)
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        echo "📊 Log parsing completed in ${duration}s"
        echo "❌ ERROR entries: $error_count"
        echo "⚠️  WARN entries: $warn_count"
        
        echo "parse_duration=$duration" > /tmp/logging_stats.txt
        echo "error_count=$error_count" >> /tmp/logging_stats.txt
        echo "warn_count=$warn_count" >> /tmp/logging_stats.txt
      register: log_parsing

    - name: Test log compression performance
      shell: |
        echo "📦 Testing log compression performance..."
        start_time=$(date +%s)
        
        # Compress log file
        gzip {{ test_log_dir }}/test_app.log
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        # Calculate compression ratio
        original_size=$(du -h {{ test_log_dir }}/test_app.log 2>/dev/null | cut -f1 || echo "0")
        compressed_size=$(du -h {{ test_log_dir }}/test_app.log.gz | cut -f1)
        
        echo "📦 Compression completed in ${duration}s"
        echo "📊 Original size: $original_size"
        echo "📊 Compressed size: $compressed_size"
        
        echo "compression_duration=$duration" >> /tmp/logging_stats.txt
        echo "original_size=$original_size" >> /tmp/logging_stats.txt
        echo "compressed_size=$compressed_size" >> /tmp/logging_stats.txt
      register: log_compression

    - name: Test log search performance
      shell: |
        echo "🔎 Testing log search performance..."
        start_time=$(date +%s)
        
        # Search for specific patterns
        error_lines=$(zgrep "ERROR" {{ test_log_dir }}/test_app.log.gz | wc -l)
        debug_lines=$(zgrep "DEBUG" {{ test_log_dir }}/test_app.log.gz | wc -l)
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        echo "🔎 Search completed in ${duration}s"
        echo "❌ ERROR lines found: $error_lines"
        echo "🐛 DEBUG lines found: $debug_lines"
        
        echo "search_duration=$duration" >> /tmp/logging_stats.txt
        echo "error_lines=$error_lines" >> /tmp/logging_stats.txt
        echo "debug_lines=$debug_lines" >> /tmp/logging_stats.txt
      register: log_search

    - name: Load logging performance statistics
      shell: cat /tmp/logging_stats.txt
      register: logging_stats

    - name: Display final logging performance summary
      debug:
        msg:
          - "🚀 LOGGING PERFORMANCE TEST RESULTS:"
          - "📊 Test log entries: {{ test_log_entries }}"
          - "⏱️  Log parsing: {{ logging_stats.stdout_lines[0] | default('parse_duration=0') | regex_replace('parse_duration=', '') }}s"
          - "⏱️  Log compression: {{ logging_stats.stdout_lines[3] | default('compression_duration=0') | regex_replace('compression_duration=', '') }}s"
          - "⏱️  Log search: {{ logging_stats.stdout_lines[6] | default('search_duration=0') | regex_replace('search_duration=', '') }}s"
          - "❌ Total ERROR entries: {{ logging_stats.stdout_lines[1] | default('error_count=0') | regex_replace('error_count=', '') }}"
          - "📦 Compression: {{ logging_stats.stdout_lines[4] | default('original_size=0') | regex_replace('original_size=', '') }} → {{ logging_stats.stdout_lines[5] | default('compressed_size=0') | regex_replace('compressed_size=', '') }}"
          - "✅ All logging performance tests: COMPLETED SUCCESSFULLY"

    - name: Cleanup test files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - "{{ test_log_dir }}"
        - /tmp/logging_stats.txt
      ignore_errors: yes