---
- name: Diag<PERSON>za serwera OVH - poszukiwanie danych Baserow
  hosts: serwery_legacy
  become: true
  tasks:
    - name: <PERSON>praw<PERSON><PERSON> kontenery Docker
      ansible.builtin.shell: |
        docker ps -a
      register: docker_containers
      ignore_errors: true
      tags: [docker, containers]

    - name: <PERSON>prawdź woluminy Docker
      ansible.builtin.shell: |
        docker volume ls
      register: docker_volumes
      ignore_errors: true
      tags: [docker, volumes]

    - name: S<PERSON>j kontenerów z PostgreSQL
      ansible.builtin.shell: |
        docker ps -a | grep -i postgres
      register: postgres_containers
      ignore_errors: true
      tags: [postgres, search]

    - name: Szukaj kontenerów z Redis
      ansible.builtin.shell: |
        docker ps -a | grep -i redis
      register: redis_containers
      ignore_errors: true
      tags: [redis, search]

    - name: Szukaj kontenerów z Baserow
      ansible.builtin.shell: |
        docker ps -a | grep -i baserow
      register: baserow_containers
      ignore_errors: true
      tags: [baserow, search]

    - name: <PERSON>praw<PERSON><PERSON> katalogi potencjalnie zawierające dane <PERSON>row
      ansible.builtin.shell: |
        find /home /opt /var -maxdepth 3 -name "*baserow*" -type d 2>/dev/null
      register: baserow_directories
      ignore_errors: true
      tags: [filesystem, search]

    - name: Sprawdź pliki docker-compose
      ansible.builtin.shell: |
        find /home /opt /var /root -name "docker-compose.yml" -o -name "docker-compose.yaml" 2>/dev/null
      register: compose_files
      ignore_errors: true
      tags: [docker-compose, search]

    - name: Sprawdź stan usługi Docker
      ansible.builtin.shell: |
        systemctl status docker --no-pager -l
      register: docker_status
      ignore_errors: true
      tags: [docker, status]

    - name: Wyświetl wyniki diagnozy OVH
      ansible.builtin.debug:
        msg:
          - "=== KONTENERY DOCKER ===="
          - "{{ docker_containers.stdout_lines | default(['Brak kontenerów lub błąd Docker']) }}"
          - "=== WOLUMINY DOCKER ===="
          - "{{ docker_volumes.stdout_lines | default(['Brak woluminów']) }}"
          - "=== KONTENERY POSTGRES ===="
          - "{{ postgres_containers.stdout_lines | default(['Brak kontenerów PostgreSQL']) }}"
          - "=== KONTENERY REDIS ===="
          - "{{ redis_containers.stdout_lines | default(['Brak kontenerów Redis']) }}"
          - "=== KONTENERY BASEROW ===="
          - "{{ baserow_containers.stdout_lines | default(['Brak kontenerów Baserow']) }}"
          - "=== KATALOGI BASEROW ===="
          - "{{ baserow_directories.stdout_lines | default(['Brak katalogów Baserow']) }}"
          - "=== PLIKI DOCKER-COMPOSE ===="
          - "{{ compose_files.stdout_lines | default(['Brak plików docker-compose']) }}"
          - "=== STATUS DOCKER ===="
          - "{{ docker_status.stdout_lines[-10:] | default(['Błąd sprawdzania statusu Docker']) }}"
      tags: [summary, debug]