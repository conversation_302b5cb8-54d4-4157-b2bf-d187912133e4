To export the databases and applications in a workspace:

Run docker exec baserow ./baserow.sh backend-cmd manage export_workspace_applications WORKSPACE_ID_THAT_MUST_BE_EXPORTED to make the export.
Use docker cp baserow:/baserow/backend/workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.json workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.json ./workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.json to copy the database user files to the host.
Use docker cp baserow:/baserow/backend/workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.zip workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.json ./workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.zip to copy the user files to the host.
To import the exported database and applications in another instance.

Use docker cp workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.json baserow:/baserow/backend/workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.zip to copy the JSON file into your container.
Use docker cp workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.json baserow:/baserow/backend/workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED.zip to copy the zip file into your container.
Run docker exec baserow ./baserow.sh backend-cmd manage import_workspace_applications WORKSPACE_ID_WHERE_THE_APPLICATIONS_MUST_BE_IMPORTED workspace_WORKSPACE_ID_THAT_MUST_BE_EXPORTED to import the data.