---
type: "always_apply"
---

**Struktura projektu Ansible:**

Projekt Ansible ma następującą strukturę:

*   `ansible.cfg`: Konfiguracja Ansible.
*   `inventory`: Plik inwentarza, definiujący hosty.
*   `inventory_production`: Plik inwentarza dla środowiska produkcyjnego.
*   `site.yml`: Główny playbook, uruchamiający role.
*   `group_vars/`: Zmienne dla grup hostów.
    *   `all/`: Zmienne dla wszystkich hostów.
        *   `main.yml`: Główne zmienne.
        *   `vault.yml`: <PERSON>aszyfrowane zmienne.
*   `roles/`: Role Ansible.
    *   `baserow_deployment/`: Rola do wdrażania Baserow.
    *   `logging_agent/`: Rola do konfiguracji agenta logowania.
    *   `logging_central/`: Rola do konfiguracji centralnego logowania.
    *   `security_hardening/`: Rola do wzmacniania bezpieczeństwa.
*   `tests/`: Testy.
    *   `run_integration_tests.sh`: Skrypt do uruchamiania testów integracyjnych.

**Użycie hasła z pliku `.vault_pass`:**

Do odszyfrowania zaszyfrowanych zmiennych w `group_vars/all/vault.yml` należy użyć hasła przechowywanego w pliku `.vault_pass`. Można to zrobić, używając opcji `--vault-password-file .vault_pass` w poleceniach Ansible, np.:

```bash
ansible-playbook site.yml --vault-password-file .vault_pass
```

**Instrukcje dla agenta LLM:**

1.  **Refaktoryzacja playbooków Ansible:**
    *   Analizuj playbooki pod kątem redundancji, niejasności i potencjalnych problemów.
    *   Wykorzystuj role Ansible do modularyzacji i ponownego użycia kodu.
    *   Stosuj zmienne i szablony Jinja2 do parametryzacji konfiguracji.
    *   Przestrzegaj najlepszych praktyk Ansible, takich jak idempotencja i deklaratywność.
    *   Dokumentuj zmiany i uzasadniaj decyzje.
2.  **Testowanie playbooków Ansible:**
    *   Używaj narzędzi takich jak `ansible-lint` do sprawdzania składni i stylu playbooków.
    *   Wykorzystuj testy jednostkowe i integracyjne do weryfikacji poprawności działania ról.
    *   Stosuj testy idempotencji, aby upewnić się, że playbooki nie powodują niepożądanych zmian przy ponownym uruchomieniu.
    *   Monitoruj logi i metryki podczas testów, aby zidentyfikować potencjalne problemy.
3.  **Rozwiązywanie problemów z konfiguracją Baserow:**
    *   Sprawdzaj logi Baserow i Docker, aby zidentyfikować źródło problemu.
    *   Weryfikuj konfigurację Baserow w plikach `docker-compose.yml` i zmiennych środowiskowych.
    *   Upewnij się, że porty są prawidłowo skonfigurowane i dostępne.
    *   Sprawdzaj połączenie z bazą danych PostgreSQL.
    *   Weryfikuj konfigurację Nginx i certyfikatów SSL.
    *   Wykorzystuj narzędzia diagnostyczne, takie jak `docker exec` do uruchamiania poleceń w kontenerach Baserow.
    *   Konsultuj dokumentację Baserow i fora dyskusyjne.
4.  **Ogólne wskazówki:**
    *   Zawsze używaj opcji `--vault-password-file .vault_pass` do odszyfrowywania zaszyfrowanych zmiennych.
    *   Przed wprowadzeniem zmian w środowisku produkcyjnym, testuj je w środowisku testowym.
    *   Wykonuj kopie zapasowe przed wprowadzeniem większych zmian.
    *   Dokumentuj wszystkie zmiany i procedury.

```mermaid
graph LR
A[Start] --> B{Refaktoryzacja Playbooków};
B -- Tak --> C[Analiza Playbooków];
C --> D[Modularyzacja (Role)];
D --> E[Parametryzacja (Zmienne, Szablony)];
E --> F[Najlepsze Praktyki (Idempotencja)];
F --> G[Dokumentacja];
G --> H[Koniec Refaktoryzacji];
B -- Nie --> I{Testowanie Playbooków};
I -- Tak --> J[ansible-lint];
J --> K[Testy Jednostkowe/Integracyjne];
K --> L[Testy Idempotencji];
L --> M[Monitoring Logów/Metryk];
M --> N[Koniec Testowania];
I -- Nie --> O{Rozwiązywanie Problemów Baserow};
O -- Tak --> P[Sprawdzanie Logów (Baserow, Docker)];
P --> Q[Weryfikacja Konfiguracji (docker-compose.yml)];
Q --> R[Sprawdzanie Portów];
R --> S[Połączenie z Bazą Danych];
S --> T[Konfiguracja Nginx/SSL];
T --> U[Narzędzia Diagnostyczne (docker exec)];
U --> V[Dokumentacja/Fora];
V --> W[Koniec Rozwiązywania Problemów];
O -- Nie --> X[Koniec];
H --> X;
N --> X;
W --> X;