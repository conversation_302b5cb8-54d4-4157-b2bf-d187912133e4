---
- name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nowej konfiguracji n8n
  hosts: simetria-ovh
  become: true
  gather_facts: true
  
  vars:
    n8n_new_version: "1.103.2"  # Najnowsza stabilna wersja
    n8n_config_dir: "/home/<USER>/projekty/n8n"
    backup_timestamp: "{{ ansible_date_time.epoch }}"
    
  tasks:
    - name: "=== PRZYGOTOWANIE AKTUALIZACJI N8N ==="
      debug:
        msg:
          - "Przygotowuję aktualizację n8n na serwerze {{ ansible_hostname }}"
          - "Obecna wersja: latest (1.98.2)"
          - "Docelowa wersja: {{ n8n_new_version }}"
          - "Data: {{ ansible_date_time.iso8601 }}"

    # ==========================================================================
    # BACKUP OBECNEJ KONFIGURACJI
    # ==========================================================================
    
    - name: "Backup obecnego docker-compose.yml"
      copy:
        src: "{{ n8n_config_dir }}/docker-compose.yml"
        dest: "{{ n8n_config_dir }}/docker-compose.yml.backup.{{ backup_timestamp }}"
        remote_src: true
        owner: szymcio
        group: szymcio
        mode: '0644'

    - name: "Potwierdzenie backup"
      debug:
        msg: "✓ Backup utworzony: docker-compose.yml.backup.{{ backup_timestamp }}"

    # ==========================================================================
    # PRZYGOTOWANIE NOWEJ KONFIGURACJI
    # ==========================================================================
    
    - name: "Stwórz nowy docker-compose.yml z aktualizacją"
      copy:
        dest: "{{ n8n_config_dir }}/docker-compose.yml.new"
        owner: szymcio
        group: szymcio
        mode: '0644'
        content: |
          version: '3.8'

          services:
            postgres:
              image: postgres:15
              container_name: n8n_postgres
              restart: unless-stopped
              environment:
                POSTGRES_DB: n8n_db
                POSTGRES_USER: n8n_user
                POSTGRES_PASSWORD: super_tajne_haslo
              volumes:
                - n8n_postgres_data:/var/lib/postgresql/data
              deploy:
                resources:
                  limits:
                    memory: 512M
              ports:
                - "127.0.0.1:5433:5432"  # Bind tylko do localhost
              healthcheck:
                test: ["CMD-SHELL", "pg_isready -U n8n_user -d n8n_db"]
                interval: 30s
                timeout: 10s
                retries: 3

            n8n:
              image: n8nio/n8n:{{ n8n_new_version }}
              container_name: n8n
              restart: unless-stopped
              depends_on:
                postgres:
                  condition: service_healthy
              environment:
                - N8N_HOST=n8n.simetria.pl
                - N8N_PORT=5678
                - N8N_PROTOCOL=https
                - N8N_SSL_PORT=443
                - WEBHOOK_URL=https://n8n.simetria.pl
                - N8N_ALLOW_EXTERNAL_ACCESS=true
                - N8N_SECURE_COOKIE=true
                - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
                - N8N_RUNNERS_ENABLED=true
                - DB_TYPE=postgresdb
                - DB_POSTGRESDB_HOST=postgres
                - DB_POSTGRESDB_PORT=5432
                - DB_POSTGRESDB_DATABASE=n8n_db
                - DB_POSTGRESDB_USER=n8n_user
                - DB_POSTGRESDB_PASSWORD=super_tajne_haslo
                - TZ=Europe/Warsaw
                # === RETENCJA WYKONAŃ - KONTROLA ROZMIARU BAZY ===
                - N8N_EXECUTIONS_DATA_PRUNE=true
                - N8N_EXECUTIONS_DATA_MAX_AGE=336  # 14 dni w godzinach (14 * 24)
                - N8N_EXECUTIONS_DATA_PRUNE_MAX_COUNT=3000  # maksymalnie 3000 wykonań
                - N8N_EXECUTIONS_DATA_PRUNE_HARD_DELETE_INTERVAL=1800  # czyść co 30 min
                - N8N_LOG_LEVEL=info  # zmniejsz verbose logging
                # === NOWE USTAWIENIA DLA WERSJI 1.103+ ===
                - N8N_METRICS=true  # Włącz metryki
                - N8N_DIAGNOSTICS_ENABLED=false  # Wyłącz diagnostykę
              ports:
                - "127.0.0.1:5678:5678"  # Bind tylko do localhost
              volumes:
                - n8n_data:/home/<USER>/.n8n
              healthcheck:
                test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/healthz"]
                interval: 30s
                timeout: 10s
                retries: 3

          volumes:
            n8n_postgres_data:
            n8n_data:

    - name: "Potwierdzenie utworzenia nowej konfiguracji"
      debug:
        msg: "✓ Nowa konfiguracja utworzona: docker-compose.yml.new"

    # ==========================================================================
    # SPRAWDZENIE RÓŻNIC
    # ==========================================================================
    
    - name: "Porównaj stary i nowy docker-compose"
      shell: |
        echo "=== RÓŻNICE MIĘDZY STARĄ A NOWĄ KONFIGURACJĄ ==="
        diff -u {{ n8n_config_dir }}/docker-compose.yml {{ n8n_config_dir }}/docker-compose.yml.new || true
      register: config_diff

    - name: "=== RÓŻNICE W KONFIGURACJI ==="
      debug:
        msg: "{{ config_diff.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE DOSTĘPNOŚCI NOWEGO OBRAZU
    # ==========================================================================
    
    - name: "Sprawdź dostępność nowego obrazu n8n"
      shell: docker pull n8nio/n8n:{{ n8n_new_version }}
      register: image_pull_result
      ignore_errors: true

    - name: "Status pobierania obrazu"
      debug:
        msg: "{{ 'SUKCES - obraz pobrany' if image_pull_result.rc == 0 else 'BŁĄD - nie można pobrać obrazu' }}"

    # ==========================================================================
    # PRZYGOTOWANIE SKRYPTU AKTUALIZACJI
    # ==========================================================================
    
    - name: "Stwórz skrypt aktualizacji"
      copy:
        dest: "{{ n8n_config_dir }}/update_n8n.sh"
        owner: szymcio
        group: szymcio
        mode: '0755'
        content: |
          #!/bin/bash
          # Skrypt aktualizacji n8n do wersji {{ n8n_new_version }}
          # Utworzony: {{ ansible_date_time.iso8601 }}
          
          set -e
          
          echo "=== AKTUALIZACJA N8N DO WERSJI {{ n8n_new_version }} ==="
          echo "Data: $(date)"
          echo ""
          
          cd {{ n8n_config_dir }}
          
          echo "1. Zatrzymywanie kontenerów..."
          docker-compose down
          
          echo "2. Backup obecnej konfiguracji..."
          cp docker-compose.yml docker-compose.yml.backup.$(date +%s)
          
          echo "3. Zastąpienie konfiguracji nową wersją..."
          cp docker-compose.yml.new docker-compose.yml
          
          echo "4. Pobieranie nowych obrazów..."
          docker-compose pull
          
          echo "5. Uruchamianie z nową wersją..."
          docker-compose up -d
          
          echo "6. Oczekiwanie na uruchomienie..."
          sleep 30
          
          echo "7. Sprawdzanie statusu..."
          docker-compose ps
          
          echo ""
          echo "=== AKTUALIZACJA ZAKOŃCZONA ==="
          echo "Sprawdź czy n8n działa: http://127.0.0.1:5678"

    - name: "Potwierdzenie utworzenia skryptu"
      debug:
        msg: "✓ Skrypt aktualizacji utworzony: update_n8n.sh"

    # ==========================================================================
    # PODSUMOWANIE
    # ==========================================================================
    
    - name: "=== PODSUMOWANIE PRZYGOTOWANIA ==="
      debug:
        msg:
          - "Przygotowanie aktualizacji n8n zakończone"
          - ""
          - "UTWORZONE PLIKI:"
          - "- {{ n8n_config_dir }}/docker-compose.yml.backup.{{ backup_timestamp }}"
          - "- {{ n8n_config_dir }}/docker-compose.yml.new"
          - "- {{ n8n_config_dir }}/update_n8n.sh"
          - ""
          - "NASTĘPNY KROK: Wykonanie aktualizacji"
          - "KOMENDA: cd {{ n8n_config_dir }} && ./update_n8n.sh"
