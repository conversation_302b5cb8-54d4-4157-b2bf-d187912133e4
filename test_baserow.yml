---
# Test aplikacji Baserow
# Sprawdza czy wszystkie komponenty Baserow działają poprawnie

- name: Test Baserow
  hosts: "{{ target_host | default('serwery_nowe') }}"
  gather_facts: true
  vars:
    baserow_home: /opt/baserow
    baserow_domain: baserow.simetria.pl
    baserow_port: 3000
  tasks:
    - name: "Informacje o teście Baserow"
      debug:
        msg:
          - "=== TEST BASEROW ==="
          - "Cel: {{ inventory_hostname }}"
          - "Katalog: {{ baserow_home }}"
          - "Domena: {{ baserow_domain }}"
          - "Port: {{ baserow_port }}"
      tags: [info]

    - name: "Sprawdzenie czy katalog Baserow istnieje"
      stat:
        path: "{{ baserow_home }}"
      register: baserow_dir_check
      tags: [baserow, directory]

    - name: "Sprawdzenie czy plik docker-compose.yml istnieje"
      stat:
        path: "{{ baserow_home }}/docker-compose.yml"
      register: docker_compose_file_check
      tags: [baserow, config]

    - name: "Sprawdzenie statusu kontenerów Baserow"
      shell: |
        cd {{ baserow_home }}
        docker-compose ps
      register: baserow_containers_status
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, containers]

    - name: "Sprawdzenie uruchomionych kontenerów Baserow"
      shell: |
        cd {{ baserow_home }}
        docker-compose ps --services --filter "status=running"
      register: baserow_running_containers
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, containers]

    - name: "Test dostępności backend (port 8000)"
      uri:
        url: "http://localhost:8000/_health/"
        method: GET
        timeout: 30
      register: backend_health_test
      ignore_errors: true
      tags: [baserow, backend, health]

    - name: "Test dostępności frontend (port 3000)"
      uri:
        url: "http://localhost:{{ baserow_port }}/_health/"
        method: GET
        timeout: 30
      register: frontend_health_test
      ignore_errors: true
      tags: [baserow, frontend, health]

    - name: "Test dostępności Baserow przez Nginx"
      uri:
        url: "http://localhost/api/_health/"
        method: GET
        timeout: 30
      register: nginx_proxy_test
      ignore_errors: true
      tags: [baserow, nginx, proxy]

    - name: "Sprawdzenie logów backend"
      shell: |
        cd {{ baserow_home }}
        docker-compose logs --tail=10 backend
      register: backend_logs
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, backend, logs]

    - name: "Sprawdzenie logów frontend"
      shell: |
        cd {{ baserow_home }}
        docker-compose logs --tail=10 frontend
      register: frontend_logs
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, frontend, logs]

    - name: "Sprawdzenie logów PostgreSQL"
      shell: |
        cd {{ baserow_home }}
        docker-compose logs --tail=10 postgres
      register: postgres_logs
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, postgres, logs]

    - name: "Sprawdzenie logów Redis"
      shell: |
        cd {{ baserow_home }}
        docker-compose logs --tail=10 redis
      register: redis_logs
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, redis, logs]

    - name: "Test połączenia z bazą danych"
      shell: |
        cd {{ baserow_home }}
        docker-compose exec -T postgres pg_isready -U baserow -d baserow
      register: postgres_connection_test
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, postgres, connection]

    - name: "Test połączenia z Redis"
      shell: |
        cd {{ baserow_home }}
        docker-compose exec -T redis redis-cli ping
      register: redis_connection_test
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, redis, connection]

    - name: "Sprawdzenie wolumenów Docker"
      shell: |
        cd {{ baserow_home }}
        docker-compose config --volumes
      register: baserow_volumes
      changed_when: false
      ignore_errors: true
      become_user: baserow
      become: true
      tags: [baserow, volumes]

    - name: "Sprawdzenie użycia miejsca przez Baserow"
      shell: du -sh {{ baserow_home }}/*
      register: baserow_disk_usage
      changed_when: false
      ignore_errors: true
      tags: [baserow, disk]

    - name: "Sprawdzenie skryptów backup"
      stat:
        path: "{{ item }}"
      register: backup_scripts_check
      loop:
        - "{{ baserow_home }}/backups/scripts/backup_database.sh"
        - "{{ baserow_home }}/backups/scripts/backup_media.sh"
        - "{{ baserow_home }}/backups/scripts/full_backup.sh"
        - "{{ baserow_home }}/backups/scripts/restore_backup.sh"
      tags: [baserow, backup]

    - name: "Podsumowanie testu Baserow"
      debug:
        msg:
          - "=== WYNIKI TESTU BASEROW ==="
          - ""
          - "📁 STRUKTURA:"
          - "  Katalog Baserow: {{ 'OK' if baserow_dir_check.stat.exists else 'BŁĄD' }}"
          - "  Docker Compose: {{ 'OK' if docker_compose_file_check.stat.exists else 'BŁĄD' }}"
          - ""
          - "🐳 KONTENERY:"
          - "  Uruchomione: {{ baserow_running_containers.stdout_lines | length if baserow_running_containers.rc == 0 else 0 }}/4"
          - "  Status: {{ 'OK' if baserow_running_containers.rc == 0 and baserow_running_containers.stdout_lines | length >= 4 else 'BŁĄD' }}"
          - ""
          - "❤️ HEALTH CHECKS:"
          - "  Backend (8000): {{ 'OK' if backend_health_test is succeeded else 'BŁĄD' }}"
          - "  Frontend (3000): {{ 'OK' if frontend_health_test is succeeded else 'BŁĄD' }}"
          - "  Nginx Proxy: {{ 'OK' if nginx_proxy_test is succeeded else 'BŁĄD' }}"
          - ""
          - "🔗 POŁĄCZENIA:"
          - "  PostgreSQL: {{ 'OK' if postgres_connection_test.rc == 0 else 'BŁĄD' }}"
          - "  Redis: {{ 'OK' if redis_connection_test.rc == 0 else 'BŁĄD' }}"
          - ""
          - "💾 BACKUP:"
          - "  Skrypty backup: {{ backup_scripts_check.results | selectattr('stat.exists') | list | length }}/4"
          - ""
          - "📊 UŻYCIE MIEJSCA:"
          - "{{ baserow_disk_usage.stdout_lines if baserow_disk_usage.rc == 0 else ['Informacje niedostępne'] }}"
          - ""
          - "{{ '✅ BASEROW DZIAŁA POPRAWNIE' if (baserow_dir_check.stat.exists and docker_compose_file_check.stat.exists and backend_health_test is succeeded and frontend_health_test is succeeded) else '❌ PROBLEMY Z BASEROW' }}"
      tags: [info, summary]
