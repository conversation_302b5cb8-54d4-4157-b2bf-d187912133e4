---
# Testowy playbook do testowania agentów logowania
# Uruchamia tylko role logging_agent na określonych hostach

- name: Test konfiguracji logging agent
  hosts: baserow_servers
  become: yes
  vars:
    logging_server: "{{ hostvars[groups['logging_central'][0]]['ansible_default_ipv4']['address'] | default('localhost') }}"
    loki_port: 3100
  vars_files:
    - roles/logging_agent/defaults/main.yml
  
  tasks:
    - name: Sprawd<PERSON> dostę<PERSON>ść hostów
      ping:
      
    - name: Debug - wyświetl konfigurację
      debug:
        msg: "Testujemy konfigurację na {{ inventory_hostname }} z serwerem logowania {{ logging_server }}"
        
    # Pomijamy wdrożenie Baserow dla celów testowania agenta logowania
    # - name: Uruchom rolę baserow_deployment
    #   include_role:
    #     name: baserow_deployment
    #   tags:
    #     - config
    #     - pull
    #     - run
    #     - status
    #     - healthcheck
    #     - resources
    #     - deploy
    - name: Uruchom rolę logging_agent
      include_role:
        name: logging_agent
        
    - name: Sprawdź status usług
      systemd:
        name: "{{ item }}"
        state: started
      loop:
        - node-exporter
        - promtail
      ignore_errors: yes
      register: service_status
      until: service_status is success
      retries: 5
      delay: 10
      
    - name: Test dostępu do serwera logowania
      uri:
        url: "http://{{ logging_server }}:{{ loki_port }}/ready"
        method: GET
      ignore_errors: yes
      register: loki_status
      
    - name: Wyświetl status Loki
      debug:
        msg: "Loki status: {{ loki_status.status | default('Brak odpowiedzi') }}"
      when: loki_status is defined