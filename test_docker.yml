---
# Test konfiguracji Docker
# Sprawdza czy Docker jest zainstalowany, działa poprawnie i może uru<PERSON>mia<PERSON> kontenery

- name: Test Docker
  hosts: "{{ target_host | default('serwery_nowe') }}"
  gather_facts: true
  tasks:
    - name: "Informacje o teście Docker"
      debug:
        msg:
          - "=== TEST DOCKER ==="
          - "Cel: {{ inventory_hostname }}"
          - "IP: {{ ansible_host }}"
      tags: [info]

    - name: "Sprawdzenie czy Docker jest zainstalowany"
      command: which docker
      register: docker_installed
      changed_when: false
      ignore_errors: true
      tags: [docker, install]

    - name: "Sprawdzenie wersji Docker"
      command: docker --version
      register: docker_version
      changed_when: false
      ignore_errors: true
      tags: [docker, version]

    - name: "Sprawdzenie wersji Docker Compose"
      command: docker compose version
      register: docker_compose_version
      changed_when: false
      ignore_errors: true
      tags: [docker, compose, version]

    - name: "Sprawdzenie statusu usługi Docker"
      systemd:
        name: docker
      register: docker_service_status
      ignore_errors: true
      tags: [docker, service]

    - name: "Sprawdzenie czy użytkownik należy do grupy docker"
      shell: groups {{ ansible_user }} | grep -q docker
      register: docker_group_check
      changed_when: false
      ignore_errors: true
      tags: [docker, permissions]

    - name: "Test uruchomienia kontenera hello-world"
      command: docker run --rm hello-world
      register: docker_hello_world
      changed_when: false
      ignore_errors: true
      tags: [docker, test]

    - name: "Sprawdzenie uruchomionych kontenerów"
      command: docker ps
      register: docker_ps
      changed_when: false
      ignore_errors: true
      tags: [docker, containers]

    - name: "Sprawdzenie wszystkich kontenerów"
      command: docker ps -a
      register: docker_ps_all
      changed_when: false
      ignore_errors: true
      tags: [docker, containers]

    - name: "Sprawdzenie obrazów Docker"
      command: docker images
      register: docker_images
      changed_when: false
      ignore_errors: true
      tags: [docker, images]

    - name: "Sprawdzenie sieci Docker"
      command: docker network ls
      register: docker_networks
      changed_when: false
      ignore_errors: true
      tags: [docker, network]

    - name: "Sprawdzenie wolumenów Docker"
      command: docker volume ls
      register: docker_volumes
      changed_when: false
      ignore_errors: true
      tags: [docker, volumes]

    - name: "Sprawdzenie użycia miejsca przez Docker"
      command: docker system df
      register: docker_disk_usage
      changed_when: false
      ignore_errors: true
      tags: [docker, disk]

    - name: "Sprawdzenie informacji o systemie Docker"
      command: docker info
      register: docker_info
      changed_when: false
      ignore_errors: true
      tags: [docker, info]

    - name: "Sprawdzenie logów Docker daemon"
      shell: journalctl -u docker --no-pager -n 10
      register: docker_logs
      changed_when: false
      become: true
      ignore_errors: true
      tags: [docker, logs]

    - name: "Sprawdzenie konfiguracji Docker daemon"
      shell: |
        if [ -f "/etc/docker/daemon.json" ]; then
          cat /etc/docker/daemon.json
        else
          echo "Brak pliku konfiguracyjnego daemon.json"
        fi
      register: docker_daemon_config
      changed_when: false
      become: true
      tags: [docker, config]

    - name: "Podsumowanie testu Docker"
      debug:
        msg:
          - "=== WYNIKI TESTU DOCKER ==="
          - ""
          - "🔧 INSTALACJA:"
          - "  Docker zainstalowany: {{ 'OK' if docker_installed.rc == 0 else 'BŁĄD' }}"
          - "  Wersja Docker: {{ docker_version.stdout if docker_version.rc == 0 else 'NIEZNANA' }}"
          - "  Wersja Docker Compose: {{ docker_compose_version.stdout if docker_compose_version.rc == 0 else 'NIEZNANA' }}"
          - ""
          - "⚙️ USŁUGA:"
          - "  Status Docker daemon: {{ docker_service_status.status.ActiveState | default('NIEZNANY') }}"
          - "  Grupa docker: {{ 'OK' if docker_group_check.rc == 0 else 'BŁĄD - użytkownik nie należy do grupy docker' }}"
          - ""
          - "🧪 TESTY FUNKCJONALNE:"
          - "  Hello World: {{ 'OK' if docker_hello_world.rc == 0 else 'BŁĄD' }}"
          - ""
          - "📊 ZASOBY:"
          - "  Uruchomione kontenery: {{ docker_ps.stdout_lines | length - 1 if docker_ps.rc == 0 and docker_ps.stdout_lines | length > 1 else 0 }}"
          - "  Wszystkie kontenery: {{ docker_ps_all.stdout_lines | length - 1 if docker_ps_all.rc == 0 and docker_ps_all.stdout_lines | length > 1 else 0 }}"
          - "  Obrazy: {{ docker_images.stdout_lines | length - 1 if docker_images.rc == 0 and docker_images.stdout_lines | length > 1 else 0 }}"
          - "  Sieci: {{ docker_networks.stdout_lines | length - 1 if docker_networks.rc == 0 and docker_networks.stdout_lines | length > 1 else 0 }}"
          - "  Wolumeny: {{ docker_volumes.stdout_lines | length - 1 if docker_volumes.rc == 0 and docker_volumes.stdout_lines | length > 1 else 0 }}"
          - ""
          - "💾 UŻYCIE MIEJSCA:"
          - "{{ docker_disk_usage.stdout_lines if docker_disk_usage.rc == 0 else ['Informacje niedostępne'] }}"
          - ""
          - "{{ '✅ DOCKER DZIAŁA POPRAWNIE' if (docker_installed.rc == 0 and docker_service_status.status.ActiveState == 'active' and docker_hello_world.rc == 0) else '❌ PROBLEMY Z DOCKER' }}"
      tags: [info, summary]
