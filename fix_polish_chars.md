# Analiza problemu z polskimi znakami w systemie SMS

## 🔍 **Zidentyfikowany problem**

Po analizie aktualnej konfiguracji i logów systemu, znalazłem **bezpośrednią przyczynę** braku polskich znaków w wysyłanych SMS-ach:

### **Główna przyczyna: Brak parametru `coding = 2` w konfiguracji SMSC**

W pliku `/etc/kannel/kannel.conf` w sekcji `group = smsc` brakuje kluczowego parametru:

```conf
group = smsc
smsc = at
smsc-id = Huawei
device = /dev/ttyUSB0
sms-center = +48601000310
sim-buffering = true
validityperiod = 167
my-number = +48780253478
log-level = 0
alt-charset = "UTF-8"  # ✅ OBECNE
# ❌ BRAKUJE: coding = 2
```

### **Dowód z logów**

W `/var/log/kannel/core.log.1` w<PERSON><PERSON>, że Kanne<PERSON> wysyła SMS-y w formacie 7-bit:

```
AT2[Huawei]: --> 0031000B918411111111F10000A72DF4F29C0E9287E16F39FD7D0791DFF2F2585F76835A20F81B2E0FDFD36F77180497BFC765725D1E06
```

PDU zaczyna się od `00` co oznacza kodowanie 7-bit, które **nie obsługuje polskich znaków**.

### **Rozwiązanie**

Dodać parametr `coding = 2` do sekcji SMSC, który wymusi kodowanie UCS-2 (16-bit Unicode):

```conf
group = smsc
smsc = at
smsc-id = Huawei
device = /dev/ttyUSB0
sms-center = +48601000310
sim-buffering = true
validityperiod = 167
my-number = +48780253478
log-level = 0
alt-charset = "UTF-8"
coding = 2  # ← DODAĆ TEN PARAMETR
```

### **Status implementacji rozwiązań z dokumentacji**

✅ **Zaimplementowane:**
- `sendsms-chars` jest wykomentowane (linia 32)
- `alt-charset = "UTF-8"` jest obecne (linia 51)

❌ **Brakuje:**
- `coding = 2` - kluczowy parametr dla polskich znaków

### **Dodatkowe obserwacje**

1. **Błędy CMS ERROR 500** nadal występują, co może wskazywać na problemy z modemem przy próbie wysyłania znaków Unicode
2. **System działa** - SMS-y są akceptowane i wysyłane, ale bez polskich znaków
3. **Webhook proxy** działa poprawnie dla SMS-ów przychodzących

### **✅ PROBLEM ROZWIĄZANY**

**Przyczyna:** Dokumentacja była niepoprawna - parametr `coding = 2` **nie może** być dodany do konfiguracji SMSC w Kannel (błąd: "Group 'smsc' may not contain field 'coding'").

**Poprawne rozwiązanie:** Parametry `charset=UTF-8&coding=2` muszą być dodawane **per-request** do każdego żądania HTTP sendsms.

**Implementacja:**
1. ✅ Zmodyfikowano `/home/<USER>/sms_api/app/services/kannel_client.py`
2. ✅ Dodano automatyczne parametry kodowania do każdego żądania SMS
3. ✅ Zrestartowano API wrapper

**Kod dodany do API wrapper:**
```python
# Dodaj parametry dla polskich znaków
params['charset'] = 'UTF-8'
params['coding'] = '2'
```

**Weryfikacja w logach:**
- **Z parametrami kodowania:** `flags:-1:2:-1:-1:-1` = UCS-2 ✅ Polskie znaki działają
- **Bez parametrów kodowania:** `flags:-1:0:-1:-1:-1` = 7-bit ❌ Polskie znaki → `?`

**Status:** Wszystkie SMS-y wysyłane przez API wrapper (port 5050) automatycznie używają kodowania Unicode i poprawnie obsługują polskie znaki.
