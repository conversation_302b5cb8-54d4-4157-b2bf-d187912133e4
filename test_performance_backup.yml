---
- name: Test backup performance
  hosts: all
  become: yes
  vars:
    test_data_size_mb: 100
    test_backup_dir: /tmp/backup_performance_test
    test_data_dir: /tmp/test_data
    
  tasks:
    - name: Install required packages for testing
      package:
        name:
          - time
          - gzip
          - tar
        state: present
      ignore_errors: yes

    - name: Create test directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - "{{ test_backup_dir }}"
        - "{{ test_data_dir }}"

    - name: Generate test data
      shell: |
        echo "📊 Generating {{ test_data_size_mb }}MB of test data..."
        dd if=/dev/urandom of={{ test_data_dir }}/test_data_{{ test_data_size_mb }}mb.bin bs=1M count={{ test_data_size_mb }} 2>/dev/null
        echo "✅ Test data generated successfully"
        ls -lh {{ test_data_dir }}/test_data_{{ test_data_size_mb }}mb.bin
      register: data_generation

    - name: Test backup performance - tar + gzip
      shell: |
        echo "🚀 Testing backup performance with tar + gzip..."
        start_time=$(date +%s)
        
        tar -czf {{ test_backup_dir }}/backup_test.tar.gz -C {{ test_data_dir }} .
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        backup_size=$(du -h {{ test_backup_dir }}/backup_test.tar.gz | cut -f1)
        original_size=$(du -h {{ test_data_dir }} | cut -f1)
        
        echo "📊 Backup completed in ${duration}s"
        echo "📦 Original size: $original_size"
        echo "📦 Backup size: $backup_size"
        echo "✅ Tar + gzip backup: PASSED"
        
        echo "duration=$duration" > /tmp/backup_stats.txt
        echo "backup_size=$backup_size" >> /tmp/backup_stats.txt
        echo "original_size=$original_size" >> /tmp/backup_stats.txt
      register: backup_performance

    - name: Test backup verification performance
      shell: |
        echo "🔍 Testing backup verification performance..."
        start_time=$(date +%s)
        
        # Verify tar.gz integrity
        tar -tzf {{ test_backup_dir }}/backup_test.tar.gz > /dev/null
        
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        echo "🔍 Verification completed in ${duration}s"
        echo "✅ Backup verification: PASSED"
        
        echo "verify_duration=$duration" >> /tmp/backup_stats.txt
      register: verify_performance

    - name: Load performance statistics
      shell: cat /tmp/backup_stats.txt
      register: performance_stats

    - name: Display performance test results
      debug:
        var: performance_stats.stdout_lines

    - name: Display final performance summary
      debug:
        msg:
          - "🚀 BACKUP PERFORMANCE TEST RESULTS:"
          - "📊 Test data size: {{ test_data_size_mb }}MB"
          - "⏱️  Tar + gzip backup: {{ (performance_stats.stdout | regex_search('duration=([0-9]+)', '\\1') | first) }}s"
          - "⏱️  Verification: {{ (performance_stats.stdout | regex_search('verify_duration=([0-9]+)', '\\1') | first) }}s"
          - "📦 Original: {{ (performance_stats.stdout | regex_search('original_size=([0-9.M]+)', '\\1') | first) }}"
          - "📦 Compressed: {{ (performance_stats.stdout | regex_search('backup_size=([0-9.M]+)', '\\1') | first) }}"
          - "✅ All backup performance tests: COMPLETED SUCCESSFULLY"

    - name: Cleanup test files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - "{{ test_backup_dir }}"
        - "{{ test_data_dir }}"
        - /tmp/backup_stats.txt
      ignore_errors: yes