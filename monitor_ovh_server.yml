---
- name: Monitoring serwera OVH - sprawd<PERSON>ie stanu i alertów
  hosts: simetria-ovh
  become: true
  gather_facts: true
  vars:
    disk_warning_threshold: 85
    disk_critical_threshold: 95
    memory_warning_threshold: 80
    load_warning_threshold: 2.0
    
  tasks:
    # ========================================
    # SPRAWDZENIE PODSTAWOWYCH METRYK
    # ========================================
    - name: Sprawdź wykorzystanie dysku
      ansible.builtin.shell: |
        df -h / | tail -1 | awk '{print $5}' | sed 's/%//'
      register: disk_usage_percent
      tags: [monitoring, disk]

    - name: Sprawdź wykorzystanie pamięci
      ansible.builtin.shell: |
        free | grep Mem | awk '{printf "%.0f", ($3/$2) * 100.0}'
      register: memory_usage_percent
      tags: [monitoring, memory]

    - name: Sprawdź load average
      ansible.builtin.shell: |
        uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//'
      register: load_average
      tags: [monitoring, load]

    - name: Sprawdź status kluczowych usług
      ansible.builtin.shell: |
        echo "=== STATUS USŁUG ==="
        systemctl is-active docker || echo "docker: INACTIVE"
        systemctl is-active nginx || echo "nginx: INACTIVE"
        systemctl is-active ssh || echo "ssh: INACTIVE"
        echo "=== KONTENERY DOCKER ==="
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "Docker niedostępny"
      register: services_status
      tags: [monitoring, services]

    # ========================================
    # SPRAWDZENIE ALERTÓW
    # ========================================
    - name: Sprawdź alerty dysku
      ansible.builtin.set_fact:
        disk_alert: |
          {% if disk_usage_percent.stdout|int >= disk_critical_threshold %}
          KRYTYCZNY: Dysk zapełniony w {{ disk_usage_percent.stdout }}%
          {% elif disk_usage_percent.stdout|int >= disk_warning_threshold %}
          OSTRZEŻENIE: Dysk zapełniony w {{ disk_usage_percent.stdout }}%
          {% else %}
          OK: Dysk zapełniony w {{ disk_usage_percent.stdout }}%
          {% endif %}
      tags: [monitoring, alerts]

    - name: Sprawdź alerty pamięci
      ansible.builtin.set_fact:
        memory_alert: |
          {% if memory_usage_percent.stdout|int >= memory_warning_threshold %}
          OSTRZEŻENIE: Pamięć wykorzystana w {{ memory_usage_percent.stdout }}%
          {% else %}
          OK: Pamięć wykorzystana w {{ memory_usage_percent.stdout }}%
          {% endif %}
      tags: [monitoring, alerts]

    - name: Sprawdź alerty load average
      ansible.builtin.set_fact:
        load_alert: |
          {% if load_average.stdout|float >= load_warning_threshold %}
          OSTRZEŻENIE: Wysokie obciążenie CPU: {{ load_average.stdout }}
          {% else %}
          OK: Obciążenie CPU: {{ load_average.stdout }}
          {% endif %}
      tags: [monitoring, alerts]

    # ========================================
    # SPRAWDZENIE DOCKER
    # ========================================
    - name: Sprawdź zasoby Docker
      ansible.builtin.shell: |
        echo "=== DOCKER SYSTEM DF ==="
        docker system df 2>/dev/null || echo "Docker niedostępny"
        echo "=== DOCKER STATS ==="
        timeout 3 docker stats --no-stream 2>/dev/null || echo "Brak uruchomionych kontenerów"
      register: docker_status
      ignore_errors: true
      tags: [monitoring, docker]

    # ========================================
    # SPRAWDZENIE LOGÓW BŁĘDÓW
    # ========================================
    - name: Sprawdź ostatnie błędy w logach
      ansible.builtin.shell: |
        echo "=== OSTATNIE BŁĘDY SYSTEMOWE ==="
        journalctl --since "1 hour ago" --priority=err --no-pager | tail -10 || echo "Brak błędów"
        echo "=== FAILED SERVICES ==="
        systemctl --failed --no-pager || echo "Brak nieudanych usług"
      register: error_logs
      tags: [monitoring, logs]

    # ========================================
    # SPRAWDZENIE POŁĄCZEŃ SIECIOWYCH
    # ========================================
    - name: Sprawdź połączenia sieciowe
      ansible.builtin.shell: |
        echo "=== AKTYWNE POŁĄCZENIA SSH ==="
        netstat -tnp | grep :1988 | wc -l
        echo "=== TOP POŁĄCZENIA ==="
        netstat -tuln | grep LISTEN | head -10
      register: network_status
      tags: [monitoring, network]

    # ========================================
    # RAPORT MONITORINGU
    # ========================================
    - name: Wyświetl raport monitoringu
      ansible.builtin.debug:
        msg:
          - "=================================="
          - "RAPORT MONITORINGU SERWERA OVH"
          - "=================================="
          - "Data: {{ ansible_date_time.iso8601 }}"
          - "Host: {{ inventory_hostname }}"
          - ""
          - "=== ALERTY ==="
          - "{{ disk_alert }}"
          - "{{ memory_alert }}"
          - "{{ load_alert }}"
          - ""
          - "=== METRYKI ==="
          - "Dysk: {{ disk_usage_percent.stdout }}%"
          - "Pamięć: {{ memory_usage_percent.stdout }}%"
          - "Load Average: {{ load_average.stdout }}"
          - ""
          - "=== STATUS USŁUG ==="
          - "{{ services_status.stdout_lines }}"
          - ""
          - "=== DOCKER ==="
          - "{{ docker_status.stdout_lines }}"
          - ""
          - "=== BŁĘDY ==="
          - "{{ error_logs.stdout_lines }}"
          - ""
          - "=== SIEĆ ==="
          - "{{ network_status.stdout_lines }}"
      tags: [monitoring, report]

    # ========================================
    # AUTOMATYCZNE DZIAŁANIA
    # ========================================
    - name: Automatyczne czyszczenie przy krytycznym zapełnieniu dysku
      ansible.builtin.shell: |
        echo "Dysk zapełniony w {{ disk_usage_percent.stdout }}% - uruchamiam awaryjne czyszczenie"
        # Szybkie czyszczenie Docker
        docker system prune -f 2>/dev/null || true
        # Czyszczenie logów
        journalctl --vacuum-size=100M
        # Czyszczenie APT
        apt-get clean
      when: disk_usage_percent.stdout|int >= disk_critical_threshold
      register: emergency_cleanup
      tags: [monitoring, emergency]

    - name: Wyświetl wyniki awaryjnego czyszczenia
      ansible.builtin.debug:
        msg:
          - "=== AWARYJNE CZYSZCZENIE WYKONANE ==="
          - "{{ emergency_cleanup.stdout_lines }}"
      when: emergency_cleanup is defined and emergency_cleanup.changed
      tags: [monitoring, emergency]

    # ========================================
    # ZAPISANIE RAPORTU
    # ========================================
    - name: Zapisz raport monitoringu
      ansible.builtin.copy:
        content: |
          RAPORT MONITORINGU SERWERA OVH
          Data: {{ ansible_date_time.iso8601 }}
          Host: {{ inventory_hostname }}
          
          === ALERTY ===
          {{ disk_alert }}
          {{ memory_alert }}
          {{ load_alert }}
          
          === METRYKI ===
          Dysk: {{ disk_usage_percent.stdout }}%
          Pamięć: {{ memory_usage_percent.stdout }}%
          Load Average: {{ load_average.stdout }}
          
          === STATUS USŁUG ===
          {{ services_status.stdout }}
          
          === DOCKER ===
          {{ docker_status.stdout }}
          
          === BŁĘDY ===
          {{ error_logs.stdout }}
          
          === SIEĆ ===
          {{ network_status.stdout }}
          
          {% if emergency_cleanup is defined and emergency_cleanup.changed %}
          === AWARYJNE CZYSZCZENIE ===
          {{ emergency_cleanup.stdout }}
          {% endif %}
        dest: "/tmp/monitoring_report_{{ ansible_date_time.epoch }}.txt"
        mode: '0644'
      tags: [monitoring, save]

    # ========================================
    # REKOMENDACJE
    # ========================================
    - name: Wyświetl rekomendacje
      ansible.builtin.debug:
        msg:
          - "=== REKOMENDACJE ==="
          - "{% if disk_usage_percent.stdout|int >= disk_warning_threshold %}🔴 PILNE: Wyczyść dysk - zapełniony w {{ disk_usage_percent.stdout }}%{% endif %}"
          - "{% if memory_usage_percent.stdout|int >= memory_warning_threshold %}🟡 Sprawdź procesy zużywające pamięć{% endif %}"
          - "{% if load_average.stdout|float >= load_warning_threshold %}🟡 Sprawdź procesy obciążające CPU{% endif %}"
          - "💡 Uruchom regularnie: ansible-playbook monitor_ovh_server.yml"
          - "💡 W przypadku problemów: bash emergency_cleanup.sh"
      tags: [monitoring, recommendations]
