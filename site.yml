---
- name: <PERSON>n<PERSON>gu<PERSON><PERSON> wszystkich serwerów
  hosts: serwery
  become: true
  roles:
    - role: common
      tags: [common, base, system]
      when: "'common' in ansible_run_tags or ansible_run_tags == ['all']"
    
    - role: security_hardening
      tags: [security, hardening, firewall]
      when: "'security' in ansible_run_tags or ansible_run_tags == ['all']"
    
    - role: docker_setup
      tags: [docker, containers]
      when:
        - inventory_hostname in groups['serwery_nowe']
        - "'docker' in ansible_run_tags or ansible_run_tags == ['all']"

    - role: baserow_app
      tags: [baserow, application]
      when:
        - inventory_hostname in groups['serwery_nowe']
        - "'baserow' in ansible_run_tags or ansible_run_tags == ['all']"

    - role: nginx_setup
      tags: [nginx, proxy, web]
      when:
        - inventory_hostname in groups['serwery_nowe']
        - "'nginx' in ansible_run_tags or ansible_run_tags == ['all']"

    - role: ssl_setup
      tags: [ssl, certificates, https]
      when:
        - inventory_hostname in groups['serwery_nowe']
        - "'ssl' in ansible_run_tags or ansible_run_tags == ['all']"
        - ssl_setup_enabled | default(true)

    - role: backup_setup
      tags: [backup, maintenance]
      when:
        - inventory_hostname in groups['serwery_nowe']
        - "'backup' in ansible_run_tags or ansible_run_tags == ['all']"
    
    - role: logging_agent
      tags: [logging, monitoring]
      when: "'logging' in ansible_run_tags or ansible_run_tags == ['all']"
    
    - role: logging_central
      tags: [logging, central, grafana]
      when: 
        - inventory_hostname in groups['serwery_nowe']
        - "'logging' in ansible_run_tags or ansible_run_tags == ['all']"
