# Dziennik Migracji Baserow

**Data:** 19 lipca 2025

## Podsumowanie Diagnostyki

Serwer <PERSON> (cel migracji) działa poprawnie. Serwer OVH (źr<PERSON>dł<PERSON> danych) nie dzia<PERSON>, ale dane są dostępne. Moż<PERSON>wość migracji jest wysoka, poni<PERSON><PERSON>ż dane są zachowane i dostępne. Dostępne są świeże backupy z 1 lipca 2025.

## Odpowiedzi na Pytania Kluczowe

- **<PERSON><PERSON> konieczne jest uruchomienie starej aplikacji Baserow na OVH?** Nie, zalecana jest migracja z backupów.
- **<PERSON><PERSON> instrukcja z baserow-instruction.md jest odpowiednia dla tego przypadku?** Nie, instrukcja dotyczy eksportu/importu workspace'ów, a my migrujemy całą instancję.
- **Gdzie znajdują się dane do migracji?** Dane do migracji znajdują się w backupach na serwerze OVH w lokalizacji `/home/<USER>/backup/`.
- **Jaka jest zalecana strategia migracji?** Zalecana strategia to migracja z backupów z 1 lipca 2025.

## Rekomendowana Strategia Migracji

1.  **Przygotowanie środowiska docelowego (Hetzner):**
    *   Zatrzymanie obecnej instalacji Baserow na Hetzner.
    *   Wykonanie backupu obecnych danych na Hetzner (dla bezpieczeństwa).
    *   Przygotowanie czystej instalacji Baserow.
2.  **Transfer danych z OVH:**
    *   Pobranie backupów z OVH (`/home/<USER>/backup/baserow_*_2025-07-01_*`).
    *   Weryfikacja integralności backupów.
    *   Transfer backupów na serwer Hetzner.
3.  **Przywracanie na Hetzner:**
    *   Import bazy danych z `baserow_db_2025-07-01_09-50.sql`.
    *   Rozpakowanie plików media z `baserow_media_2025-07-01_09-50.tar.gz`.
    *   Konfiguracja Baserow na podstawie działającej instalacji na Hetzner.
    *   Testowanie przywróconej instalacji.
4.  **Weryfikacja i czyszczenie:**
    *   Przeprowadzenie testów funkcjonalnych aplikacji Baserow.
    *   Weryfikacja danych użytkowników i tabel.
    *   Opcjonalne czyszczenie serwera OVH (po potwierdzeniu sukcesu migracji).

## Wykonane Kroki Migracji

### ✅ FAZA 1: Przygotowanie środowiska docelowego (Hetzner) - ZAKOŃCZONA
- **Data:** 19 lipca 2025, 11:16
- **Wykonane działania:**
  - Zatrzymanie kontenerów Baserow na Hetzner
  - Wykonanie backupu obecnych danych na Hetzner (`/tmp/hetzner_pre_migration_backup_20250719_111627.sql`)
  - Usunięcie starych woluminów Docker (`baserow_postgres_data`, `baserow_baserow_data`)
  - Przygotowanie czystego środowiska

### ✅ FAZA 2: Transfer danych z OVH - ZAKOŃCZONA
- **Data:** 19 lipca 2025, 11:18-11:19
- **Przeniesione pliki:**
  - `baserow_db_2025-07-01_09-50.sql` (49M) - baza danych
  - `baserow_media_2025-07-01_09-50.tar.gz` (118M) - pliki media
  - `baserow_config_2025-07-01_09-50.tar.gz` (486B) - konfiguracja
  - `baserow_files_2025-07-01_09-50.tar.gz` (87B) - pliki aplikacji
  - `baserow_redis_2025-07-01_09-50.tar.gz` (664K) - dane Redis
- **Lokalizacja na Hetzner:** `/tmp/baserow_*_2025-07-01_*`

### ✅ FAZA 3: Przywracanie na Hetzner - ZAKOŃCZONA
- **Data:** 19 lipca 2025, 11:20-11:28
- **Wykonane działania:**
  1. **Import bazy danych:**
     - Uruchomienie tymczasowego kontenera PostgreSQL
     - Import bazy danych z `baserow_db_2025-07-01_09-50.sql`
     - Reset hasła użytkownika `baserow` w bazie danych

  2. **Przywrócenie plików media:**
     - Utworzenie nowego woluminu `baserow_baserow_data`
     - Rozpakowanie plików media z `baserow_media_2025-07-01_09-50.tar.gz`

  3. **Uruchomienie kontenerów:**
     - PostgreSQL: `baserow-postgres` (postgres:13)
     - Redis: `baserow-redis` (redis:6.2)
     - Backend: `baserow-backend` (baserow/backend:latest)
     - Frontend: `baserow-frontend` (baserow/web-frontend:latest)

  4. **Konfiguracja środowiska:**
     - DATABASE_HOST=db, DATABASE_USER=baserow, DATABASE_PASSWORD=baserow
     - REDIS_URL=redis://redis:6379
     - SECRET_KEY=migrated_baserow_secret_key_2025
     - BASEROW_PUBLIC_URL=https://baserow.simetria.pl

### ✅ FAZA 4: Weryfikacja i finalizacja - ZAKOŃCZONA
- **Data:** 19 lipca 2025, 11:29-11:30
- **Testy przeprowadzone:**
  1. **Test dostępności aplikacji:**
     - ✅ HTTPS: `https://baserow.simetria.pl` - HTTP 302 (przekierowanie na /login)
     - ✅ API: `https://baserow.simetria.pl/api/settings/` - HTTP 200

  2. **Weryfikacja danych:**
     - ✅ Użytkownicy: 2 konta użytkowników przywrócone
     - ✅ Bazy danych: 134 bazy danych przywrócone
     - ✅ Wszystkie kontenery działają poprawnie

  3. **Status kontenerów:**
     ```
     baserow-frontend: Up (healthy) - 127.0.0.1:3000->3000/tcp
     baserow-backend:  Up (healthy) - 127.0.0.1:8000->8000/tcp
     baserow-redis:    Up - 6379/tcp
     baserow-postgres: Up - 5432/tcp
     ```

## 🎉 MIGRACJA ZAKOŃCZONA SUKCESEM

**Data zakończenia:** 19 lipca 2025, 11:30
**Status:** ✅ SUKCES
**Aplikacja dostępna:** https://baserow.simetria.pl

### Podsumowanie migracji:
- ✅ Wszystkie dane z 1 lipca 2025 zostały przywrócone
- ✅ Aplikacja Baserow działa poprawnie na serwerze Hetzner
- ✅ API i frontend są dostępne przez HTTPS
- ✅ Baza danych zawiera 2 użytkowników i 134 bazy danych
- ✅ Pliki media zostały przywrócone
- ✅ Backup danych z Hetzner został wykonany przed migracją

## 🔧 NAPRAWA WEBSOCKET - 19 lipca 2025, 11:37

### Problem:
- Frontend wyświetlał komunikat o problemach z połączeniem WebSocket
- Prośba o odświeżenie strony z powodu nieudanych połączeń

### Diagnoza:
- Konfiguracja nginx zawierała poprawne ustawienia WebSocket (`proxy_set_header Upgrade $http_upgrade`)
- Problem był w konfiguracji zmiennych środowiskowych kontenerów
- Frontend używał `PUBLIC_BACKEND_URL=http://backend:8000` zamiast HTTPS URL

### Rozwiązanie:
1. **Zaktualizowano konfigurację backend:**
   ```bash
   docker run -d --name baserow-backend \
     -e PUBLIC_BACKEND_URL=https://baserow.simetria.pl \
     -e PUBLIC_WEB_FRONTEND_URL=https://baserow.simetria.pl \
     -e BASEROW_PUBLIC_URL=https://baserow.simetria.pl \
     # ... inne zmienne
   ```

2. **Zaktualizowano konfigurację frontend:**
   ```bash
   docker run -d --name baserow-frontend \
     -e PUBLIC_BACKEND_URL=https://baserow.simetria.pl \
     -e PUBLIC_WEB_FRONTEND_URL=https://baserow.simetria.pl \
     -e BASEROW_PUBLIC_URL=https://baserow.simetria.pl \
     # ... inne zmienne
   ```

### 🔍 Dalsze diagnozy - 19 lipca 2025, 11:45:

**Problem nadal występuje** - komunikat o błędzie połączenia WebSocket.

**Znaleziona przyczyna:**
- W dokumentacji Baserow nginx powinien mieć: `location ~ ^/(api|ws)/`
- Aktualna konfiguracja ma tylko: `location /api/`
- **BRAKUJE obsługi endpoint `/ws/` w nginx!**

**Endpoint WebSocket w Baserow:** `/ws/core/`
- Test: `curl -I http://127.0.0.1:8000/ws/core/` → HTTP 404 (endpoint nie istnieje w backend)
- Problem: nginx nie przekierowuje żądań `/ws/` do backend

### ✅ Naprawa nginx - WYKONANA - 19 lipca 2025, 11:46:

**Wykonane działania:**
1. Backup konfiguracji: `/etc/nginx/nginx.conf.backup.20250719_114505`
2. Zmiana linii 172: `location /api/` → `location ~ ^/(api|ws)/`
3. Test konfiguracji: `nginx -t` ✅ OK
4. Przeładowanie: `systemctl reload nginx` ✅ OK

**Weryfikacja naprawy:**
- ✅ Endpoint `/ws/` jest teraz dostępny przez nginx
- ✅ Backend logi pokazują: `WebSocket /ws/core/?jwt_token=... [accepted]`
- ✅ Backend logi pokazują: `connection open`
- ✅ CSP headers zawierają: `connect-src 'self' ws: wss:`

### 🎉 PROBLEM WEBSOCKET ROZWIĄZANY!

**Status:** WebSocket działa poprawnie - połączenia są akceptowane przez backend.

### Następne kroki (opcjonalne):
1. 🧹 **Czyszczenie serwera OVH** - usunięcie niepotrzebnych kontenerów i woluminów
2. 📚 **Dokumentacja** - aktualizacja dokumentacji konfiguracji na Hetzner
3. 🔄 **Optymalizacja** - konfiguracja automatycznych backupów na Hetzner