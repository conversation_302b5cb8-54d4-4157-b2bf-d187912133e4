---
- name: Sprawdzen<PERSON> obecnej konfiguracji n8n
  hosts: simetria-ovh
  become: true
  gather_facts: true
  
  tasks:
    - name: "=== SPRAWDZENIE KONFIGURACJI N8N ==="
      debug:
        msg:
          - "Analizuję obecną konfigurację n8n na serwerze {{ ansible_hostname }}"
          - "Data: {{ ansible_date_time.iso8601 }}"

    # ==========================================================================
    # SPRAWDZENIE GŁÓWNEJ KONFIGURACJI DOCKER-COMPOSE
    # ==========================================================================
    
    - name: "Sprawdź czy istnieje główny plik docker-compose"
      stat:
        path: /home/<USER>/projekty/n8n/docker-compose.yml
      register: main_compose_file

    - name: "Wyświetl zawartość głównego docker-compose.yml"
      shell: cat /home/<USER>/projekty/n8n/docker-compose.yml
      register: main_compose_content
      when: main_compose_file.stat.exists

    - name: "=== GŁÓWNY DOCKER-COMPOSE.YML ==="
      debug:
        msg: "{{ main_compose_content.stdout_lines | default(['Plik nie istnieje']) }}"

    # ==========================================================================
    # SPRAWDZENIE PLIKU .ENV (JEŚLI ISTNIEJE)
    # ==========================================================================
    
    - name: "Sprawdź czy istnieje plik .env"
      stat:
        path: /home/<USER>/projekty/n8n/.env
      register: env_file

    - name: "Wyświetl zawartość .env (bez haseł)"
      shell: |
        if [ -f /home/<USER>/projekty/n8n/.env ]; then
          echo "=== Plik .env istnieje ==="
          grep -v -i "password\|secret\|key" /home/<USER>/projekty/n8n/.env || echo "Brak zmiennych (lub wszystkie są hasłami)"
        else
          echo "Plik .env nie istnieje"
        fi
      register: env_content

    - name: "=== PLIK .ENV ==="
      debug:
        msg: "{{ env_content.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE DZIAŁAJĄCYCH KONTENERÓW
    # ==========================================================================
    
    - name: "Sprawdź działające kontenery n8n"
      shell: docker ps --filter "name=n8n"
      register: running_containers

    - name: "=== DZIAŁAJĄCE KONTENERY ==="
      debug:
        msg: "{{ running_containers.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE WOLUMINÓW
    # ==========================================================================
    
    - name: "Lista woluminów n8n"
      shell: docker volume ls | grep n8n || echo "Brak woluminów n8n"
      register: volumes_list

    - name: "=== WOLUMINY N8N ==="
      debug:
        msg: "{{ volumes_list.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE OBRAZÓW DOCKER
    # ==========================================================================
    
    - name: "Sprawdź obrazy n8n"
      shell: docker images | grep n8n || echo "Brak obrazów n8n"
      register: images_list

    - name: "=== OBRAZY N8N ==="
      debug:
        msg: "{{ images_list.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE KONFIGURACJI KONTENERÓW
    # ==========================================================================
    
    - name: "Sprawdź konfigurację kontenera n8n"
      shell: |
        if docker ps --filter "name=n8n" --format "{{.Names}}" | grep -q "^n8n$"; then
          echo "=== Konfiguracja kontenera n8n ==="
          echo "Obraz:"
          docker inspect n8n --format='{{.Config.Image}}'
          echo ""
          echo "Porty:"
          docker inspect n8n --format='{{range $p, $conf := .NetworkSettings.Ports}}{{$p}} -> {{(index $conf 0).HostPort}}{{"\n"}}{{end}}'
          echo ""
          echo "Woluminy:"
          docker inspect n8n --format='{{range .Mounts}}{{.Source}} -> {{.Destination}} ({{.Type}}){{"\n"}}{{end}}'
          echo ""
          echo "Zmienne środowiskowe (bez haseł):"
          docker inspect n8n --format='{{range .Config.Env}}{{.}}{{"\n"}}{{end}}' | grep -v -i "password\|secret\|key" || echo "Brak zmiennych (lub wszystkie są hasłami)"
        else
          echo "Kontener n8n nie działa"
        fi
      register: n8n_container_config

    - name: "=== KONFIGURACJA KONTENERA N8N ==="
      debug:
        msg: "{{ n8n_container_config.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE KONFIGURACJI BAZY DANYCH
    # ==========================================================================
    
    - name: "Sprawdź konfigurację bazy danych n8n"
      shell: |
        if docker ps --filter "name=n8n_postgres" --format "{{.Names}}" | grep -q "n8n_postgres"; then
          echo "=== Konfiguracja bazy danych n8n_postgres ==="
          echo "Obraz:"
          docker inspect n8n_postgres --format='{{.Config.Image}}'
          echo ""
          echo "Porty:"
          docker inspect n8n_postgres --format='{{range $p, $conf := .NetworkSettings.Ports}}{{$p}} -> {{(index $conf 0).HostPort}}{{"\n"}}{{end}}'
          echo ""
          echo "Woluminy:"
          docker inspect n8n_postgres --format='{{range .Mounts}}{{.Source}} -> {{.Destination}} ({{.Type}}){{"\n"}}{{end}}'
        else
          echo "Kontener n8n_postgres nie działa"
        fi
      register: postgres_container_config

    - name: "=== KONFIGURACJA BAZY DANYCH ==="
      debug:
        msg: "{{ postgres_container_config.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE DOSTĘPNOŚCI N8N
    # ==========================================================================
    
    - name: "Test dostępności n8n"
      uri:
        url: "http://127.0.0.1:5678"
        method: GET
        timeout: 10
      register: n8n_accessibility
      ignore_errors: true

    - name: "=== DOSTĘPNOŚĆ N8N ==="
      debug:
        msg: "Status HTTP: {{ n8n_accessibility.status | default('NIEDOSTĘPNY') }}"

    # ==========================================================================
    # PODSUMOWANIE
    # ==========================================================================
    
    - name: "=== PODSUMOWANIE KONFIGURACJI ==="
      debug:
        msg:
          - "Analiza konfiguracji n8n zakończona"
          - "Główny plik: /home/<USER>/projekty/n8n/docker-compose.yml"
          - "Status n8n: {{ 'DZIAŁA' if n8n_accessibility.status == 200 else 'NIE DZIAŁA' }}"
          - ""
          - "NASTĘPNY KROK: Przygotowanie nowej konfiguracji"
