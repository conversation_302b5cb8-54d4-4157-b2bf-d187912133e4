---
- name: Test komponentów systemu
  hosts: "{{ target_host | default('serwery_nowe') }}"
  become: true
  tasks:
    - name: Sprawdź status Docker
      ansible.builtin.systemd:
        name: docker
      register: docker_status
      tags: [docker, services]

    - name: Sprawdź status Nginx
      ansible.builtin.systemd:
        name: nginx
      register: nginx_status
      tags: [nginx, services]

    - name: Sprawdź kontenery Baserow
      ansible.builtin.shell: |
        cd /home/<USER>
      register: baserow_containers
      ignore_errors: true
      tags: [baserow, containers]

    - name: Wyświetl status komponentów
      ansible.builtin.debug:
        msg:
          - "Docker: {{ docker_status.status.ActiveState }}"
          - "Nginx: {{ nginx_status.status.ActiveState }}"
          - "Baserow containers: {{ baserow_containers.stdout_lines | default(['Brak']) }}"
      tags: [status, summary]