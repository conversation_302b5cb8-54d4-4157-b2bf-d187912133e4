---
- name: <PERSON><PERSON>fika<PERSON>ja działania n8n po aktualizacji
  hosts: simetria-ovh
  become: true
  gather_facts: true
  
  vars:
    n8n_config_dir: "/home/<USER>/projekty/n8n"
    
  tasks:
    - name: "=== WERYFIKACJA N8N PO AKTUALIZACJI ==="
      debug:
        msg:
          - "Weryfikuję działanie n8n po aktualizacji na serwerze {{ ansible_hostname }}"
          - "Data: {{ ansible_date_time.iso8601 }}"

    # ==========================================================================
    # SPRAWDZENIE STATUSU KONTENERÓW
    # ==========================================================================
    
    - name: "Sprawdź status kontenerów"
      shell: |
        cd {{ n8n_config_dir }}
        echo "=== STATUS KONTENERÓW ==="
        docker-compose ps
        echo ""
        echo "=== SZCZEGÓŁY KONTENERÓW ==="
        docker ps --filter "name=n8n"
      register: container_status
      become_user: szymcio

    - name: "=== STATUS KONTENERÓW ==="
      debug:
        msg: "{{ container_status.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE WERSJI
    # ==========================================================================
    
    - name: "Sprawdź wersję n8n"
      shell: docker exec n8n n8n --version
      register: n8n_version

    - name: "Sprawdź obraz Docker"
      shell: docker inspect n8n --format='{{.Config.Image}}'
      register: n8n_image

    - name: "=== WERSJA N8N ==="
      debug:
        msg:
          - "Wersja aplikacji: {{ n8n_version.stdout }}"
          - "Obraz Docker: {{ n8n_image.stdout }}"

    # ==========================================================================
    # TEST DOSTĘPNOŚCI HTTP
    # ==========================================================================
    
    - name: "Test dostępności HTTP"
      uri:
        url: "http://127.0.0.1:5678"
        method: GET
        timeout: 10
      register: http_test

    - name: "=== TEST HTTP ==="
      debug:
        msg: 
          - "Status HTTP: {{ http_test.status }}"
          - "Czas odpowiedzi: {{ http_test.elapsed }}s"
          - "Content-Type: {{ http_test.content_type }}"

    # ==========================================================================
    # SPRAWDZENIE LOGÓW
    # ==========================================================================
    
    - name: "Sprawdź logi n8n (ostatnie 30 linii)"
      shell: docker logs n8n --tail 30
      register: n8n_logs
      ignore_errors: true

    - name: "=== LOGI N8N ==="
      debug:
        msg: "{{ n8n_logs.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE BAZY DANYCH
    # ==========================================================================
    
    - name: "Test połączenia z bazą danych"
      shell: |
        docker exec n8n_postgres psql -U n8n_user -d n8n_db -c "SELECT version();" 2>/dev/null || echo "Błąd połączenia z bazą"
      register: db_test
      ignore_errors: true

    - name: "=== TEST BAZY DANYCH ==="
      debug:
        msg: "{{ db_test.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE WOLUMINÓW
    # ==========================================================================
    
    - name: "Sprawdź woluminy n8n"
      shell: |
        echo "=== WOLUMINY N8N ==="
        docker volume ls | grep n8n
        echo ""
        echo "=== ROZMIARY WOLUMINÓW ==="
        for volume in $(docker volume ls --filter "name=n8n" --format "{{.Name}}"); do
          echo "Wolumen: $volume"
          docker run --rm -v $volume:/data alpine du -sh /data 2>/dev/null || echo "Błąd sprawdzania $volume"
        done
      register: volumes_check
      ignore_errors: true

    - name: "=== WOLUMINY ==="
      debug:
        msg: "{{ volumes_check.stdout_lines }}"

    # ==========================================================================
    # TEST HEALTHCHECK
    # ==========================================================================
    
    - name: "Sprawdź healthcheck kontenerów"
      shell: |
        echo "=== HEALTHCHECK N8N ==="
        docker inspect n8n --format='{{.State.Health.Status}}' 2>/dev/null || echo "Brak healthcheck"
        echo ""
        echo "=== HEALTHCHECK POSTGRES ==="
        docker inspect n8n_postgres --format='{{.State.Health.Status}}' 2>/dev/null || echo "Brak healthcheck"
      register: healthcheck_status

    - name: "=== HEALTHCHECK ==="
      debug:
        msg: "{{ healthcheck_status.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE KONFIGURACJI
    # ==========================================================================
    
    - name: "Sprawdź aktualną konfigurację"
      shell: |
        cd {{ n8n_config_dir }}
        echo "=== AKTUALNA KONFIGURACJA ==="
        echo "Plik docker-compose.yml:"
        head -10 docker-compose.yml
        echo ""
        echo "=== PLIKI BACKUP ==="
        ls -la *.backup.* 2>/dev/null || echo "Brak plików backup"
      register: config_check
      become_user: szymcio

    - name: "=== KONFIGURACJA ==="
      debug:
        msg: "{{ config_check.stdout_lines }}"

    # ==========================================================================
    # PODSUMOWANIE WERYFIKACJI
    # ==========================================================================
    
    - name: "=== PODSUMOWANIE WERYFIKACJI ==="
      debug:
        msg:
          - "Weryfikacja n8n zakończona"
          - ""
          - "WYNIKI:"
          - "✅ Wersja n8n: {{ n8n_version.stdout }}"
          - "✅ Obraz Docker: {{ n8n_image.stdout }}"
          - "✅ Status HTTP: {{ http_test.status }}"
          - "✅ Healthcheck n8n: {{ healthcheck_status.stdout_lines[1] | default('Nieznany') }}"
          - "✅ Healthcheck PostgreSQL: {{ healthcheck_status.stdout_lines[3] | default('Nieznany') }}"
          - ""
          - "{{ '🎉 AKTUALIZACJA ZAKOŃCZONA SUKCESEM!' if (n8n_version.stdout == '1.103.2' and http_test.status == 200) else '⚠️  SPRAWDŹ PROBLEMY POWYŻEJ' }}"
          - ""
          - "DOSTĘP DO N8N:"
          - "- Lokalny: http://127.0.0.1:5678"
          - "- Publiczny: https://n8n.simetria.pl"
          - ""
          - "W RAZIE PROBLEMÓW:"
          - "- Sprawdź logi: docker logs n8n"
          - "- Restart: cd {{ n8n_config_dir }} && docker-compose restart"
          - "- Rollback: cd {{ n8n_config_dir }} && cp docker-compose.yml.backup.* docker-compose.yml && docker-compose up -d"
