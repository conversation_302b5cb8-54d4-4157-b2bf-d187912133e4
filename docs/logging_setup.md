# System Logowania i Monitoringu dla Baserow

## Przegląd

System logowania i monitoringu dla infrastruktury Baserow składa się z dwóch głównych komponentów:

1. **Serwer centralny** (`logging_central`) - centralizuje wszystkie logi i metryki
2. **Agenci logowania** (`logging_agent`) - zbierają logi i metryki z serwerów aplikacji

## Architektura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Baserow App   │    │   Baserow App   │    │   Other Server  │
│   + Agent       │    │   + Agent       │    │   + Agent       │
│                 │    │                 │    │                 │
│  ┌─────────────┐│    │  ┌─────────────┐│    │  ┌─────────────┐│
│  │  Promtail   ││    │  │  Promtail   ││    │  │  Promtail   ││
│  │ Node Exp.   ││    │  │ Node Exp.   ││    │  │ Node Exp.   ││
│  │ PG Exp.     ││    │  │ PG Exp.     ││    │  │             ││
│  └─────────────┘│    │  └─────────────┘│    │  └─────────────┘│
└─────────────────┘    └─────────────────┘    └─────────────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                     ┌─────────────────┐
                     │ Central Server  │
                     │                 │
                     │  ┌─────────────┐│
                     │  │  Grafana    ││
                     │  │  Loki       ││
                     │  │  Prometheus ││
                     │  │  AlertMngr  ││
                     │  └─────────────┘│
                     └─────────────────┘
```

## Komponenty

### Serwer Centralny (logging_central)

**Lokalizacja:** `monitoring.simetria.pl`

**Serwisy:**
- **Grafana** (port 3000) - wizualizacja danych i dashboardy
- **Loki** (port 3100) - agregacja i przechowywanie logów
- **Prometheus** (port 9090) - zbieranie i przechowywanie metryk
- **AlertManager** (port 9093) - zarządzanie alertami

**Porty:**
- 3000/tcp - Grafana Web UI
- 3100/tcp - Loki API
- 9090/tcp - Prometheus Web UI
- 9093/tcp - AlertManager Web UI

### Agent Logowania (logging_agent)

**Zainstalowany na:** wszystkich serwerach aplikacji

**Komponenty:**
- **Promtail** (port 9080) - zbieranie logów
- **Node Exporter** (port 9100) - metryki systemu
- **Postgres Exporter** (port 9187) - metryki PostgreSQL
- **Redis Exporter** (port 9121) - metryki Redis
- **Nginx Exporter** (port 9113) - metryki Nginx

## Instalacja Systemu Logowania

### Wymagania wstępne

1. **System operacyjny**: Ubuntu 20.04+ / Debian 11+
2. **Pamięć RAM**: Minimum 2GB dla serwera centralnego, 512MB dla agentów
3. **Porty**: Porty muszą być dostępne między serwerami
4. **Docker**: Zainstalowany Docker i Docker Compose

### 1. Instalacja Serwera Centralnego

#### Krok 1: Konfiguracja inventory

Dodaj serwer monitoringu do pliku inventory:

```ini
[monitoring_server]
monitoring.simetria.pl ansible_host=*************

[monitoring_server:vars]
ansible_user=root
ansible_ssh_private_key_file=~/.ssh/id_rsa
```

#### Krok 2: Konfiguracja zmiennych

Utwórz plik konfiguracyjny dla serwera centralnego:

```yaml
# group_vars/monitoring_server.yml
grafana_admin_password: "your_secure_password_here"
loki_retention_period: "720h"  # 30 dni
prometheus_retention_time: "15d"
alertmanager_smtp_server: "smtp.gmail.com"
alertmanager_email: "<EMAIL>"
alertmanager_smtp_port: 587
alertmanager_smtp_username: "<EMAIL>"
alertmanager_smtp_password: "your_app_password"
```

#### Krok 3: Instalacja

```bash
# Instalacja serwera centralnego
ansible-playbook site.yml --limit monitoring_server --tags central

# Sprawdzenie statusu
ansible monitoring.simetria.pl -m shell -a "docker-compose ps" -i inventory
```

### 2. Instalacja Agentów Logowania

#### Krok 1: Konfiguracja zmiennych

```yaml
# group_vars/baserow_servers.yml
logging_enabled: true
loki_url: "http://monitoring.simetria.pl:3100"
prometheus_url: "http://monitoring.simetria.pl:9090"
logging_agent_user: "logging-agent"
```

#### Krok 2: Instalacja agentów

```bash
# Instalacja na wszystkich serwerach Baserow
ansible-playbook site.yml --limit baserow_servers --tags agent

# Instalacja na pojedynczym serwerze
ansible-playbook site.yml --limit baserow1.simetria.pl --tags agent
```

#### Krok 3: Weryfikacja instalacji

```bash
# Sprawdzenie statusu agentów
ansible baserow_servers -m shell -a "systemctl status promtail" -i inventory

# Test połączenia z Loki
ansible baserow_servers -m shell -a "curl -s http://monitoring.simetria.pl:3100/ready" -i inventory
```

## Przykładowe Dashboardy

### 1. Dashboard System Monitoring

**URL:** `https://monitoring.simetria.pl:3000/d/system-monitoring`

**Metryki:**
- CPU Usage (%) - w czasie rzeczywistym
- Memory Usage (%) - wykorzystanie RAM
- Disk Usage (%) - wykorzystanie dysku
- Network I/O - ruch sieciowy
- Load Average - obciążenie systemu
- Top Processes - najbardziej obciążające procesy

**Komendy do szybkiego podglądu:**
```bash
# CPU usage
curl -s "http://monitoring.simetria.pl:9090/api/v1/query?query=100-(avg+by+(instance)+(irate(node_cpu_seconds_total{mode='idle'}[5m]))*100)" | jq

# Memory usage
curl -s "http://monitoring.simetria.pl:9090/api/v1/query?query=(node_memory_MemTotal_bytes-node_memory_MemAvailable_bytes)/node_memory_MemTotal_bytes*100" | jq
```

### 2. Dashboard Baserow Application

**URL:** `https://monitoring.simetria.pl:3000/d/baserow-app`

**Metryki:**
- HTTP Requests/sec - liczba żądań HTTP
- Response Time - czas odpowiedzi
- Error Rate - współczynnik błędów
- Active Connections - aktywne połączenia
- Database Connections - połączenia PostgreSQL
- Redis Operations - operacje Redis

**Przykładowe zapytania:**
```bash
# HTTP requests rate
rate(nginx_http_requests_total[5m])

# Database connections
pg_stat_activity_count{datname="baserow"}

# Redis operations
rate(redis_commands_processed_total[5m])
```

### 3. Dashboard Logs Overview

**URL:** `https://monitoring.simetria.pl:3000/d/logs-explorer`

**Funkcje:**
- Real-time logi z wszystkich serwerów
- Filtrowanie po poziomie logów (INFO, WARN, ERROR)
- Wyszukiwanie po słowach kluczowych
- Agregacja logów po aplikacjach
- Historia logów (30 dni)

**Przykładowe zapytania Loki:**
```bash
# Wszystkie logi z błędami
{job="varlogs"} |= "ERROR"

# Logi z konkretnego serwera
{instance="baserow1.simetria.pl"}

# Logi PostgreSQL
{job="postgres-exporter"}
```

## Konfiguracja Alertów

### 1. Konfiguracja Alertmanager

Edytuj plik konfiguracyjny:

```yaml
# roles/logging_central/templates/alertmanager.yml.j2
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'email-alerts'

receivers:
- name: 'email-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      Instance: {{ .Labels.instance }}
      {{ end }}
```

### 2. Reguły alertów Prometheus

```yaml
# roles/logging_central/templates/prometheus.yml.j2
groups:
- name: system-alerts
  rules:
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on {{ $labels.instance }}"
      description: "CPU usage is above 80% for 5 minutes"

  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage on {{ $labels.instance }}"
      description: "Memory usage is above 85% for 5 minutes"

  - alert: DiskSpaceLow
    expr: (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"} * 100) < 10
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Disk space low on {{ $labels.instance }}"
      description: "Less than 10% disk space available"

  - alert: ServiceDown
    expr: up == 0
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Service {{ $labels.job }} is down"
      description: "Service has been down for more than 2 minutes"
```

### 3. Testowanie alertów

```bash
# Test wysyłania emaila
curl -X POST http://monitoring.simetria.pl:9093/api/v1/alerts \
  -H "Content-Type: application/json" \
  -d '[{"labels":{"alertname":"test"},"annotations":{"summary":"Test alert"}}]'

# Sprawdzenie statusu alertów
curl http://monitoring.simetria.pl:9093/api/v1/alerts | jq

# Manualne wywołanie alertu
echo "node_cpu_seconds_total{cpu=\"0\",mode=\"idle\"} 0" | curl --data-binary @- http://monitoring.simetria.pl:9090/metrics
```

## Przykładowe Komendy

### 1. Podstawowe komendy zarządzania

```bash
# Status wszystkich serwisów
ansible all -m shell -a "systemctl status promtail node-exporter" -i inventory

# Restart wszystkich agentów
ansible baserow_servers -m shell -a "systemctl restart promtail" -i inventory

# Sprawdzenie logów na wszystkich serwerach
ansible all -m shell -a "tail -n 50 /var/log/syslog" -i inventory

# Test połączenia z Loki
curl -s http://monitoring.simetria.pl:3100/ready | jq
```

### 2. Query Prometheus

```bash
# Lista wszystkich metryk
curl -s http://monitoring.simetria.pl:9090/api/v1/label/__name__/values | jq

# Wartość konkretnej metryki
curl -s "http://monitoring.simetria.pl:9090/api/v1/query?query=up" | jq

# Metryki z konkretnego serwera
curl -s "http://monitoring.simetria.pl:9090/api/v1/query?query=up{instance='baserow1.simetria.pl'}" | jq

# Historia metryk
curl -s "http://monitoring.simetria.pl:9090/api/v1/query_range?query=node_cpu_seconds_total&start=$(date -d '1 hour ago' +%s)&end=$(date +%s)&step=300" | jq
```

### 3. Query Loki

```bash
# Lista etykiet
curl -s http://monitoring.simetria.pl:3100/loki/api/v1/labels | jq

# Wartości etykiety
curl -s "http://monitoring.simetria.pl:3100/loki/api/v1/label/instance/values" | jq

# Wyszukiwanie logów
curl -G -s "http://monitoring.simetria.pl:3100/loki/api/v1/query_range" \
  --data-urlencode 'query={job="varlogs"}' \
  --data-urlencode 'start=1609459200000000000' \
  --data-urlencode 'end=1609545600000000000' | jq
```

### 4. Grafana CLI

```bash
# Reset hasła admina
docker exec grafana grafana-cli admin reset-admin-password new_password

# Lista dashboardów
curl -s "http://admin:<EMAIL>:3000/api/search" | jq

# Export dashboardu
curl -s "http://admin:<EMAIL>:3000/api/dashboards/uid/DASHBOARD_UID" | jq
```

### 5. Maintenance

```bash
# Czyszczenie starych danych Prometheus
curl -X POST http://monitoring.simetria.pl:9090/api/v1/admin/tsdb/clean_tombstones

# Backup konfiguracji
tar -czf grafana-backup.tar.gz /opt/monitoring/grafana/data

# Restore konfiguracji
tar -xzf grafana-backup.tar.gz -C /opt/monitoring/grafana/
```

## Integracja z Baserow

### Konfiguracja logowania w Baserow

```yaml
# roles/baserow_deployment/templates/docker-compose.yml.j2
services:
  backend:
    environment:
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - LOKI_URL=http://monitoring.simetria.pl:3100
      
  nginx:
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - /var/log/nginx:/var/log/nginx
```

### Monitoring aplikacji Baserow

```bash
# Sprawdzenie metryk aplikacji
curl http://baserow.simetria.pl:3000/api/health/

# Logi aplikacji
docker-compose logs -f backend

# Metryki PostgreSQL
docker-compose exec postgres psql -U baserow -c "SELECT * FROM pg_stat_activity;"
```

## Rozwiązywanie problemów

### Częste problemy i rozwiązania

1. **Promtail nie startuje**
   ```bash
   # Sprawdź konfigurację
   sudo promtail -config.file=/etc/logging-agent/promtail-config.yml -dry-run
   
   # Sprawdź uprawnienia
   sudo ls -la /var/log/syslog
   ```

2. **Brak danych w Grafana**
   ```bash
   # Sprawdź źródła danych
   curl http://monitoring.simetria.pl:3000/api/datasources
   
   # Test połączenia
   curl http://monitoring.simetria.pl:3100/ready
   ```

3. **Alertmanager nie wysyła emaili**
   ```bash
   # Test SMTP
   curl -v smtp://smtp.gmail.com:587
   
   # Sprawdź logi
   docker-compose logs alertmanager
   ```

4. **Wysokie zużycie zasobów**
   ```bash
   # Monitoruj użycie
   docker stats
   
   # Optymalizacja Prometheus
   curl -X POST http://monitoring.simetria.pl:9090/api/v1/admin/tsdb/snapshot
   ```

## Backup i Restore

### Backup konfiguracji

```bash
# Backup Grafana
docker exec grafana tar -czf /tmp/grafana-backup.tar.gz /var/lib/grafana

# Backup Prometheus
docker exec prometheus tar -czf /tmp/prometheus-backup.tar.gz /prometheus

# Backup Loki
docker exec loki tar -czf /tmp/loki-backup.tar.gz /loki
```

### Restore

```bash
# Restore Grafana
docker run --rm -v grafana-data:/var/lib/grafana -v $(pwd):/backup alpine sh -c "tar -xzf /backup/grafana-backup.tar.gz -C /"

# Restore Prometheus
docker run --rm -v prometheus-data:/prometheus -v $(pwd):/backup alpine sh -c "tar -xzf /backup/prometheus-backup.tar.gz -C /prometheus"
```

## Monitoring bezpieczeństwa

### Firewall rules

```bash
# Otwórz porty dla monitoringu
sudo ufw allow from 192.168.1.0/24 to any port 3000
sudo ufw allow from 192.168.1.0/24 to any port 3100
sudo ufw allow from 192.168.1.0/24 to any port 9090
```

### SSL/TLS

```bash
# Konfiguracja SSL dla Grafana
docker exec grafana grafana-cli --homepath=/usr/share/grafana admin reset-admin-password --password="new_secure_password"
```

## Skalowanie

### Dodanie nowego serwera

1. Dodaj serwer do inventory
2. Uruchom playbook z tagami agent
3. Zweryfikuj w Grafana

### Performance tuning

```yaml
# roles/logging_central/defaults/main.yml
prometheus_storage_tsdb_retention_size: "10GB"
loki_limits_config:
  ingestion_rate_mb: 10
  ingestion_burst_size_mb: 20
```

---

*Dokumentacja wygenerowana automatycznie przez system Ansible*
*Ostatnia aktualizacja: 2024-12-15 10:30:00*