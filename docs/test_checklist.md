# Checklista Testów - Projekt Baserow

## Przed wdrożeniem - obowiązkowe testy

### ✅ Testy składni Ansible
- [x] `ansible-playbook --syntax-check baserow_production.yml`
- [x] `ansible-playbook --syntax-check site.yml`
- [x] Wszystkie role przeszły test składni

### ✅ Testy konfiguracji
- [x] `ansible-playbook --check --diff baserow_production.yml -i inventory_production`
- [x] `ansible-playbook test_logging_agent.yml -i inventory_production`

### ✅ Testy integracyjne
- [x] Test backupu GPG: `./full_backup.sh --test`
- [x] Test weryfikacji backupów: `./verify_backup.sh --test`
- [x] Test systemu logowania: `curl http://localhost:3000/api/health`

### ✅ Testy bezpieczeństwa
- [x] Konfiguracja SSL: `openssl s_client -connect baserow.simetria.pl:443`
- [x] Nagłówki bezpieczeństwa: `curl -I https://baserow.simetria.pl`

## Testy wydajnościowe (do wykonania na serwerze produkcyjnym)

### ⏳ Testy wydajności backupów
- [ ] `time ./full_backup.sh`
- [ ] Monitorowanie zużycia CPU podczas backupu
- [ ] Monitorowanie zużycia pamięci podczas backupu
- [ ] Monitorowanie wykorzystania dysku

### ⏳ Testy wydajności weryfikacji
- [ ] `time ./verify_backup.sh`
- [ ] Czas weryfikacji pełnego backupu
- [ ] Czas weryfikacji backupu przyrostowego

### ⏳ Testy wydajności aplikacji
- [ ] Test obciążenia API: `ab -n 1000 -c 10 https://baserow.simetria.pl/api/health`
- [ ] Test czasu odpowiedzi: `curl -w "@curl-format.txt" -o /dev/null https://baserow.simetria.pl`
- [ ] Test wydajności bazy danych

## Testy po wdrożeniu

### 🔲 Testy funkcjonalne
- [ ] Logowanie do aplikacji Baserow
- [ ] Tworzenie nowej bazy danych
- [ ] Import danych testowych
- [ ] Eksport danych
- [ ] Tworzenie backupu ręcznego

### 🔲 Testy monitoringu
- [ ] Sprawdzenie dashboardów Grafana
- [ ] Test alertów (symulacja błędu)
- [ ] Sprawdzenie logów w Loki
- [ ] Weryfikacja metryk Prometheus

### 🔲 Testy backupów
- [ ] Automatyczny backup dzienny
- [ ] Przywracanie z backupu
- [ ] Weryfikacja integralności backupów
- [ ] Test rotacji backupów

## Komendy do szybkiej weryfikacji

```bash
# Status usług
docker-compose -f /opt/baserow/docker-compose.yml ps

# Logi aplikacji
docker-compose -f /opt/baserow/docker-compose.yml logs -f

# Status backupu
ls -la /opt/baserow/backups/

# Test SSL
curl -I https://baserow.simetria.pl

# Test API
curl https://baserow.simetria.pl/api/health

# Test logowania
curl -X POST http://localhost:3000/api/token-auth/ \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

## Procedura awaryjna

### W przypadku błędów backupu
1. Sprawdź logi: `tail -f /var/log/baserow/backup.log`
2. Zweryfikuj klucze GPG: `gpg --list-keys`
3. Sprawdź dostępność miejsca na dysku: `df -h`
4. Uruchom testowy backup: `./full_backup.sh --test`

### W przypadku błędów SSL
1. Sprawdź status certyfikatu: `certbot certificates`
2. Odśwież certyfikat: `certbot renew --force-renewal`
3. Restart nginx: `systemctl restart nginx`

### W przypadku błędów aplikacji
1. Sprawdź status kontenerów: `docker-compose ps`
2. Zrestartuj usługi: `docker-compose restart`
3. Sprawdź logi: `docker-compose logs -f`

## Kontakt w razie problemów

- **Monitoring**: https://baserow.simetria.pl:3000 (Grafana)
- **Logi**: https://baserow.simetria.pl:3100 (Loki)
- **Alerty**: skonfigurowane <NAME_EMAIL>

---
**Ostatnia aktualizacja:** 15.07.2025  
**Wersja:** 1.0