# Plan Migracji Baserow i Wdrożenia Monitorowania v2.0

## Obecny Stan Projektu

### Serwery w Inventarzu
- **simetria-hetzner001** (5.75.181.53) - nowy serwer <PERSON>tzner dla Baserow
- **simetria-ovh** (57.128.194.215:1988) - stary serwer z obecnym Baserow
- **sms-server** - serwer SMS przez Cloudflare Access

### Obecne Zabezpieczenia (Bardzo Dobre!)
✅ **UFW Firewall** - domyślna polityka DENY  
✅ **Fail2ban** - ochrona przed bruteforce  
✅ **SSH Hardening** - wyłączenie root login i logowania hasłem  
✅ **AIDE** - monitoring integralności plików  
✅ **Auditd** - logowanie zdarzeń systemowych  
✅ **Sysctl Hardening** - bezpieczeństwo kernela

## Nowe Funkcjonalności w wersji 2.0

### 🔐 Szyfrowanie GPG Backupów
- Automatyczne szyfrowanie wszystkich backupów przed wysłaniem
- Konfiguracja kluczy GPG dla bezpiecznego przechowywania
- Integracja z systemem backup

### ✅ Automatyczna Weryfikacja Backupów
- Codzienna weryfikacja integralności backupów
- Sprawdzanie poprawności plików GPG
- Raportowanie statusu weryfikacji

### 🛡️ Wzmocnione Nagłówki SSL
- HSTS (HTTP Strict Transport Security)
- CSP (Content Security Policy)
- X-Frame-Options
- X-Content-Type-Options
- Referrer-Policy

### 📊 Centralny System Logowania
- Loki + Grafana dla centralizacji logów
- Promtail na wszystkich serwerach
- Dashboardy dla aplikacji i systemu
- Alerting dla krytycznych błędów

## Plan Działania - Zaktualizowany

### Faza 0: Przygotowanie i Planowanie
- [x **Analiza nowych wymagań** - GPG, weryfikacja, SSL, logging
- [x **Projektowanie architektury** - integracja nowych komponentów
- [x **Przygotowanie ról Ansible** - nowe role i modyfikacje

### Faza 1: Diagnostyka i Analiza
- [ ] **Sprawdzenie stanu Baserow na OVH** - kontenery Docker, volumes, baza danych
- [ ] **Analiza rozmiaru danych** - planowanie migracji
- [ ] **Identyfikacja konfiguracji** - docker-compose, zmienne środowiskowe
- [ ] **Audit Netdata** - przygotowanie do usunięcia
- [ ] **Sprawdzenie kluczy GPG** - przygotowanie do szyfrowania

### Faza 2: Projektowanie Architektury v2.0

#### Baserow na Nowym Serwerze v2.0
```
┌─────────────────────────────────────────────────────────┐
│                    Internet                             │
└─────────────────┬───────────────────────────────────────┘
│                 HTTPS (443)                            │
│                                                      │
┌─────────────────▼─────────────────────────────────────┐
│              Nginx Reverse Proxy                        │
│          (SSL Hardening, HSTS, CSP)                    │
│          (Rate Limiting, Compression)                  │
└─────────────────┬───────────────────────────────────────┘
│                 │
┌─────────────────▼─────────────────────────────────────┐
│               Baserow Container                        │
│              (baserow.simetria.pl)                   │
│              (Logging to Loki)                       │
└─────────────────┬───────────────────────────────────────┘
│                 │
┌─────────────────▼─────────────────────────────────────┐
│            PostgreSQL Container                        │
│           (Persistent Volume)                          │
│           (Postgres Exporter)                          │
└─────────────────┬───────────────────────────────────────┘
│                 │
┌─────────────────▼─────────────────────────────────────┐
│               Redis Container                          │
│            (Cache + Session Store)                   │
│            (Redis Exporter)                          │
└─────────────────────────────────────────────────────┘
```

#### System Monitorowania v2.0
```
┌─────────────────────────────────────────────────────────┐
│                Grafana Dashboard                        │
│              (Centralne UI + Alerting)                  │
└─────────────────┬───────────────────────────────────────┘
│                 │
┌─────────────────▼─────────────────────────────────────┐
│            Prometheus Server                           │
│          (Metryki + Alertmanager)                    │
│          (Backup metryk)                              │
└──┬──────────────┬──────────────────┬──────────────────┘
│  │              │                  │
│  ▼              ▼                  ▼
┌──────┐    ┌──────┐          ┌──────┐
│Hetzner│    │ OVH  │          │ SMS  │
│Node   │    │Node  │          │Node  │
│Exporter│    │Exporter│      │Exporter│
│Loki   │    │Loki  │          │Loki  │
└──────┘    └──────┘          └──────┘
```

#### System Backup v2.0
```
┌─────────────────────────────────────────────────────────┐
│              Backup System                              │
│         (GPG Encryption + Verification)                │
├─────────────────────────────────────────────────────────┤
│ 1. Backup bazy danych (PostgreSQL)                    │
│ 2. Backup plików aplikacji                            │
│ 3. Szyfrowanie GPG                                    │
│ 4. Weryfikacja integralności                          │
│ 5. Upload do bezpiecznego storage                    │
│ 6. Powiadomienia email                               │
└─────────────────────────────────────────────────────┘
```

### Faza 3: Role Ansible - Zaktualizowane

#### Role 1: `baserow_deployment` v2.0
- ✅ Instalacja Docker i Docker Compose
- ✅ Konfiguracja PostgreSQL z optymalizacją RAM
- ✅ Deployment Baserow z Redis cache
- ✅ Konfiguracja Nginx reverse proxy
- ✅ SSL z Let's Encrypt (Certbot)
- ✅ **Automatyczne backupy z GPG encryption**
- ✅ **Wzmocnione nagłówki SSL**
- ✅ **Integracja z centralnym logowaniem**

#### Role 2: `logging_central` v2.0
- ✅ Instalacja Prometheus + Grafana + Alertmanager
- ✅ Konfiguracja Loki dla logów
- ✅ Dashboardy Grafana dla infrastruktury
- ✅ Alerting (email/Slack) dla krytycznych problemów
- ✅ **Backup konfiguracji monitoringu**

#### Role 3: `logging_agent` v2.0
- ✅ Instalacja Promtail na wszystkich serwerach
- ✅ Konfiguracja Node Exporter
- ✅ Konfiguracja Postgres Exporter
- ✅ Konfiguracja Redis Exporter
- ✅ Konfiguracja Nginx Exporter

#### Role 4: `security_hardening` v2.0
- ✅ **Konfiguracja GPG dla szyfrowania backupów**
- ✅ **Instalacja skryptów weryfikacji backupów**
- ✅ **Ulepszone zabezpieczenia SSL/TLS**

#### Role 5: `netdata_cleanup`
- ✅ Bezpieczne usunięcie Netdata z OVH
- ✅ Czyszczenie pozostałości konfiguracyjnych
- ✅ Weryfikacja braku wpływu na inne usługi

### Faza 4: Bezpieczeństwo i Optymalizacja v2.0

#### Dodatkowe Zabezpieczenia dla Baserow v2.0
- ✅ **GPG Encryption** dla backupów
- ✅ **Automatyczna weryfikacja** integralności backupów
- ✅ **Wzmocnione nagłówki SSL** (HSTS, CSP, X-Frame-Options)
- ✅ **Rate limiting** w Nginx
- ✅ **Monitoring bezpieczeństwa** z Fail2ban
- ✅ **Centralne logowanie** dla audytu

#### Optymalizacja Wydajności v2.0
- ✅ PostgreSQL tuning dla RAM
- ✅ Redis cache optimization
- ✅ Nginx compression i caching
- ✅ Log rotation z centralnym logowaniem
- ✅ **Optymalizacja dla ARM64** (Hetzner ARM)

### Faza 5: Migracja Danych - Zaktualizowana

#### Strategia Migracji z nowymi funkcjonalnościami
1. **Mała baza (<1GB)**: Dump → Transfer → Restore → Konfiguracja v2.0
2. **Średnia baza (1-5GB)**: Planowane okno maintenance + konfiguracja v2.0
3. **Duża baza (>5GB)**: Migracja etapami z replikacją + konfiguracja v2.0

#### Checklista Przed Migracją v2.0

### ✅ Sprawdzenie wstępne
- [ ] **Backup aktualnej konfiguracji** OVH
- [ ] **Sprawdzenie dostępności** nowego serwera Hetzner
- [ ] **Weryfikacja DNS** - przygotowanie do przełączenia
- [ ] **Sprawdzenie kluczy GPG** - przygotowanie do szyfrowania
- [ ] **Test połączenia** z serwerem monitoringu

### ✅ Przygotowanie nowego serwera
- [ ] **Instalacja systemu** na Hetzner
- [ ] **Konfiguracja SSH** i użytkowników
- [ ] **Instalacja Dockera** i Docker Compose
- [ ] **Konfiguracja firewall** (UFW)
- [ ] **Instalacja monitoringu** (Node Exporter)
- [ ] **Konfiguracja GPG** dla szyfrowania
- [ ] **Test weryfikacji backupów**

### ✅ Przygotowanie migracji
- [ ] **Analiza rozmiaru** bazy danych
- [ ] **Planowanie okna** maintenance
- [ ] **Przygotowanie playbook** migracji
- [ ] **Test konfiguracji** SSL i wzmocnionych nagłówków
- [ ] **Konfiguracja alertów** dla migracji

### ✅ Kroki Migracji - Szczegółowo

#### Krok 1: Backup i szyfrowanie (OVH)
```bash
# Backup bazy danych z szyfrowaniem
cd /opt/baserow
./scripts/backup_database.sh --encrypt --gpg-key <EMAIL>

# Backup plików aplikacji
./scripts/backup_media.sh --encrypt --gpg-key <EMAIL>

# Weryfikacja backupów
./scripts/verify_backup.sh --check-all
```

#### Krok 2: Przygotowanie nowego serwera (Hetzner)
```bash
# Instalacja całego stacka v2.0
ansible-playbook site.yml --limit simetria-hetzner001

# Weryfikacja instalacji
ansible-playbook test_baserow.yml --limit simetria-hetzner001
```

#### Krok 3: Transfer danych
```bash
# Transfer backupów z szyfrowaniem
rsync -avz --progress \
  /opt/baserow/backups/encrypted/ \
  root@simetria-hetzner001:/opt/baserow/backups/

# Weryfikacja transferu
ssh simetria-hetzner001 "cd /opt/baserow && ./scripts/verify_backup.sh --check-all"
```

#### Krok 4: Restore i konfiguracja
```bash
# Restore bazy danych
./scripts/restore_backup.sh --database --decrypt --gpg-key <EMAIL>

# Restore plików
./scripts/restore_backup.sh --media --decrypt --gpg-key <EMAIL>

# Konfiguracja SSL i wzmocnionych nagłówków
./scripts/configure_ssl_hardening.sh

# Konfiguracja centralnego logowania
./scripts/setup_logging.sh --loki-server monitoring.simetria.pl
```

#### Krok 5: Testy i przełączenie
```bash
# Testy funkcjonalne
curl -I https://baserow.simetria.pl
./scripts/test_baserow.sh --full

# Test weryfikacji backupów
./scripts/verify_backup.sh --test-restore

# Przełączenie DNS
# Monitorowanie przez 24h
```

### Faza 6: Monitoring i Maintenance v2.0

#### Konfiguracja Alertów v2.0
- **Krytyczne**: Down serwera, brak miejsca na dysku (>90%), błędy backupów
- **Ostrzeżenie**: Wysokie CPU (>80%), RAM (>85%), błędy weryfikacji backupów
- **Info**: Restart usług, wykonane backupy, testy restore

#### Procedury Operacyjne v2.0
- ✅ **Codzienne backupy** z szyfrowaniem GPG i weryfikacją
- ✅ **Tygodniowe testy restore** z weryfikacją integralności
- ✅ **Miesięczne przeglądy** bezpieczeństwa i logów
- ✅ **Kwartalne** aktualizacje bezpieczeństwa
- ✅ **Roczne** przeglądy konfiguracji GPG

### Procedura Wycofywania v2.0

#### Szybki wycof (5 minut)
```bash
# Przywrócenie DNS do OVH
# Serwer OVH jest gotowy jako backup
```

#### Pełny wycof (30 minut)
```bash
# Restore z backupu OVH
# Przywrócenie konfiguracji OVH
# Wycofanie zmian DNS
```

#### Disaster recovery (2h)
```bash
# Użycie najnowszego backupu z szyfrowaniem
# Restore na nowy serwer
# Konfiguracja od podstaw z playbook
```

## Następne Kroki - Zaktualizowane

1. **Przejście do trybu Code** - implementacja playbook diagnostycznego
2. **Uruchomienie diagnostyki** na serwerze OVH z nowymi parametrami
3. **Test konfiguracji GPG** i weryfikacji backupów
4. **Test wzmocnionych nagłówków SSL**
5. **Test centralnego logowania** przed migracją
6. **Rozpoczęcie implementacji** ról Ansible v2.0

## Wymagania Techniczne - Zaktualizowane

### Nowy Serwer (simetria-hetzner001) v2.0
- ✅ **Docker & Docker Compose** - konteneryzacja
- ✅ **Nginx** - reverse proxy z wzmocnionymi nagłówkami
- ✅ **PostgreSQL 15+** - baza danych
- ✅ **Redis** - cache
- ✅ **Certbot** - SSL certificates
- ✅ **Node Exporter** - monitoring
- ✅ **GPG** - szyfrowanie backupów
- ✅ **Promtail** - log collection

### Wszystkie Serwery v2.0
- ✅ **Prometheus Node Exporter** - metryki systemowe
- ✅ **Promtail** - log collection do Loki
- ✅ **UFW** - firewall rules dla monitoringu
- ✅ **GPG** - klucze dla szyfrowania

## Korzyści Rozwiązania v2.0

✅ **Bezpieczeństwo**: GPG encryption + wzmocnione SSL + monitoring  
✅ **Niezawodność**: Automatyczne backupy + weryfikacja + restore tests  
✅ **Compliance**: Centralne logowanie + audyting + alerty  
✅ **Skalowalność**: Łatwe dodawanie nowych serwerów + role Ansible  
✅ **Optymalizacja**: ARM64 + cache + compression  
✅ **Recovery**: Disaster recovery z szyfrowanymi backupami

---

*Plan migracji zaktualizowany dla wersji 2.0 z nowymi funkcjonalnościami*
*Ostatnia aktualizacja: 2024-12-15*