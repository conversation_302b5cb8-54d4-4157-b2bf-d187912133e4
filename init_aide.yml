---
- name: Initialize AIDE database
  hosts: simetria-hetzner001
  become: true

  tasks:
    - name: Refresh apt cache and re-run update
      ansible.builtin.apt:
        update_cache: yes

    - name: Deploy AIDE configuration file
      ansible.builtin.copy:
        src: roles/security_hardening/files/aide.conf
        dest: /etc/aide.conf
        owner: root
        group: root
        mode: '0600'
        
    - name: Check if AIDE database already exists
      ansible.builtin.stat:
        path: /var/lib/aide/aide.db
      register: aide_db

    - name: Initialize new AIDE database if it does not exist
      ansible.builtin.command:
        cmd: /usr/bin/aide --config /etc/aide.conf --init
      when: not aide_db.stat.exists
      async: 7200
      poll: 15
      register: aide_init_result

    - name: Activate the new AIDE database
      ansible.builtin.copy:
        src: /var/lib/aide/aide.db.new
        dest: /var/lib/aide/aide.db
        remote_src: true
        owner: root
        group: root
        mode: '0600'
      when: aide_init_result.changed
