---
- name: <PERSON><PERSON><PERSON><PERSON> aktualizacji n8n
  hosts: simetria-ovh
  become: true
  gather_facts: true
  
  vars:
    n8n_config_dir: "/home/<USER>/projekty/n8n"
    
  tasks:
    - name: "=== WYKONANIE AKTUALIZACJI N8N ==="
      debug:
        msg:
          - "UWAGA: Rozpoczynam aktualizację n8n na serwerze {{ ansible_hostname }}"
          - "To spowoduje krótką przerwę w działaniu n8n"
          - "Data: {{ ansible_date_time.iso8601 }}"

    # ==========================================================================
    # SPRAWDZENIE STANU PRZED AKTUALIZACJĄ
    # ==========================================================================
    
    - name: "Sprawdź status n8n przed aktualizacją"
      shell: |
        cd {{ n8n_config_dir }}
        echo "=== STATUS PRZED AKTUALIZACJĄ ==="
        docker-compose ps
        echo ""
        echo "=== WERSJA N8N ==="
        docker exec n8n n8n --version 2>/dev/null || echo "Nie można sprawdzić wersji"
      register: status_before
      ignore_errors: true

    - name: "=== STATUS PRZED AKTUALIZACJĄ ==="
      debug:
        msg: "{{ status_before.stdout_lines }}"

    # ==========================================================================
    # ZATRZYMANIE KONTENERÓW
    # ==========================================================================
    
    - name: "Zatrzymaj kontenery n8n"
      shell: |
        cd {{ n8n_config_dir }}
        echo "Zatrzymywanie kontenerów..."
        docker-compose down
        echo "Kontenery zatrzymane"
      register: stop_result
      become_user: szymcio

    - name: "=== ZATRZYMANIE KONTENERÓW ==="
      debug:
        msg: "{{ stop_result.stdout_lines }}"

    # ==========================================================================
    # ZASTĄPIENIE KONFIGURACJI
    # ==========================================================================
    
    - name: "Zastąp konfigurację nową wersją"
      shell: |
        cd {{ n8n_config_dir }}
        echo "Zastępowanie docker-compose.yml..."
        cp docker-compose.yml.new docker-compose.yml
        echo "Konfiguracja zaktualizowana"
        echo ""
        echo "=== NOWA KONFIGURACJA ==="
        head -20 docker-compose.yml
      register: config_replace
      become_user: szymcio

    - name: "=== ZASTĄPIENIE KONFIGURACJI ==="
      debug:
        msg: "{{ config_replace.stdout_lines }}"

    # ==========================================================================
    # POBRANIE NOWYCH OBRAZÓW
    # ==========================================================================
    
    - name: "Pobierz nowe obrazy Docker"
      shell: |
        cd {{ n8n_config_dir }}
        echo "Pobieranie nowych obrazów..."
        docker-compose pull
        echo "Obrazy pobrane"
      register: pull_result
      become_user: szymcio

    - name: "=== POBIERANIE OBRAZÓW ==="
      debug:
        msg: "{{ pull_result.stdout_lines }}"

    # ==========================================================================
    # URUCHOMIENIE Z NOWĄ WERSJĄ
    # ==========================================================================
    
    - name: "Uruchom n8n z nową wersją"
      shell: |
        cd {{ n8n_config_dir }}
        echo "Uruchamianie kontenerów z nową wersją..."
        docker-compose up -d
        echo "Kontenery uruchomione"
      register: start_result
      become_user: szymcio

    - name: "=== URUCHOMIENIE KONTENERÓW ==="
      debug:
        msg: "{{ start_result.stdout_lines }}"

    # ==========================================================================
    # OCZEKIWANIE NA URUCHOMIENIE
    # ==========================================================================
    
    - name: "Oczekiwanie na uruchomienie n8n (60 sekund)"
      pause:
        seconds: 60

    - name: "Sprawdź status kontenerów po uruchomieniu"
      shell: |
        cd {{ n8n_config_dir }}
        echo "=== STATUS KONTENERÓW ==="
        docker-compose ps
        echo ""
        echo "=== LOGI N8N (ostatnie 20 linii) ==="
        docker logs n8n --tail 20 2>/dev/null || echo "Nie można pobrać logów"
      register: status_after
      become_user: szymcio
      ignore_errors: true

    - name: "=== STATUS PO URUCHOMIENIU ==="
      debug:
        msg: "{{ status_after.stdout_lines }}"

    # ==========================================================================
    # SPRAWDZENIE WERSJI
    # ==========================================================================
    
    - name: "Sprawdź nową wersję n8n"
      shell: |
        echo "Sprawdzanie wersji n8n..."
        sleep 10  # Dodatkowe oczekiwanie
        docker exec n8n n8n --version 2>/dev/null || echo "Kontener jeszcze się uruchamia..."
      register: new_version
      ignore_errors: true

    - name: "=== NOWA WERSJA N8N ==="
      debug:
        msg: "{{ new_version.stdout_lines }}"

    # ==========================================================================
    # TEST DOSTĘPNOŚCI
    # ==========================================================================
    
    - name: "Test dostępności n8n"
      uri:
        url: "http://127.0.0.1:5678"
        method: GET
        timeout: 10
      register: accessibility_test
      ignore_errors: true
      retries: 3
      delay: 10

    - name: "=== TEST DOSTĘPNOŚCI ==="
      debug:
        msg: "Status HTTP: {{ accessibility_test.status | default('NIEDOSTĘPNY') }}"

    # ==========================================================================
    # PODSUMOWANIE AKTUALIZACJI
    # ==========================================================================
    
    - name: "=== PODSUMOWANIE AKTUALIZACJI ==="
      debug:
        msg:
          - "Aktualizacja n8n zakończona"
          - ""
          - "STATUS:"
          - "- Kontenery: {{ 'DZIAŁAJĄ' if status_after.rc == 0 else 'PROBLEM' }}"
          - "- Dostępność HTTP: {{ 'OK' if accessibility_test.status == 200 else 'PROBLEM' }}"
          - "- Nowa wersja: {{ new_version.stdout | default('Sprawdź ręcznie') }}"
          - ""
          - "NASTĘPNY KROK: Weryfikacja działania n8n"
          - ""
          - "W RAZIE PROBLEMÓW:"
          - "- Sprawdź logi: docker logs n8n"
          - "- Przywróć backup: cd {{ n8n_config_dir }} && cp docker-compose.yml.backup.* docker-compose.yml && docker-compose up -d"
