---
# Test dostępności serwera
# Sprawdza czy serwer odpowiada na ping i czy można się z nim połączyć przez SSH

- name: Test dostępności serwera
  hosts: "{{ target_host | default('serwery_nowe') }}"
  gather_facts: true
  tasks:
    - name: "Informacje o teście"
      debug:
        msg:
          - "=== TEST DOSTĘPNOŚCI SERWERA ==="
          - "Cel: {{ inventory_hostname }}"
          - "IP: {{ ansible_host }}"
          - "Użytkownik: {{ ansible_user }}"
      tags: [info]

    - name: "Ping serwera"
      ping:
      tags: [ping, connectivity]

    - name: "Sprawdzenie połączenia SSH"
      command: whoami
      register: ssh_result
      tags: [ssh, connectivity]

    - name: "Weryfikacja połączenia SSH"
      assert:
        that:
          - ssh_result.rc == 0
        fail_msg: "Nie udało się połą<PERSON> z serwerem przez SSH"
        success_msg: "Połączenie SSH działa poprawnie"
      tags: [ssh, verify]

    - name: "Sprawdzenie podstawowych informacji o systemie"
      debug:
        msg:
          - "Hostname: {{ ansible_hostname }}"
          - "OS: {{ ansible_distribution }} {{ ansible_distribution_version }}"
          - "Kernel: {{ ansible_kernel }}"
          - "Architektura: {{ ansible_architecture }}"
          - "Pamięć: {{ ansible_memtotal_mb }}MB"
          - "CPU: {{ ansible_processor_vcpus }} vCPU"
          - "Uptime: {{ ansible_uptime_seconds // 3600 }}h {{ (ansible_uptime_seconds % 3600) // 60 }}m"
      tags: [info, system]

    - name: "Sprawdzenie dostępnego miejsca na dysku"
      shell: df -h / | awk 'NR==2 {print $4}'
      register: disk_space
      changed_when: false
      tags: [disk, system]

    - name: "Sprawdzenie obciążenia systemu"
      shell: uptime | awk -F'load average:' '{print $2}'
      register: load_average
      changed_when: false
      tags: [load, system]

    - name: "Sprawdzenie czy porty SSH są otwarte"
      wait_for:
        port: 22
        host: "{{ ansible_host }}"
        timeout: 10
      delegate_to: localhost
      tags: [ports, connectivity]

    - name: "Test rozwiązywania nazw DNS"
      shell: nslookup google.com
      register: dns_test
      changed_when: false
      ignore_errors: true
      tags: [dns, connectivity]

    - name: "Podsumowanie testu dostępności"
      debug:
        msg:
          - "=== WYNIKI TESTU DOSTĘPNOŚCI ==="
          - ""
          - "✅ POŁĄCZENIE:"
          - "  Ping: OK"
          - "  SSH: {{ 'OK' if ssh_result.rc == 0 else 'BŁĄD' }}"
          - "  Port 22: OK"
          - ""
          - "📊 SYSTEM:"
          - "  OS: {{ ansible_distribution }} {{ ansible_distribution_version }}"
          - "  Pamięć: {{ ansible_memtotal_mb }}MB"
          - "  CPU: {{ ansible_processor_vcpus }} vCPU"
          - "  Dostępne miejsce: {{ disk_space.stdout }}"
          - "  Obciążenie: {{ load_average.stdout.strip() }}"
          - ""
          - "🌐 SIEĆ:"
          - "  DNS: {{ 'OK' if dns_test.rc == 0 else 'BŁĄD' }}"
          - ""
          - "{{ '✅ SERWER DOSTĘPNY I GOTOWY' if ssh_result.rc == 0 else '❌ PROBLEMY Z DOSTĘPNOŚCIĄ SERWERA' }}"
      tags: [info, summary]
