version: '3.8'

# Baserow Docker Compose - Zoptymalizowana konfiguracja dla baserow.simetria.pl
# Wygenerowane przez Ansible - nie edytować ręcznie!

services:
  # ==========================================================================
  # POSTGRESQL DATABASE - Zoptymalizowana dla 3806MB RAM
  # ==========================================================================
  postgres:
    image: postgres:13
    container_name: baserow-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      # Optymalizacja PostgreSQL
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 768MB
      POSTGRES_WORK_MEM: 4MB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
      POSTGRES_MAX_CONNECTIONS: 100
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U baserow -d baserow"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================================================
  # REDIS CACHE - Zoptymalizowany dla wydajności API
  # ==========================================================================
  redis:
    image: redis:6.2
    container_name: baserow-redis
    restart: unless-stopped
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --appendonly yes
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================================================
  # BASEROW BACKEND - API i logika biznesowa
  # ==========================================================================
  backend:
    image: baserow/backend:latest
    container_name: baserow-backend
    restart: unless-stopped
    ports:
      - "127.0.0.1:8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Konfiguracja bazy danych
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: redis://redis:6379
      
      # Konfiguracja Baserow
      BASEROW_PUBLIC_URL: https://baserow.simetria.pl
      SECRET_KEY: changeme
      BASEROW_AMOUNT_OF_WORKERS: 2
      
      # Email (jeśli skonfigurowane)
      EMAIL_SMTP: "false"
      
      # Optymalizacja wydajności
      BASEROW_TRIGGER_SYNC_TEMPLATES_AFTER_MIGRATION: "false"
      BASEROW_SYNC_TEMPLATES_TIME_LIMIT: "30"
      
      # Debug (tylko dla development)
      DEBUG: "false"
      
      # Bezpieczeństwo
      ALLOWED_HOSTS: baserow.simetria.pl,localhost,127.0.0.1
      CSRF_TRUSTED_ORIGINS: https://baserow.simetria.pl
      
    volumes:
      - baserow_data:/baserow/data
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/_health/"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ==========================================================================
  # BASEROW FRONTEND - Interfejs użytkownika
  # ==========================================================================
  frontend:
    image: baserow/web-frontend:latest
    container_name: baserow-frontend
    restart: unless-stopped
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    environment:
      BASEROW_PUBLIC_URL: https://baserow.simetria.pl
      PRIVATE_BACKEND_URL: http://backend:8000
      PUBLIC_BACKEND_URL: https://baserow.simetria.pl/api
      PUBLIC_WEB_FRONTEND_URL: https://baserow.simetria.pl
      INITIAL_TABLE_DATA_LIMIT: 500
      HOURS_UNTIL_TRASH_PERMANENTLY_DELETED: 72
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/_health/"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"


# =============================================================================
# VOLUMES - Persistent storage
# =============================================================================
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/baserow/volumes/postgres_data
  
  baserow_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/baserow/volumes/baserow_data
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/baserow/volumes/redis_data
  
  nginx_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/baserow/volumes/nginx_cache

# =============================================================================
# NETWORKS - Izolowana sieć dla kontenerów
# =============================================================================
networks:
  baserow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
