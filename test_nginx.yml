---
# Test konfiguracji Nginx
# Sprawdza czy Nginx odpowiada na żądania HTTP i HTTPS, oraz czy konfiguracja jest poprawna

- name: Test Nginx
  hosts: "{{ target_host | default('serwery_nowe') }}"
  gather_facts: true
  vars:
    baserow_domain: baserow.simetria.pl
    baserow_home: /opt/baserow
  tasks:
    - name: "Informacje o teście Nginx"
      debug:
        msg:
          - "=== TEST NGINX ==="
          - "Cel: {{ inventory_hostname }}"
          - "Domena: {{ baserow_domain }}"
          - "IP: {{ ansible_host }}"
      tags: [info]

    - name: "Sprawdzenie czy Nginx jest zainstalowany"
      command: which nginx
      register: nginx_installed
      changed_when: false
      ignore_errors: true
      tags: [nginx, install]

    - name: "Sprawdzenie statusu usługi Nginx"
      systemd:
        name: nginx
      register: nginx_service_status
      ignore_errors: true
      tags: [nginx, service]

    - name: "Sprawdzenie składni konfiguracji <PERSON>in<PERSON>"
      command: nginx -t
      register: nginx_syntax
      changed_when: false
      ignore_errors: true
      become: true
      tags: [nginx, config]

    - name: "Sprawdzenie czy Nginx nasłuchuje na porcie 80"
      wait_for:
        port: 80
        host: "{{ ansible_host }}"
        timeout: 10
      delegate_to: localhost
      ignore_errors: true
      register: port_80_check
      tags: [nginx, ports]

    - name: "Sprawdzenie czy Nginx nasłuchuje na porcie 443"
      wait_for:
        port: 443
        host: "{{ ansible_host }}"
        timeout: 10
      delegate_to: localhost
      ignore_errors: true
      register: port_443_check
      tags: [nginx, ports]

    - name: "Test dostępności HTTP (localhost)"
      uri:
        url: "http://localhost"
        method: GET
        timeout: 10
        status_code: [200, 301, 302, 404]
      register: http_localhost_test
      ignore_errors: true
      tags: [nginx, http]

    - name: "Test dostępności HTTP (zewnętrzny)"
      uri:
        url: "http://{{ ansible_host }}"
        method: GET
        timeout: 10
        status_code: [200, 301, 302, 404]
      register: http_external_test
      ignore_errors: true
      delegate_to: localhost
      tags: [nginx, http]

    - name: "Test dostępności HTTPS (localhost)"
      uri:
        url: "https://localhost"
        method: GET
        timeout: 10
        status_code: [200, 301, 302, 404]
        validate_certs: false
      register: https_localhost_test
      ignore_errors: true
      tags: [nginx, https]

    - name: "Test dostępności HTTPS (zewnętrzny)"
      uri:
        url: "https://{{ ansible_host }}"
        method: GET
        timeout: 10
        status_code: [200, 301, 302, 404]
        validate_certs: false
      register: https_external_test
      ignore_errors: true
      delegate_to: localhost
      tags: [nginx, https]

    - name: "Test health endpoint"
      uri:
        url: "http://localhost/health"
        method: GET
        timeout: 10
        status_code: [200]
      register: health_test
      ignore_errors: true
      tags: [nginx, health]

    - name: "Sprawdzenie logów błędów Nginx"
      shell: tail -n 10 /var/log/nginx/error.log 2>/dev/null || echo "Brak logów błędów"
      register: nginx_error_logs
      changed_when: false
      become: true
      tags: [nginx, logs]

    - name: "Sprawdzenie konfiguracji SSL"
      shell: |
        if [ -f "/etc/nginx/ssl/{{ baserow_domain }}.crt" ]; then
          openssl x509 -in "/etc/nginx/ssl/{{ baserow_domain }}.crt" -noout -subject -dates
        else
          echo "Certyfikat SSL nie istnieje"
        fi
      register: ssl_cert_info
      changed_when: false
      become: true
      tags: [nginx, ssl]

    - name: "Sprawdzenie procesów Nginx"
      shell: ps aux | grep nginx | grep -v grep
      register: nginx_processes
      changed_when: false
      tags: [nginx, processes]

    - name: "Podsumowanie testu Nginx"
      debug:
        msg:
          - "=== WYNIKI TESTU NGINX ==="
          - ""
          - "🔧 INSTALACJA I KONFIGURACJA:"
          - "  Nginx zainstalowany: {{ 'OK' if nginx_installed.rc == 0 else 'BŁĄD' }}"
          - "  Status usługi: {{ nginx_service_status.status.ActiveState | default('NIEZNANY') }}"
          - "  Składnia konfiguracji: {{ 'OK' if nginx_syntax.rc == 0 else 'BŁĄD' }}"
          - ""
          - "🌐 PORTY:"
          - "  Port 80 (HTTP): {{ 'OK' if port_80_check is succeeded else 'BŁĄD' }}"
          - "  Port 443 (HTTPS): {{ 'OK' if port_443_check is succeeded else 'BŁĄD' }}"
          - ""
          - "📡 DOSTĘPNOŚĆ HTTP:"
          - "  Localhost: {{ 'OK (' + (http_localhost_test.status | string) + ')' if http_localhost_test is succeeded else 'BŁĄD' }}"
          - "  Zewnętrzny: {{ 'OK (' + (http_external_test.status | string) + ')' if http_external_test is succeeded else 'BŁĄD' }}"
          - ""
          - "🔒 DOSTĘPNOŚĆ HTTPS:"
          - "  Localhost: {{ 'OK (' + (https_localhost_test.status | string) + ')' if https_localhost_test is succeeded else 'BŁĄD' }}"
          - "  Zewnętrzny: {{ 'OK (' + (https_external_test.status | string) + ')' if https_external_test is succeeded else 'BŁĄD' }}"
          - ""
          - "❤️ HEALTH CHECK:"
          - "  /health endpoint: {{ 'OK' if health_test is succeeded else 'BŁĄD' }}"
          - ""
          - "🔐 SSL:"
          - "{{ ssl_cert_info.stdout_lines }}"
          - ""
          - "📊 PROCESY:"
          - "  Liczba procesów worker: {{ nginx_processes.stdout_lines | length - 1 if nginx_processes.stdout_lines | length > 1 else 0 }}"
          - ""
          - "{{ '✅ NGINX DZIAŁA POPRAWNIE' if (nginx_installed.rc == 0 and nginx_syntax.rc == 0 and http_localhost_test is succeeded) else '❌ PROBLEMY Z NGINX' }}"
      tags: [info, summary]
