#!/bin/bash
# Skrypt do uruchamiania testów integracyjnych

set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TEST_DIR="$SCRIPT_DIR/integration"
RESULTS_DIR="$SCRIPT_DIR/results"

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funkcja do logowania
log() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Utworzenie katalogu na wyniki
mkdir -p "$RESULTS_DIR"

log "Rozpoczynam testy integracyjne..."

# Sprawdzenie dostępności narzędzi
log "Sprawdzanie dostępności narzędzi..."
command -v ansible-playbook >/dev/null 2>&1 || { error "ansible-playbook nie jest zainstalowany"; exit 1; }
command -v gpg >/dev/null 2>&1 || { error "gpg nie jest zainstalowany"; exit 1; }

# Testy backup GPG
log "Uruchamianie testów backup GPG..."
if ansible-playbook -i localhost, "$TEST_DIR/backup/test_gpg_backup.yml" -v > "$RESULTS_DIR/gpg_backup_test.log" 2>&1; then
    success "Testy backup GPG zakończone sukcesem"
else
    error "Testy backup GPG zakończone niepowodzeniem"
    echo "Szczegóły w $RESULTS_DIR/gpg_backup_test.log"
    exit 1
fi

# Podsumowanie
log "Testy integracyjne zakończone"
log "Wyniki zapisane w: $RESULTS_DIR"

# Wyświetlenie podsumowania
echo ""
echo "=== PODSUMOWANIE TESTÓW ==="
echo "Testy backup GPG: $(grep -q "failed=0" "$RESULTS_DIR/gpg_backup_test.log" && echo "OK" || echo "FAILED")"
echo ""
echo "Szczegółowe logi:"
echo "- GPG Backup: $RESULTS_DIR/gpg_backup_test.log"