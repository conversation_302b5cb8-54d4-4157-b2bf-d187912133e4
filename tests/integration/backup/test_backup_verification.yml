---
- name: Test weryfikacji backupów
  hosts: localhost
  connection: local
  gather_facts: yes
  
  vars:
    test_backup_dir: "/tmp/test_verification_{{ ansible_date_time.epoch }}"
    test_data_dir: "/tmp/test_verification_data"
    gpg_test_key_email: "<EMAIL>"
    
  tasks:
    - name: Utworzenie katalogów testowych
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - "{{ test_backup_dir }}"
        - "{{ test_data_dir }}"
        
    - name: <PERSON>rowanie przykładowych danych testowych
      copy:
        content: |
          Testowa baza danych do weryfikacji
          Wersja: 1.0
          Data: {{ ansible_date_time.iso8601 }}
          Dane testowe do weryfikacji integralności backupu
        dest: "{{ test_data_dir }}/test_verification.sql"
        
    - name: Tworzenie backupu testowego
      archive:
        path: "{{ test_data_dir }}"
        dest: "{{ test_backup_dir }}/test_backup.tar.gz"
        format: gz
        
    - name: Ob<PERSON><PERSON>ie sumy kontrolnej oryginalnego backupu
      stat:
        path: "{{ test_backup_dir }}/test_backup.tar.gz"
        checksum_algorithm: sha256
      register: original_backup
        
    - name: Test 1 - Weryfikacja integralności archiwum
      shell: "tar -tzf {{ test_backup_dir }}/test_backup.tar.gz"
      register: tar_check
      failed_when: tar_check.rc != 0
      
    - name: Test 2 - Weryfikacja sumy kontrolnej
      stat:
        path: "{{ test_backup_dir }}/test_backup.tar.gz"
        checksum_algorithm: sha256
      register: current_backup
      
    - name: Porównanie sum kontrolnych
      assert:
        that:
          - original_backup.stat.checksum == current_backup.stat.checksum
        fail_msg: "Suma kontrolna backupu została zmieniona - możliwe uszkodzenie"
        
    - name: Test 3 - Weryfikacja rozmiaru pliku
      assert:
        that:
          - original_backup.stat.size > 0
          - current_backup.stat.size == original_backup.stat.size
        fail_msg: "Rozmiar backupu jest nieprawidłowy"
        
    - name: Test 4 - Weryfikacja daty modyfikacji
      assert:
        that:
          - original_backup.stat.mtime == current_backup.stat.mtime
        fail_msg: "Data modyfikacji backupu została zmieniona"
        
    - name: Test 5 - Test ekstrakcji
      shell: "tar -xzf {{ test_backup_dir }}/test_backup.tar.gz -C {{ test_backup_dir }}"
      register: extract_result
      failed_when: extract_result.rc != 0
      
    - name: Weryfikacja przywróconych plików
      stat:
        path: "{{ test_backup_dir }}{{ test_data_dir }}/test_verification.sql"
      register: restored_file
      
    - name: Sprawdzenie integralności przywróconych danych
      assert:
        that:
          - restored_file.stat.exists
          - restored_file.stat.size > 0
        fail_msg: "Przywrócone dane są uszkodzone"
        
    - name: Czyszczenie - usunięcie katalogów testowych
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - "{{ test_backup_dir }}"
        - "{{ test_data_dir }}"
        
    - name: Podsumowanie testu weryfikacji
      debug:
        msg:
          - "Test weryfikacji backupu zakończony"
          - "Integralność archiwum: {{ tar_check.rc == 0 }}"
          - "Suma kontrolna poprawna: {{ original_backup.stat.checksum == current_backup.stat.checksum }}"
          - "Rozmiar prawidłowy: {{ current_backup.stat.size == original_backup.stat.size }}"
          - "Ekstrakcja udana: {{ extract_result.rc == 0 }}"
          - "Dane przywrócone poprawnie: {{ restored_file.stat.exists | default(false) }}"