---
- name: Test integracyjny - backup GPG
  hosts: localhost
  connection: local
  gather_facts: yes
  
  vars:
    test_backup_dir: "/tmp/test_backup_{{ ansible_date_time.epoch }}"
    test_data_dir: "/tmp/test_data"
    gpg_test_key_email: "<EMAIL>"
    gpg_test_key_name: "Test Backup Key"
    
  tasks:
    - name: Utworzenie katalogów testowych
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - "{{ test_backup_dir }}"
        - "{{ test_data_dir }}"
        
    - name: Generowanie przykładowych danych testowych
      copy:
        content: |
          Testowa baza danych
          Wersja: 1.0
          Data: {{ ansible_date_time.iso8601 }}
          Dane testowe do weryfikacji backupu GPG
        dest: "{{ test_data_dir }}/test_database.sql"
        
    - name: Generowanie klucza GPG dla testów
      shell: |
        gpg --batch --gen-key <<EOF
        Key-Type: RSA
        Key-Length: 2048
        Subkey-Type: <PERSON>A
        Subkey-Length: 2048
        Name-Real: {{ gpg_test_key_name }}
        Name-Email: {{ gpg_test_key_email }}
        Expire-Date: 0
        Passphrase: test123
        %commit
        %echo done
        EOF
      register: gpg_keygen
      changed_when: false
      
    - name: Pobranie ID klucza GPG
      shell: "gpg --list-secret-keys --keyid-format LONG {{ gpg_test_key_email }} | grep sec | awk '{print $2}' | cut -d'/' -f2"
      register: gpg_key_id
      changed_when: false
      
    - name: Test 1 - Tworzenie backupu z szyfrowaniem GPG
      shell: |
        tar -czf - {{ test_data_dir }} | \
        gpg --trust-model always --encrypt --recipient {{ gpg_test_key_email }} > \
        {{ test_backup_dir }}/test_backup.tar.gz.gpg
      register: backup_result
      
    - name: Weryfikacja utworzenia zaszyfrowanego backupu
      stat:
        path: "{{ test_backup_dir }}/test_backup.tar.gz.gpg"
      register: backup_file
      
    - name: Sprawdzenie czy backup został utworzony
      assert:
        that:
          - backup_file.stat.exists
          - backup_file.stat.size > 0
        fail_msg: "Backup GPG nie został utworzony lub jest pusty"
        
    - name: Test 2 - Weryfikacja szyfrowania
      shell: "file {{ test_backup_dir }}/test_backup.tar.gz.gpg"
      register: encryption_check
      
    - name: Sprawdzenie czy plik jest zaszyfrowany
      assert:
        that:
          - "'PGP' in encryption_check.stdout"
        fail_msg: "Plik backupu nie jest zaszyfrowany GPG"
        
    - name: Test 3 - Odszyfrowanie backupu
      shell: |
        gpg --batch --passphrase test123 --decrypt \
        {{ test_backup_dir }}/test_backup.tar.gz.gpg > \
        {{ test_backup_dir }}/decrypted_backup.tar.gz
      register: decrypt_result
      
    - name: Weryfikacja odszyfrowania
      stat:
        path: "{{ test_backup_dir }}/decrypted_backup.tar.gz"
      register: decrypted_file
      
    - name: Sprawdzenie czy backup został poprawnie odszyfrowany
      assert:
        that:
          - decrypted_file.stat.exists
          - decrypted_file.stat.size > 0
        fail_msg: "Nie udało się odszyfrować backupu"
        
    - name: Test 4 - Weryfikacja integralności po odszyfrowaniu
      shell: "tar -tzf {{ test_backup_dir }}/decrypted_backup.tar.gz"
      register: tar_check
      
    - name: Sprawdzenie czy archiwum tar jest poprawne
      assert:
        that:
          - tar_check.rc == 0
        fail_msg: "Odszyfrowany backup ma uszkodzoną strukturę tar"
        
    - name: Test 5 - Weryfikacja zawartości backupu
      shell: "tar -xzf {{ test_backup_dir }}/decrypted_backup.tar.gz -C {{ test_backup_dir }}"
      
    - name: Sprawdzenie czy oryginalne pliki zostały przywrócone
      stat:
        path: "{{ test_backup_dir }}{{ test_data_dir }}/test_database.sql"
      register: restored_file
      
    - name: Weryfikacja integralności przywróconych danych
      assert:
        that:
          - restored_file.stat.exists
          - restored_file.stat.size > 0
        fail_msg: "Przywrócone dane są uszkodzone lub niekompletne"
        
    - name: Test 6 - Weryfikacja sum kontrolnych
      shell: "sha256sum {{ test_data_dir }}/test_database.sql"
      register: original_checksum
      
    - name: Obliczenie sumy kontrolnej przywróconego pliku
      shell: "sha256sum {{ test_backup_dir }}{{ test_data_dir }}/test_database.sql | cut -d' ' -f1"
      register: restored_checksum
      
    - name: Porównanie sum kontrolnych
      assert:
        that:
          - original_checksum.stdout.split(' ')[0] == restored_checksum.stdout
        fail_msg: "Suma kontrolna przywróconego pliku nie zgadza się z oryginałem"
        
    - name: Czyszczenie - usunięcie klucza GPG
      shell: "gpg --batch --yes --delete-secret-and-public-key {{ gpg_test_key_email }}"
      ignore_errors: yes
      
    - name: Czyszczenie - usunięcie katalogów testowych
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - "{{ test_backup_dir }}"
        - "{{ test_data_dir }}"
        
    - name: Logowanie wyników testu
      debug:
        msg:
          - "Test GPG backup zakończony"
          - "Backup utworzony: {{ backup_file.stat.exists | default(false) }}"
          - "Szyfrowanie weryfikowane: {{ 'PGP' in encryption_check.stdout | default('') }}"
          - "Odszyfrowanie udane: {{ decrypted_file.stat.exists | default(false) }}"
          - "Integralność danych: {{ original_checksum.stdout.split(' ')[0] == restored_checksum.stdout | default(false) }}"