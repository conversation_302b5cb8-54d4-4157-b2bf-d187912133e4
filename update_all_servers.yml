---
# ============================================================================
# PLAYBOOK AKTUALIZACJI WSZYSTKICH SERWERÓW
# ============================================================================
# Cel: Bezpieczna aktualizacja systemu na wszystkich serwerach
# Uwzględnia różnice między serwerami nowymi i legacy
# Wykonuje aktualizacje po kolei (serial: 1) dla bezpieczeństwa
# ============================================================================

- name: "Informacje o aktualizacji serwerów"
  hosts: localhost
  gather_facts: false
  tasks:
    - name: "Rozpoczęcie aktualizacji wszystkich serwerów"
      debug:
        msg:
          - "=== AKTUALIZACJA WSZYSTKICH SERWERÓW ==="
          - "Data: {{ ansible_date_time.iso8601 }}"
          - "Tryb: {{ update_mode | default('safe') }}"
          - "Serial: {{ update_serial | default(1) }}"
          - ""
          - "SERWERY DO AKTUALIZACJI:"
          - "- Nowe (Hetzner): simetria-hetzner001"
          - "- Legacy (OVH): simetria-ovh"  
          - "- Legacy (SMS): sms-server"
          - ""
          - "UWAGA: Aktualizacja będzie wykonana po kolei dla bezpieczeństwa"
      tags: [info]

# ============================================================================
# AKTUALIZACJA SERWERÓW NOWYCH (PEŁNA AUTOMATYZACJA)
# ============================================================================

- name: "Aktualizacja serwerów nowych (Hetzner)"
  hosts: serwery_nowe
  become: true
  serial: "{{ update_serial | default(1) }}"
  gather_facts: true
  
  vars:
    update_mode: "{{ update_mode | default('safe') }}"
    reboot_required: false
    
  tasks:
    - name: "=== AKTUALIZACJA SERWERA NOWEGO ==="
      debug:
        msg:
          - "Aktualizuję serwer: {{ inventory_hostname }}"
          - "Grupa: serwery_nowe"
          - "System: {{ ansible_distribution }} {{ ansible_distribution_version }}"
          - "Tryb: {{ update_mode }}"
      tags: [info]

    # Sprawdzenie stanu przed aktualizacją
    - name: "Sprawdź stan przed aktualizacją"
      shell: |
        echo "=== STAN PRZED AKTUALIZACJĄ ==="
        echo "Uptime: $(uptime)"
        echo "Dostępne aktualizacje:"
        apt list --upgradable 2>/dev/null | wc -l
        echo "Miejsce na dysku:"
        df -h / | tail -1
        echo "Pamięć:"
        free -h | grep Mem
      register: pre_update_status
      tags: [check]

    - name: "Wyświetl stan przed aktualizacją"
      debug:
        msg: "{{ pre_update_status.stdout_lines }}"
      tags: [info]

    # Aktualizacja cache pakietów
    - name: "Aktualizuj cache pakietów"
      apt:
        update_cache: true
        cache_valid_time: 3600
      tags: [update]

    # Sprawdzenie dostępnych aktualizacji
    - name: "Sprawdź dostępne aktualizacje"
      shell: apt list --upgradable 2>/dev/null | grep -v "WARNING" | wc -l
      register: available_updates
      changed_when: false
      tags: [check]

    - name: "Informacja o dostępnych aktualizacjach"
      debug:
        msg: "Dostępne aktualizacje: {{ available_updates.stdout }}"
      tags: [info]

    # Aktualizacja systemu (różne tryby)
    - name: "Aktualizacja systemu (tryb bezpieczny)"
      apt:
        upgrade: safe
        autoremove: true
        autoclean: true
      register: safe_update
      when: update_mode == "safe"
      tags: [update]

    - name: "Aktualizacja systemu (tryb pełny)"
      apt:
        upgrade: dist
        autoremove: true
        autoclean: true
      register: full_update
      when: update_mode == "full"
      tags: [update]

    # Sprawdzenie czy wymagany restart
    - name: "Sprawdź czy wymagany restart"
      stat:
        path: /var/run/reboot-required
      register: reboot_required_file
      tags: [check]

    - name: "Informacja o wymaganym restarcie"
      debug:
        msg: "{{ 'RESTART WYMAGANY' if reboot_required_file.stat.exists else 'Restart nie jest wymagany' }}"
      tags: [info]

    # Sprawdzenie stanu po aktualizacji
    - name: "Sprawdź stan po aktualizacji"
      shell: |
        echo "=== STAN PO AKTUALIZACJI ==="
        echo "Zainstalowane aktualizacje: {{ (safe_update.changed if update_mode == 'safe' else full_update.changed) | default(false) }}"
        echo "Dostępne aktualizacje:"
        apt list --upgradable 2>/dev/null | wc -l
        echo "Miejsce na dysku:"
        df -h / | tail -1
      register: post_update_status
      tags: [check]

    - name: "Wyświetl stan po aktualizacji"
      debug:
        msg: "{{ post_update_status.stdout_lines }}"
      tags: [info]

    # Sprawdzenie kluczowych usług (tylko dla serwerów nowych)
    - name: "Sprawdź kluczowe usługi"
      shell: |
        echo "=== STATUS USŁUG ==="
        systemctl is-active docker || echo "docker: INACTIVE"
        systemctl is-active nginx || echo "nginx: INACTIVE"
        systemctl is-active ssh || echo "ssh: INACTIVE"
        echo "=== KONTENERY DOCKER ==="
        docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Docker niedostępny"
      register: services_status
      ignore_errors: true
      tags: [check]

    - name: "Wyświetl status usług"
      debug:
        msg: "{{ services_status.stdout_lines }}"
      tags: [info]

  tags: [new_servers, hetzner]

# ============================================================================
# AKTUALIZACJA SERWERÓW LEGACY (OSTROŻNA)
# ============================================================================

- name: "Aktualizacja serwerów legacy"
  hosts: serwery_legacy
  become: true
  serial: 1  # Zawsze po jednym dla legacy
  gather_facts: true
  
  vars:
    update_mode: "{{ update_mode | default('safe') }}"
    
  tasks:
    - name: "=== AKTUALIZACJA SERWERA LEGACY ==="
      debug:
        msg:
          - "Aktualizuję serwer: {{ inventory_hostname }}"
          - "Grupa: serwery_legacy"
          - "System: {{ ansible_distribution }} {{ ansible_distribution_version }}"
          - "Tryb: {{ update_mode }} (zawsze ostrożny dla legacy)"
      tags: [info]

    # Sprawdzenie stanu przed aktualizacją
    - name: "Sprawdź stan przed aktualizacją (legacy)"
      shell: |
        echo "=== STAN PRZED AKTUALIZACJĄ (LEGACY) ==="
        echo "Hostname: $(hostname)"
        echo "Uptime: $(uptime)"
        echo "Dostępne aktualizacje:"
        apt list --upgradable 2>/dev/null | wc -l || echo "Nie można sprawdzić"
        echo "Miejsce na dysku:"
        df -h / | tail -1
        echo "Pamięć:"
        free -h | grep Mem
        echo "Działające kontenery:"
        docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Docker niedostępny"
      register: legacy_pre_update_status
      tags: [check]

    - name: "Wyświetl stan przed aktualizacją (legacy)"
      debug:
        msg: "{{ legacy_pre_update_status.stdout_lines }}"
      tags: [info]

    # Aktualizacja cache (ostrożnie)
    - name: "Aktualizuj cache pakietów (legacy)"
      apt:
        update_cache: true
        cache_valid_time: 3600
      ignore_errors: true
      tags: [update]

    # Tylko bezpieczne aktualizacje dla legacy
    - name: "Aktualizacja systemu (tylko bezpieczne dla legacy)"
      apt:
        upgrade: safe
        autoremove: false  # Nie usuwaj pakietów automatycznie
        autoclean: true
      register: legacy_update
      ignore_errors: true
      tags: [update]

    # Sprawdzenie czy wymagany restart
    - name: "Sprawdź czy wymagany restart (legacy)"
      stat:
        path: /var/run/reboot-required
      register: legacy_reboot_required
      tags: [check]

    - name: "Informacja o wymaganym restarcie (legacy)"
      debug:
        msg: "{{ 'RESTART WYMAGANY - WYKONAJ RĘCZNIE!' if legacy_reboot_required.stat.exists else 'Restart nie jest wymagany' }}"
      tags: [info]

    # Sprawdzenie aplikacji specyficznych dla każdego serwera legacy
    - name: "Sprawdź aplikacje specyficzne (n8n na OVH)"
      shell: |
        echo "=== APLIKACJE N8N (OVH) ==="
        if [ "{{ inventory_hostname }}" = "simetria-ovh" ]; then
          cd /home/<USER>/projekty/n8n 2>/dev/null && docker-compose ps || echo "n8n niedostępny"
        fi
      register: ovh_apps_status
      when: inventory_hostname == "simetria-ovh"
      ignore_errors: true
      tags: [check]

    - name: "Wyświetl status aplikacji OVH"
      debug:
        msg: "{{ ovh_apps_status.stdout_lines }}"
      when: inventory_hostname == "simetria-ovh" and ovh_apps_status is defined
      tags: [info]

    # Sprawdzenie stanu po aktualizacji
    - name: "Sprawdź stan po aktualizacji (legacy)"
      shell: |
        echo "=== STAN PO AKTUALIZACJI (LEGACY) ==="
        echo "Aktualizacja wykonana: {{ legacy_update.changed | default(false) }}"
        echo "Dostępne aktualizacje:"
        apt list --upgradable 2>/dev/null | wc -l || echo "Nie można sprawdzić"
        echo "Miejsce na dysku:"
        df -h / | tail -1
      register: legacy_post_update_status
      tags: [check]

    - name: "Wyświetl stan po aktualizacji (legacy)"
      debug:
        msg: "{{ legacy_post_update_status.stdout_lines }}"
      tags: [info]

  tags: [legacy_servers, ovh, sms]

# ============================================================================
# PODSUMOWANIE AKTUALIZACJI
# ============================================================================

- name: "Podsumowanie aktualizacji wszystkich serwerów"
  hosts: localhost
  gather_facts: false
  tasks:
    - name: "=== PODSUMOWANIE AKTUALIZACJI ==="
      debug:
        msg:
          - "Aktualizacja wszystkich serwerów zakończona"
          - ""
          - "WYKONANE DZIAŁANIA:"
          - "✅ Aktualizacja serwerów nowych (Hetzner)"
          - "✅ Aktualizacja serwerów legacy (OVH, SMS)"
          - "✅ Sprawdzenie stanu przed i po aktualizacji"
          - "✅ Weryfikacja kluczowych usług"
          - ""
          - "NASTĘPNE KROKI:"
          - "1. Sprawdź logi: ansible-playbook server_overview.yml"
          - "2. Wykonaj health check: ansible-playbook health_check.yml"
          - "3. W razie problemów sprawdź poszczególne serwery ręcznie"
          - ""
          - "RESTART SERWERÓW:"
          - "- Jeśli wymagany restart, wykonaj ręcznie dla bezpieczeństwa"
          - "- Serwery legacy: szczególna ostrożność!"
      tags: [summary]
