# Plik inwentarza Ansible
# Definiuje serwery, kt<PERSON><PERSON><PERSON>z<PERSON>d<PERSON>, grupuje je i przypisuje zmienne.

[serwery:children]
serwery_nowe
serwery_legacy

# ==============================================================================
# GRUPA: Nowe serwery
# ==============================================================================
[serwery_nowe]
simetria-hetzner001 ansible_host=*********** ansible_ssh_private_key_file=~/.ssh/simetria-hetzner

# ==============================================================================
# GRUPA: Stare serwery (Legacy)
# ==============================================================================
[serwery_legacy]
simetria-ovh ansible_host=************** ansible_port=1988 ansible_ssh_private_key_file=~/.ssh/id_ed25519

sms-server ansible_host=ssh-hp.simetria.cc ansible_ssh_private_key_file=~/.ssh/sms-server-key ansible_ssh_common_args='-o ProxyCommand="cloudflared access ssh --hostname %h"'

# ==============================================================================
# ZMIENNE DLA WSZYSTKICH SERWERÓW (`all:vars`)
# ==============================================================================
[all:vars]
ansible_user=szymcio
