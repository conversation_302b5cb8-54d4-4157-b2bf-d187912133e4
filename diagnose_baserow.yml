---
- name: <PERSON>ag<PERSON>za problemów Baserow
  hosts: serwery_nowe
  become: true
  tasks:
    - name: Sprawdź porty
      ansible.builtin.shell: |
        netstat -tlnp | grep -E ':(80|443|3000)'
      register: ports_check
      tags: [network, ports]

    - name: <PERSON>prawd<PERSON> logi Nginx
      ansible.builtin.shell: |
        tail -20 /var/log/nginx/error.log
      register: nginx_logs
      ignore_errors: true
      tags: [logs, nginx]

    - name: Sprawdź logi Docker
      ansible.builtin.shell: |
        cd /home/<USER>
      register: docker_logs
      ignore_errors: true
      tags: [logs, docker]

    - name: Test połączenia lokalnego
      ansible.builtin.uri:
        url: "http://localhost:3000"
        method: GET
        timeout: 10
      register: local_test
      ignore_errors: true
      tags: [connectivity, local]

    - name: Wyświetl wyniki diagnozy
      ansible.builtin.debug:
        msg:
          - "=== PORTY ==="
          - "{{ ports_check.stdout_lines }}"
          - "=== NGINX LOGS ==="
          - "{{ nginx_logs.stdout_lines | default(['Brak logów']) }}"
          - "=== DOCKER LOGS ==="
          - "{{ docker_logs.stdout_lines | default(['Brak logów']) }}"
          - "=== TEST LOKALNY ==="
          - "Status: {{ local_test.status | default('BŁĄD') }}"
      tags: [summary, debug]
