[defaults]
host_key_checking = False
timeout = 30
retry_files_enabled = False
inventory = inventory_production
private_key_file = ~/.ssh/simetria-hetzner
remote_user = root
ask_pass = False
ask_sudo_pass = False
vault_password_file = .vault_pass

[ssh_connection]
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no
pipelining = True
control_path = ~/.ssh/ansible-%%h-%%p-%%r