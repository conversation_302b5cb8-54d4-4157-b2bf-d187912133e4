---
- name: Diagnoza obecnego stanu n8n na serwerze OVH
  hosts: simetria-ovh
  become: true
  gather_facts: true
  
  tasks:
    - name: "=== DIAGNOZA N8N - START ==="
      debug:
        msg:
          - "Rozpoczynam diagnozę n8n na serwerze {{ ansible_hostname }}"
          - "Data: {{ ansible_date_time.iso8601 }}"
          - "Użytkownik: {{ ansible_user }}"

    # ==========================================================================
    # SPRAWDZENIE KONTENERÓW N8N
    # ==========================================================================
    
    - name: "Sprawd<PERSON> działaj<PERSON><PERSON> kontenery Docker"
      shell: 'docker ps --format "table {{"{{"}}.Names{{"}}"}}\t{{"{{"}}.Status{{"}}"}}\t{{"{{"}}.Ports{{"}}"}}\t{{"{{"}}.Image{{"}}"}}"'
      register: docker_containers
      ignore_errors: true

    - name: "Wyświetl działające kontenery"
      debug:
        msg: "{{ docker_containers.stdout_lines | default(['Brak danych']) }}"

    - name: "Sprawdź kontenery n8n (wszystkie)"
      shell: 'docker ps -a --filter "name=n8n" --format "table {{"{{"}}.Names{{"}}"}}\t{{"{{"}}.Status{{"}}"}}\t{{"{{"}}.Ports{{"}}"}}\t{{"{{"}}.Image{{"}}"}}"'
      register: n8n_containers
      ignore_errors: true

    - name: "Wyświetl kontenery n8n"
      debug:
        msg: "{{ n8n_containers.stdout_lines | default(['Brak kontenerów n8n']) }}"

    # ==========================================================================
    # SPRAWDZENIE WERSJI N8N
    # ==========================================================================
    
    - name: "Sprawdź wersję n8n"
      shell: docker exec n8n n8n --version 2>/dev/null || echo "Nie można sprawdzić wersji"
      register: n8n_version
      ignore_errors: true

    - name: "Wyświetl wersję n8n"
      debug:
        msg: "Obecna wersja n8n: {{ n8n_version.stdout }}"

    - name: "Sprawdź obraz Docker n8n"
      shell: docker inspect n8n --format='{{.Config.Image}}' 2>/dev/null || echo "Kontener nie istnieje"
      register: n8n_image
      ignore_errors: true

    - name: "Wyświetl obraz n8n"
      debug:
        msg: "Obraz Docker n8n: {{ n8n_image.stdout }}"

    # ==========================================================================
    # SPRAWDZENIE WOLUMINÓW I DANYCH
    # ==========================================================================
    
    - name: "Sprawdź woluminy Docker"
      shell: 'docker volume ls --filter "name=n8n" --format "table {{"{{"}}.Name{{"}}"}}\t{{"{{"}}.Driver{{"}}"}}"'
      register: n8n_volumes
      ignore_errors: true

    - name: "Wyświetl woluminy n8n"
      debug:
        msg: "{{ n8n_volumes.stdout_lines | default(['Brak woluminów n8n']) }}"

    - name: "Sprawdź rozmiar woluminów n8n"
      shell: |
        for volume in $(docker volume ls --filter "name=n8n" --format "{{"{{"}}.Name{{"}}"}}");
          echo "=== $volume ==="
          docker run --rm -v $volume:/data alpine du -sh /data 2>/dev/null || echo "Błąd sprawdzania $volume"
        done
      register: volume_sizes
      ignore_errors: true

    - name: "Wyświetl rozmiary woluminów"
      debug:
        msg: "{{ volume_sizes.stdout_lines | default(['Brak danych o rozmiarach']) }}"

    # ==========================================================================
    # SPRAWDZENIE KONFIGURACJI DOCKER-COMPOSE
    # ==========================================================================
    
    - name: "Znajdź pliki docker-compose z n8n"
      shell: find /home /opt /root -name "docker-compose.yml" -exec grep -l "n8n" {} \; 2>/dev/null || echo "Nie znaleziono"
      register: compose_files
      ignore_errors: true

    - name: "Wyświetl znalezione pliki docker-compose"
      debug:
        msg: "{{ compose_files.stdout_lines | default(['Brak plików docker-compose z n8n']) }}"

    - name: "Sprawdź zawartość głównego docker-compose (jeśli istnieje)"
      shell: |
        if [ -f /home/<USER>/docker-compose.yml ]; then
          echo "=== /home/<USER>/docker-compose.yml ==="
          cat /home/<USER>/docker-compose.yml
        elif [ -f /opt/n8n/docker-compose.yml ]; then
          echo "=== /opt/n8n/docker-compose.yml ==="
          cat /opt/n8n/docker-compose.yml
        else
          echo "Nie znaleziono głównego pliku docker-compose.yml"
        fi
      register: compose_content
      ignore_errors: true

    - name: "Wyświetl zawartość docker-compose"
      debug:
        msg: "{{ compose_content.stdout_lines | default(['Brak zawartości docker-compose']) }}"

    # ==========================================================================
    # SPRAWDZENIE DOSTĘPNOŚCI N8N
    # ==========================================================================
    
    - name: "Sprawdź czy n8n odpowiada na porcie 5678"
      uri:
        url: "http://127.0.0.1:5678"
        method: GET
        timeout: 10
      register: n8n_health
      ignore_errors: true

    - name: "Wyświetl status n8n"
      debug:
        msg: "Status n8n: {{ 'DZIAŁA' if n8n_health.status == 200 else 'NIE DZIAŁA' }}"

    # ==========================================================================
    # SPRAWDZENIE LOGÓW N8N
    # ==========================================================================
    
    - name: "Sprawdź ostatnie logi n8n (ostatnie 20 linii)"
      shell: docker logs n8n --tail 20 2>/dev/null || echo "Nie można pobrać logów"
      register: n8n_logs
      ignore_errors: true

    - name: "Wyświetl logi n8n"
      debug:
        msg: "{{ n8n_logs.stdout_lines | default(['Brak logów n8n']) }}"

    # ==========================================================================
    # PODSUMOWANIE
    # ==========================================================================
    
    - name: "=== PODSUMOWANIE DIAGNOZY ==="
      debug:
        msg:
          - "Diagnoza n8n zakończona"
          - "Sprawdź wyniki powyżej przed przystąpieniem do aktualizacji"
          - "Następny krok: backup danych n8n"
