# Baserow Environment Configuration
# Generated by Ansible - do not edit manually!

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=baserow
POSTGRES_USER=baserow
POSTGRES_PASSWORD=baserow
DATABASE_URL=******************************************/baserow
REDIS_URL=redis://redis:6379

# =============================================================================
# BASEROW CONFIGURATION
# =============================================================================
BASEROW_PUBLIC_URL=https://baserow.simetria.pl
SECRET_KEY=changeme
BASEROW_AMOUNT_OF_WORKERS=2

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_SMTP=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ALLOWED_HOSTS=baserow.simetria.pl,localhost,127.0.0.1
CSRF_TRUSTED_ORIGINS=https://baserow.simetria.pl

# =============================================================================
# DEBUG CONFIGURATION
# =============================================================================
DEBUG=false

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
BASEROW_TRIGGER_SYNC_TEMPLATES_AFTER_MIGRATION=false
BASEROW_SYNC_TEMPLATES_TIME_LIMIT=30
