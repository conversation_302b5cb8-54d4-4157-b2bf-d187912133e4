---
- name: Test GPG backup functionality
  hosts: baserow_servers
  become: yes
  gather_facts: yes
  
  vars:
    ansible_ssh_timeout: 30
    ansible_command_timeout: 300
    
  tasks:
    - name: Ensure Docker is running
      systemd:
        name: docker
        state: started
        enabled: yes
        
    - name: Create test directory
      file:
        path: /tmp/test_backup
        state: directory
        mode: '0755'
        
    - name: Create test database file
      copy:
        content: |
          -- Test database backup
          CREATE TABLE test_table (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          
          INSERT INTO test_table (name) VALUES ('test_data_1'), ('test_data_2'), ('test_data_3');
        dest: /tmp/test_backup/test_db.sql
        mode: '0644'
        
    - name: Test symmetric encryption with GPG
      shell: |
        cd /tmp/test_backup
        echo "test123" | gpg --batch --yes --symmetric --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 --s2k-digest-algo SHA512 --s2k-count 65536 --passphrase-fd 0 test_db.sql
      become_user: root
        
    - name: Verify encrypted file exists
      stat:
        path: /tmp/test_backup/test_db.sql.gpg
      register: encrypted_file
        
    - name: Check encryption result
      assert:
        that:
          - encrypted_file.stat.exists
          - encrypted_file.stat.size > 0
        fail_msg: "Encrypted backup file was not created or is empty"
        success_msg: "GPG backup encryption successful"
        
    - name: Decrypt and verify backup
      shell: |
        cd /tmp/test_backup
        echo "test123" | gpg --batch --yes --decrypt --passphrase-fd 0 --output decrypted_test_db.sql test_db.sql.gpg
      become_user: root
        
    - name: Verify decrypted file
      stat:
        path: /tmp/test_backup/decrypted_test_db.sql
      register: decrypted_file
        
    - name: Check decryption result
      assert:
        that:
          - decrypted_file.stat.exists
          - decrypted_file.stat.size > 0
        fail_msg: "Decrypted backup file was not created or is empty"
        success_msg: "GPG backup decryption successful"
        
    - name: Compare original and decrypted files
      shell: |
        diff /tmp/test_backup/test_db.sql /tmp/test_backup/decrypted_test_db.sql
      register: diff_result
      failed_when: diff_result.rc != 0
        
    - name: Display test results
      debug:
        msg:
          - "✅ GPG backup encryption test: PASSED"
          - "✅ GPG backup decryption test: PASSED"
          - "✅ File integrity verification: PASSED"
          - "📊 Original file size: {{ (decrypted_file.stat.size / 1024) | round(2) }} KB"
          - "🔐 Encrypted file size: {{ (encrypted_file.stat.size / 1024) | round(2) }} KB"
          
    - name: Cleanup test files
      file:
        path: /tmp/test_backup
        state: absent
      ignore_errors: yes