---
- name: "Kompleksowa diagnoza konfiguracji Baserow na serwerze simetria-hetzner001"
  hosts: simetria-hetzner001
  become: yes
  gather_facts: yes
  vars:
    test_timestamp: "{{ ansible_date_time.iso8601 }}"
    baserow_domain: "baserow.simetria.pl"
    
  tasks:
    - name: "Pokaż informacje o systemie"
      debug:
        msg: |
          TEST: System Information Check
          EXECUTED: {{ test_timestamp }}
          Server: {{ inventory_hostname }} ({{ ansible_host }})
          OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
          Architecture: {{ ansible_architecture }}
          Memory: {{ ansible_memtotal_mb }}MB
          CPU: {{ ansible_processor_vcpus }} vCPU

    # ==========================================================================
    # ANALIZA KONFIGURACJI DOCKER I ZMIENNYCH ŚRODOWISKOWYCH
    # ==========================================================================
    
    - name: "TEST: Sprawdzenie statusu Docker"
      systemd:
        name: docker
      register: docker_status

    - name: "Wyświetl status Docker"
      debug:
        msg: |
          TEST: Docker Service Status
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ docker_status.status.ActiveState }}
          STATUS: {{ 'SUCCESS' if docker_status.status.ActiveState == 'active' else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie wersji Docker i Docker Compose"
      shell: |
        echo "Docker version: $(docker --version)"
        echo "Docker Compose version: $(docker compose version)"
      register: docker_versions
      ignore_errors: yes

    - name: "Wyświetl wersje Docker"
      debug:
        msg: |
          TEST: Docker Versions Check
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ docker_versions.stdout }}
          STATUS: {{ 'SUCCESS' if docker_versions.rc == 0 else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie struktury katalogów Baserow"
      find:
        paths: /opt/baserow
        file_type: directory
        recurse: yes
      register: baserow_directories
      ignore_errors: yes

    - name: "Wyświetl strukturę katalogów Baserow"
      debug:
        msg: |
          TEST: Baserow Directory Structure
          EXECUTED: {{ test_timestamp }}
          RESULT: Znalezione katalogi - {{ baserow_directories.files | length }}
          STATUS: {{ 'SUCCESS' if baserow_directories.files | length > 0 else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie docker-compose.yml"
      stat:
        path: /opt/baserow/docker-compose.yml
      register: compose_file

    - name: "Odczytaj docker-compose.yml"
      slurp:
        src: /opt/baserow/docker-compose.yml
      register: compose_content
      when: compose_file.stat.exists

    - name: "Wyświetl informacje o docker-compose.yml"
      debug:
        msg: |
          TEST: Docker Compose Configuration Check
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ 'Plik istnieje' if compose_file.stat.exists else 'Plik nie istnieje' }}
          STATUS: {{ 'SUCCESS' if compose_file.stat.exists else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie zmiennych środowiskowych w docker-compose.yml"
      shell: |
        if [ -f /opt/baserow/docker-compose.yml ]; then
          echo "=== BASEROW_PUBLIC_URL ==="
          grep -i "BASEROW_PUBLIC_URL" /opt/baserow/docker-compose.yml || echo "Nie znaleziono BASEROW_PUBLIC_URL"
          echo ""
          echo "=== Mapowanie portów ==="
          grep -E "ports:|8000|3000" /opt/baserow/docker-compose.yml || echo "Nie znaleziono mapowania portów"
          echo ""
          echo "=== Konfiguracja domeny ==="
          grep -E "baserow\.simetria\.pl|ALLOWED_HOSTS|CSRF_TRUSTED_ORIGINS" /opt/baserow/docker-compose.yml || echo "Nie znaleziono konfiguracji domeny"
        else
          echo "Docker compose file nie istnieje"
        fi
      register: env_vars_check
      ignore_errors: yes

    - name: "Wyświetl analizę zmiennych środowiskowych"
      debug:
        msg: |
          TEST: Environment Variables Analysis
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ env_vars_check.stdout }}
          STATUS: {{ 'SUCCESS' if env_vars_check.rc == 0 else 'FAILED' }}
          CHANGES: Nie

    # ==========================================================================
    # ANALIZA KONTENERÓW DOCKER
    # ==========================================================================

    - name: "TEST: Sprawdzenie statusu kontenerów Baserow"
      shell: |
        echo "=== Status wszystkich kontenerów ==="
        docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        echo "=== Kontenery Baserow ==="
        docker ps -a --filter "name=baserow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
      register: containers_status
      ignore_errors: yes

    - name: "Wyświetl status kontenerów"
      debug:
        msg: |
          TEST: Docker Containers Status
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ containers_status.stdout }}
          STATUS: {{ 'SUCCESS' if containers_status.rc == 0 else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie healthcheck kontenerów"
      shell: |
        echo "=== Healthcheck status ==="
        for container in $(docker ps --filter "name=baserow" --format "{{.Names}}"); do
          echo "Container: $container"
          docker inspect --format='{{.State.Health.Status}}' $container 2>/dev/null || echo "Brak healthcheck"
          echo "---"
        done
      register: healthcheck_status
      ignore_errors: yes

    - name: "Wyświetl status healthcheck"
      debug:
        msg: |
          TEST: Container Health Check Status
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ healthcheck_status.stdout }}
          STATUS: {{ 'SUCCESS' if healthcheck_status.rc == 0 else 'PARTIAL' }}
          CHANGES: Nie

    # ==========================================================================
    # ANALIZA LOGÓW KONTENERÓW
    # ==========================================================================

    - name: "TEST: Zbieranie logów kontenerów Baserow (ostatnie 50 linii)"
      shell: |
        for container in $(docker ps --filter "name=baserow" --format "{{.Names}}"); do
          echo "========== LOGI KONTENERA: $container =========="
          docker logs --tail 50 $container 2>&1 | head -50
          echo ""
        done
      register: container_logs
      ignore_errors: yes

    - name: "Wyświetl logi kontenerów"
      debug:
        msg: |
          TEST: Container Logs Collection
          EXECUTED: {{ test_timestamp }}
          RESULT: Logi zebrane dla {{ (container_logs.stdout.split('==========') | length - 1) // 2 }} kontenerów
          STATUS: {{ 'SUCCESS' if container_logs.rc == 0 else 'FAILED' }}
          CHANGES: Nie
          
          LOGS:
          {{ container_logs.stdout }}

    # ==========================================================================
    # ANALIZA NGINX
    # ==========================================================================

    - name: "TEST: Sprawdzenie statusu Nginx"
      systemd:
        name: nginx
      register: nginx_status
      ignore_errors: yes

    - name: "Wyświetl status Nginx"
      debug:
        msg: |
          TEST: Nginx Service Status
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ nginx_status.status.ActiveState if nginx_status.status is defined else 'Nieznany' }}
          STATUS: {{ 'SUCCESS' if nginx_status.status is defined and nginx_status.status.ActiveState == 'active' else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie konfiguracji Nginx dla Baserow"
      shell: |
        echo "=== Pliki konfiguracyjne Nginx ==="
        find /etc/nginx -name "*baserow*" -type f 2>/dev/null || echo "Brak plików konfiguracyjnych Baserow"
        echo ""
        echo "=== Test konfiguracji Nginx ==="
        nginx -t 2>&1
        echo ""
        echo "=== Sprawdzenie nasłuchiwania portów ==="
        nginx -T 2>/dev/null | grep -E "listen.*80|listen.*443" | head -10
      register: nginx_config_check
      ignore_errors: yes

    - name: "Wyświetl analizę konfiguracji Nginx"
      debug:
        msg: |
          TEST: Nginx Configuration Analysis
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ nginx_config_check.stdout }}
          STATUS: {{ 'SUCCESS' if 'syntax is ok' in nginx_config_check.stdout else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Sprawdzenie logów Nginx"
      shell: |
        echo "=== Ostatnie wpisy w access.log ==="
        tail -20 /var/log/nginx/access.log 2>/dev/null || echo "Brak access.log"
        echo ""
        echo "=== Ostatnie błędy w error.log ==="
        tail -20 /var/log/nginx/error.log 2>/dev/null || echo "Brak error.log"
        echo ""
        echo "=== Logi związane z baserow.simetria.pl ==="
        find /var/log/nginx -name "*baserow*" -type f -exec tail -10 {} \; 2>/dev/null || echo "Brak logów Baserow"
      register: nginx_logs
      ignore_errors: yes

    - name: "Wyświetl logi Nginx"
      debug:
        msg: |
          TEST: Nginx Logs Analysis
          EXECUTED: {{ test_timestamp }}
          RESULT: Logi zebrane
          STATUS: {{ 'SUCCESS' if nginx_logs.rc == 0 else 'PARTIAL' }}
          CHANGES: Nie
          
          LOGS:
          {{ nginx_logs.stdout }}

    # ==========================================================================
    # TESTOWANIE POŁĄCZEŃ SIECIOWYCH
    # ==========================================================================

    - name: "TEST: Sprawdzenie otwartych portów"
      shell: |
        echo "=== Porty nasłuchujące (netstat) ==="
        netstat -tlnp | grep -E ":80|:443|:3000|:8000" || echo "Brak nasłuchujących portów"
        echo ""
        echo "=== Porty nasłuchujące (ss) ==="
        ss -tlnp | grep -E ":80|:443|:3000|:8000" || echo "Brak nasłuchujących portów"
      register: listening_ports
      ignore_errors: yes

    - name: "Wyświetl otwarte porty"
      debug:
        msg: |
          TEST: Network Ports Listening
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ listening_ports.stdout }}
          STATUS: {{ 'SUCCESS' if listening_ports.rc == 0 else 'FAILED' }}
          CHANGES: Nie

    - name: "TEST: Test lokalnej dostępności"
      shell: |
        echo "=== Test localhost:80 ==="
        curl -s -I -m 5 http://localhost/ 2>&1 | head -5 || echo "Połączenie nie udane"
        echo ""
        echo "=== Test localhost:443 ==="
        curl -s -I -m 5 -k https://localhost/ 2>&1 | head -5 || echo "Połączenie nie udane"
        echo ""
        echo "=== Test 127.0.0.1:3000 ==="
        curl -s -I -m 5 http://127.0.0.1:3000/ 2>&1 | head -5 || echo "Połączenie nie udane"
        echo ""
        echo "=== Test 127.0.0.1:8000 ==="
        curl -s -I -m 5 http://127.0.0.1:8000/ 2>&1 | head -5 || echo "Połączenie nie udane"
      register: local_connectivity
      ignore_errors: yes

    - name: "Wyświetl testy lokalnej dostępności"
      debug:
        msg: |
          TEST: Local Connectivity Testing
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ local_connectivity.stdout }}
          STATUS: {{ 'SUCCESS' if local_connectivity.rc == 0 else 'PARTIAL' }}
          CHANGES: Nie

    - name: "TEST: Test zewnętrznej dostępności domeny"
      shell: |
        echo "=== DNS Resolution Test ==="
        nslookup {{ baserow_domain }} 2>&1 || echo "DNS resolution failed"
        echo ""
        echo "=== External HTTP Test ==="
        curl -s -I -m 10 http://{{ baserow_domain }}/ 2>&1 | head -5 || echo "HTTP połączenie nie udane"
        echo ""
        echo "=== External HTTPS Test ==="
        curl -s -I -m 10 -k https://{{ baserow_domain }}/ 2>&1 | head -5 || echo "HTTPS połączenie nie udane"
      register: external_connectivity
      ignore_errors: yes

    - name: "Wyświetl testy zewnętrznej dostępności"
      debug:
        msg: |
          TEST: External Domain Connectivity Testing
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ external_connectivity.stdout }}
          STATUS: {{ 'SUCCESS' if 'HTTP' in external_connectivity.stdout else 'FAILED' }}
          CHANGES: Nie

    # ==========================================================================
    # ANALIZA FIREWALL I BEZPIECZEŃSTWA
    # ==========================================================================

    - name: "TEST: Sprawdzenie statusu UFW"
      shell: |
        echo "=== Status UFW ==="
        ufw status verbose 2>/dev/null || echo "UFW nie jest dostępne"
        echo ""
        echo "=== Reguły iptables ==="
        iptables -L -n | grep -E "80|443|3000|8000" || echo "Brak reguł dla portów Baserow"
      register: firewall_status
      ignore_errors: yes

    - name: "Wyświetl status firewall"
      debug:
        msg: |
          TEST: Firewall Configuration Check
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ firewall_status.stdout }}
          STATUS: {{ 'SUCCESS' if firewall_status.rc == 0 else 'PARTIAL' }}
          CHANGES: Nie

    # ==========================================================================
    # ANALIZA SYSTEMU PLIKÓW I UPRAWNIEŃ
    # ==========================================================================

    - name: "TEST: Sprawdzenie uprawnień katalogów Baserow"
      shell: |
        echo "=== Katalogi Baserow ==="
        ls -la /opt/baserow/ 2>/dev/null || echo "Katalog /opt/baserow nie istnieje"
        echo ""
        echo "=== Volumes Docker ==="
        ls -la /opt/baserow/volumes/ 2>/dev/null || echo "Katalog volumes nie istnieje"
        echo ""
        echo "=== Użytkownik baserow ==="
        id baserow 2>/dev/null || echo "Użytkownik baserow nie istnieje"
      register: permissions_check
      ignore_errors: yes

    - name: "Wyświetl sprawdzenie uprawnień"
      debug:
        msg: |
          TEST: File Permissions and User Check
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ permissions_check.stdout }}
          STATUS: {{ 'SUCCESS' if permissions_check.rc == 0 else 'FAILED' }}
          CHANGES: Nie

    # ==========================================================================
    # PODSUMOWANIE DIAGNOZY
    # ==========================================================================

    - name: "PODSUMOWANIE: Wszystkie testy zostały zakończone"
      debug:
        msg: |
          ==========================================
          KOMPLEKSOWA DIAGNOZA BASEROW - ZAKOŃCZONA
          ==========================================
          
          Data wykonania: {{ test_timestamp }}
          Serwer: {{ inventory_hostname }} ({{ ansible_host }})
          Domena: {{ baserow_domain }}
          
          WYMAGANE DZIAŁANIA:
          1. Sprawdź logi kontenerów pod kątem błędów
          2. Zweryfikuj konfigurację BASEROW_PUBLIC_URL
          3. Sprawdź mapowanie portów w docker-compose.yml
          4. Przeanalizuj konfigurację nginx dla domeny
          5. Sprawdź status kontenerów i ich healthcheck
          
          DIAGNOZA ZAKOŃCZONA - BEZ WPROWADZANIA ZMIAN