---
# Wsp<PERSON>lne zadania dla wszystkich serwerów

- name: "Rozpoczęcie konfiguracji wspólnej"
  debug:
    msg:
      - "=== KONFIGURACJA WSPÓLNA ==="
      - "Serwer: {{ ansible_hostname }}"
      - "System: {{ ansible_distribution }} {{ ansible_distribution_version }}"
      - "Architektura: {{ ansible_architecture }}"
  tags: [common, info]

# =============================================================================
# AKTUALIZACJA SYSTEMU
# =============================================================================

- name: "Aktualizuj cache pakietów"
  apt:
    update_cache: true
    cache_valid_time: 3600
  become: true
  tags: [common, apt, update]

- name: "Zaktualizuj wszystkie pakiety systemowe"
  apt:
    upgrade: dist
    autoremove: true
  become: true
  register: system_update
  tags: [common, apt, upgrade]

- name: "Wyświetl informacje o aktualizacji"
  debug:
    msg: "Pakiety zostały zaktualizowane: {{ system_update.changed }}"
  tags: [common, info]

# =============================================================================
# INSTALACJA PODSTAWOWYCH PAKIETÓW
# =============================================================================

- name: "Zainstaluj niezbędne pakiety systemowe"
  apt:
    name:
      - curl
      - wget
      - gnupg
      - lsb-release
      - ca-certificates
      - software-properties-common
      - apt-transport-https
      - python3-pip
      - python3-setuptools
      - unzip
      - tree
      - htop
      - rsync
      - logrotate
      - cron
      - vim
      - nano
      - git
    state: present
  become: true
  tags: [common, apt, packages]

# =============================================================================
# KONFIGURACJA CZASU I STREFY CZASOWEJ
# =============================================================================

- name: "Ustaw timezone na Europe/Warsaw"
  timezone:
    name: Europe/Warsaw
  become: true
  notify: restart cron
  tags: [common, timezone]

- name: "Sprawdź czy NTP jest skonfigurowane"
  shell: timedatectl status | grep "NTP synchronized" | awk '{print $3}'
  register: ntp_status
  changed_when: false
  tags: [common, ntp]

- name: "Wyświetl status synchronizacji czasu"
  debug:
    msg: "NTP synchronizacja: {{ ntp_status.stdout }}"
  tags: [common, info]

# =============================================================================
# KONFIGURACJA SSH (PODSTAWOWA)
# =============================================================================

- name: "Sprawdź konfigurację SSH"
  lineinfile:
    path: /etc/ssh/sshd_config
    regexp: "^#?{{ item.key }}"
    line: "{{ item.key }} {{ item.value }}"
    state: present
    backup: true
  become: true
  loop:
    - { key: "PermitRootLogin", value: "no" }
    - { key: "PasswordAuthentication", value: "no" }
    - { key: "PubkeyAuthentication", value: "yes" }
    - { key: "X11Forwarding", value: "no" }
  notify: restart ssh
  tags: [common, ssh, security]

# =============================================================================
# KONFIGURACJA UŻYTKOWNIKÓW
# =============================================================================

- name: "Sprawdź czy użytkownik ansible ma sudo"
  lineinfile:
    path: /etc/sudoers.d/{{ ansible_user }}
    line: "{{ ansible_user }} ALL=(ALL) NOPASSWD:ALL"
    create: true
    mode: '0440'
    validate: 'visudo -cf %s'
  become: true
  tags: [common, user, sudo]

# =============================================================================
# OPTYMALIZACJA SYSTEMU
# =============================================================================

- name: "Sprawdź dostępne miejsce na dysku"
  shell: df -h / | awk 'NR==2 {print $4}'
  register: disk_space
  changed_when: false
  tags: [common, disk]

- name: "Wyświetl dostępne miejsce na dysku"
  debug:
    msg: "Dostępne miejsce na dysku: {{ disk_space.stdout }}"
  tags: [common, info]

- name: "Sprawdź czy jest wystarczająco miejsca na dysku (minimum 2GB)"
  shell: df / | awk 'NR==2 {print $4}'
  register: disk_space_kb
  failed_when: disk_space_kb.stdout | int < 2097152  # 2GB w KB
  changed_when: false
  tags: [common, disk]

- name: "Wyświetl informacje o pamięci RAM"
  debug:
    msg: "Dostępna pamięć RAM: {{ ansible_memory_mb.nocache.free }}MB (wolna: {{ ansible_memfree_mb }}MB, całkowita: {{ ansible_memtotal_mb }}MB)"
  tags: [common, info]

# =============================================================================
# PODSUMOWANIE
# =============================================================================

- name: "Podsumowanie konfiguracji wspólnej"
  debug:
    msg:
      - "=== KONFIGURACJA WSPÓLNA ZAKOŃCZONA ==="
      - "Hostname: {{ ansible_hostname }}"
      - "OS: {{ ansible_distribution }} {{ ansible_distribution_version }}"
      - "Kernel: {{ ansible_kernel }}"
      - "Architektura: {{ ansible_architecture }}"
      - "Pamięć: {{ ansible_memtotal_mb }}MB"
      - "Dostępne miejsce: {{ disk_space.stdout }}"
      - "Timezone: {{ ansible_date_time.tz }}"
      - "SSH skonfigurowane: ✅"
      - "Pakiety zaktualizowane: {{ system_update.changed }}"
  tags: [common, info]
