---
# defaults file for common role

# Podstawowe ustawienia systemu
timezone: "Europe/Warsaw"
locale: "en_US.UTF-8"

# Konfiguracja SSH
ssh_permit_root_login: "no"
ssh_password_authentication: "no"
ssh_pubkey_authentication: "yes"
ssh_x11_forwarding: "no"

# Minimalne wymagania systemowe
min_disk_space_gb: 2
min_ram_mb: 512

# Pakiety do zainstalowania
common_packages:
  - curl
  - wget
  - gnupg
  - lsb-release
  - ca-certificates
  - software-properties-common
  - apt-transport-https
  - python3-pip
  - python3-setuptools
  - unzip
  - tree
  - htop
  - rsync
  - logrotate
  - cron
  - vim
  - nano
  - git
