---
# Konfiguracja SSL (Let's Encrypt) dla Baserow

- name: "Rozpoczęcie konfiguracji SSL"
  debug:
    msg:
      - "=== KONFIGURACJA SSL ==="
      - "Serwer: {{ ansible_hostname }}"
      - "Domena: {{ baserow_domain }}"
      - "Email: {{ ssl_email }}"
  tags: [ssl, info]

# =============================================================================
# INSTALACJA CERTBOT
# =============================================================================

- name: "Zainstaluj snapd"
  apt:
    name: snapd
    state: present
    update_cache: true
  become: true
  tags: [ssl, install]

- name: "Zainstaluj certbot przez snap"
  snap:
    name: certbot
    classic: true
  become: true
  tags: [ssl, install]

- name: "Stwórz symlink dla certbot"
  file:
    src: /snap/bin/certbot
    dest: /usr/bin/certbot
    state: link
  become: true
  tags: [ssl, install]

# =============================================================================
# PRZYGOTOWANIE KATALOGÓW
# =============================================================================

- name: "Stwórz katalog dla Let's Encrypt challenge"
  file:
    path: /var/www/certbot
    state: directory
    owner: www-data
    group: www-data
    mode: '0755'
  become: true
  tags: [ssl, directories]

# =============================================================================
# SPRAWDZENIE ISTNIEJĄCYCH CERTYFIKATÓW
# =============================================================================

- name: "Sprawdź czy certyfikat Let's Encrypt już istnieje"
  stat:
    path: "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem"
  register: letsencrypt_cert_check
  tags: [ssl, check]

- name: "Wyświetl status certyfikatu"
  debug:
    msg: "Certyfikat Let's Encrypt: {{ 'Istnieje' if letsencrypt_cert_check.stat.exists else 'Nie istnieje' }}"
  tags: [ssl, info]

# =============================================================================
# UZYSKANIE CERTYFIKATU LET'S ENCRYPT
# =============================================================================

- name: "Uzyskaj certyfikat Let's Encrypt (tylko jeśli nie istnieje)"
  shell: |
    certbot --nginx \
      --non-interactive \
      --agree-tos \
      --email {{ ssl_email }} \
      --domains {{ baserow_domain }}{% for domain in baserow_alternative_domains %},{{ domain }}{% endfor %} \
      --redirect
  become: true
  when: not letsencrypt_cert_check.stat.exists
  register: certbot_result
  ignore_errors: true
  tags: [ssl, obtain]

- name: "Wyświetl rezultat uzyskania certyfikatu"
  debug:
    msg: 
      - "Certbot result: {{ certbot_result.rc if certbot_result is defined else 'Skipped' }}"
      - "{{ certbot_result.stdout_lines if certbot_result is defined and certbot_result.stdout_lines is defined else [] }}"
  when: certbot_result is defined
  tags: [ssl, info]

# =============================================================================
# KONFIGURACJA AUTOMATYCZNEGO ODNOWIENIA
# =============================================================================

- name: "Stwórz cron job dla odnowienia certyfikatów"
  cron:
    name: "Renew Let's Encrypt certificates"
    minute: "0"
    hour: "3"
    job: "certbot renew --quiet && systemctl reload nginx"
    user: root
  become: true
  tags: [ssl, cron]

- name: "Test automatycznego odnowienia"
  shell: certbot renew --dry-run
  become: true
  register: certbot_test
  changed_when: false
  ignore_errors: true
  tags: [ssl, test]

- name: "Wyświetl wynik testu odnowienia"
  debug:
    msg: "Test odnowienia: {{ 'OK' if certbot_test.rc == 0 else 'BŁĄD' }}"
  tags: [ssl, info]

# =============================================================================
# WERYFIKACJA CERTYFIKATU
# =============================================================================

- name: "Sprawdź status certyfikatu po konfiguracji"
  stat:
    path: "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem"
  register: final_cert_check
  tags: [ssl, verify]

- name: "Sprawdź ważność certyfikatu"
  shell: |
    if [ -f "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" ]; then
      openssl x509 -in "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" -noout -dates
    else
      echo "Certyfikat nie istnieje"
    fi
  register: cert_validity
  changed_when: false
  tags: [ssl, verify]

- name: "Test połączenia SSL"
  uri:
    url: "https://{{ baserow_domain }}/health"
    method: GET
    timeout: 10
    validate_certs: true
  register: ssl_test
  ignore_errors: true
  tags: [ssl, test]

# =============================================================================
# KONFIGURACJA BEZPIECZEŃSTWA SSL
# =============================================================================

- name: "Sprawdź konfigurację SSL w Nginx"
  shell: nginx -t
  become: true
  register: nginx_ssl_test
  changed_when: false
  tags: [ssl, verify]

# =============================================================================
# PODSUMOWANIE
# =============================================================================

- name: "Podsumowanie konfiguracji SSL"
  debug:
    msg:
      - "=== KONFIGURACJA SSL ZAKOŃCZONA ==="
      - ""
      - "🔐 CERTYFIKAT:"
      - "  Status: {{ 'Zainstalowany' if final_cert_check.stat.exists else 'Błąd instalacji' }}"
      - "  Domena: {{ baserow_domain }}"
      - "  Domeny alternatywne: {{ baserow_alternative_domains | join(', ') }}"
      - "  Email: {{ ssl_email }}"
      - ""
      - "📅 WAŻNOŚĆ:"
      - "{{ cert_validity.stdout_lines }}"
      - ""
      - "🔄 ODNOWIENIE:"
      - "  Automatyczne: Tak (codziennie o 3:00)"
      - "  Test: {{ 'OK' if certbot_test.rc == 0 else 'BŁĄD' }}"
      - ""
      - "✅ TESTY:"
      - "  Nginx config: {{ 'OK' if nginx_ssl_test.rc == 0 else 'BŁĄD' }}"
      - "  HTTPS połączenie: {{ '✅ OK' if ssl_test.status == 200 else '❌ BŁĄD' }}"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Sprawdź certyfikaty: sudo certbot certificates"
      - "  Odnów ręcznie: sudo certbot renew"
      - "  Test odnowienia: sudo certbot renew --dry-run"
      - ""
      - "📝 NASTĘPNE KROKI:"
      - "  1. Sprawdź HTTPS: https://{{ baserow_domain }}"
      - "  2. Test SSL: https://www.ssllabs.com/ssltest/"
  tags: [ssl, info]
