---
# defaults file for baserow_deployment

# Konfiguracja użytkownika
baserow_user: baserow
baserow_group: baserow
baserow_home: /opt/baserow

# Konfiguracja Baserow
baserow_domain: baserow.simetria.pl
baserow_alternative_domains: ["www.baserow.simetria.pl"]
baserow_port: 3000

# SSL configuration
ssl_email: "<EMAIL>"

# Konfiguracja backupów
backup_dir: /opt/baserow/backups

# Konfiguracja Docker
log_max_size: 10m
log_max_files: 3
# Konfiguracja obrazów Docker
baserow_postgres_image: "postgres:13"
baserow_redis_image: "redis:6.2"
baserow_backend_image: "baserow/backend:latest"
baserow_frontend_image: "baserow/web-frontend:latest"
baserow_nginx_image: "nginx:1.21"

# Konfiguracja bazy danych
baserow_db_name: baserow
baserow_db_user: baserow
baserow_db_password: "changeme" # This should be changed in production

# Konfiguracja aplikacji
baserow_secret_key: "changeme" # This should be changed in production
baserow_public_url: "https://{{ baserow_domain }}"
restart_policy: unless-stopped
baserow_backend_workers: 2

# Konfiguracja health checks
health_check_interval: 10s
health_check_timeout: 5s
health_check_retries: 5

# Konfiguracja optymalizacji
postgres_shared_buffers: "256MB"
postgres_effective_cache_size: "768MB"
postgres_work_mem: "4MB"
postgres_maintenance_work_mem: "64MB"
postgres_max_connections: 100
redis_maxmemory: "256mb"
redis_maxmemory_policy: "allkeys-lru"

# Konfiguracja email (domyślnie wyłączone)
email_smtp_enabled: false
email_smtp_host: ""
email_smtp_port: 587
email_smtp_user: ""
email_smtp_password: ""
email_from_address: "noreply@{{ baserow_domain }}"

# Tryb debugowania
debug_mode: false
compose_project_name: baserow

# Konfiguracja Nginx
nginx_client_max_body_size: "100M"
nginx_proxy_connect_timeout: "30s"
nginx_proxy_read_timeout: "30s"
nginx_hsts_enabled: true
nginx_hsts_max_age: "63072000"
nginx_hsts_include_subdomains: true
nginx_hsts_preload: true
nginx_csp_enabled: true
nginx_csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:; media-src 'self'; object-src 'none'; frame-src 'none'; base-uri 'self'; form-action 'self'"
nginx_csp_api_policy: "default-src 'none'; connect-src 'self'"
nginx_permissions_policy_enabled: true
nginx_permissions_policy: "camera=(), microphone=(), geolocation=(), gyroscope=(), magnetometer=(), payment=(), usb=(), fullscreen=(self), display-capture=()"
nginx_cross_origin_policy_enabled: true
nginx_cross_origin_embedder_policy: "require-corp"
nginx_cross_origin_opener_policy: "same-origin"
nginx_cross_origin_resource_policy: "same-origin"
nginx_security_headers_enabled: true
nginx_x_permitted_cross_domain_policies: "none"
nginx_clear_site_data_enabled: false
nginx_clear_site_data_policy: "cache, cookies, storage, executionContexts"
nginx_static_files_security_enabled: true
nginx_static_files_referrer_policy: "strict-origin-when-cross-origin"
nginx_static_files_cache_control: "public, max-age=31536000, immutable"

# Konfiguracja Fail2ban
fail2ban_enabled: true
fail2ban_maxretry: 5
fail2ban_bantime: "1h"
fail2ban_findtime: "10m"

# Konfiguracja szyfrowania backupów GPG
backup_encryption_enabled: false
backup_gpg_keyring_path: "{{ baserow_home }}/.gnupg"
backup_gpg_recipient: "backup@{{ baserow_domain }}"
backup_gpg_key_type: "RSA"
backup_gpg_key_length: 2048
backup_gpg_key_expire: "0"
backup_cron_enabled: true
backup_cron_hour: "2"
backup_cron_minute: "0"
backup_retention_days: 7
backup_test_restore_dir: "/tmp/baserow_restore_test"
backup_verification_enabled: true
backup_verification_schedule: "0 4 * * 0" # Weekly on Sunday at 4 AM
backup_verification_email: ""
backup_checksum_algorithm: "sha256"
backup_checksum_enabled: true
backup_notification_email: ""