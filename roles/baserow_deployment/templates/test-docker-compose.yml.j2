version: '3.4'
services:
  db:
    image: "{{ baserow_postgres_image }}"
    container_name: baserow_test_db
    restart: unless-stopped
    environment:
      - POSTGRES_USER={{ baserow_db_user | default('baserow') }}
      - POSTGRES_PASSWORD={{ baserow_db_password }}
      - POSTGRES_DB={{ baserow_db_name | default('baserow') }}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U {{ baserow_db_user | default('baserow') }}"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - baserow_test_data:/var/lib/postgresql/data

volumes:
  baserow_test_data: