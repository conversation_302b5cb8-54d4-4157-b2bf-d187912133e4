#!/bin/bash
# Skrypt backup bazy danych Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_DIR="{{ baserow_home }}/backups/database"
DOCKER_COMPOSE_FILE="{{ baserow_home }}/docker-compose.yml"
CONTAINER_NAME="baserow-postgres"
DB_NAME="{{ baserow_db_name }}"
DB_USER="{{ baserow_db_user }}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="baserow_db_${TIMESTAMP}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"
LOG_FILE="{{ baserow_home }}/backups/backup.log"
CHECKSUM_ALGORITHM="{{ backup_checksum_algorithm }}"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Funkcja szyfrowania GPG
encrypt_backup() {
    local input_file="$1"
    local output_file="$2"
    
    {% if backup_encryption_enabled %}
    if command -v gpg >/dev/null 2>&1; then
        log "Szyfrowanie backup za pomocą GPG..."
        
        # Sprawdź czy klucz odbiorcy istnieje
        if ! gpg --homedir "{{ backup_gpg_keyring_path }}" --list-keys "{{ backup_gpg_recipient }}" >/dev/null 2>&1; then
            log "❌ Nie znaleziono klucza GPG dla odbiorcy: {{ backup_gpg_recipient }}"
            return 1
        fi
        
        # Szyfruj plik
        if gpg --homedir "{{ backup_gpg_keyring_path }}" \
               --trust-model always \
               --cipher-algo AES256 \
               --compress-algo 2 \
               --recipient "{{ backup_gpg_recipient }}" \
               --encrypt \
               --output "$output_file" \
               "$input_file"; then
            log "✅ Backup zaszyfrowany pomyślnie: $output_file"
            # Usuń niezaszyfrowany plik
            rm -f "$input_file"
            return 0
        else
            log "❌ Szyfrowanie backup nie powiodło się"
            return 1
        fi
    else
        log "❌ GPG nie jest zainstalowany, pomijanie szyfrowania"
        mv "$input_file" "$output_file"
        return 0
    fi
    {% else %}
    log "Szyfrowanie wyłączone, kopiowanie pliku..."
    mv "$input_file" "$output_file"
    return 0
    {% endif %}
}

# Funkcja generowania checksum
generate_checksum() {
    local file_path="$1"
    local checksum_file="${file_path}.${CHECKSUM_ALGORITHM}"
    
    {% if backup_checksum_enabled %}
    if command -v ${CHECKSUM_ALGORITHM}sum >/dev/null 2>&1; then
        log "Generowanie checksum ${CHECKSUM_ALGORITHM} dla $(basename "$file_path")..."
        
        # Generuj checksum w katalogu pliku
        if cd "$(dirname "$file_path")" && ${CHECKSUM_ALGORITHM}sum "$(basename "$file_path")" > "$(basename "$checksum_file")"; then
            log "✅ Checksum wygenerowany: $(basename "$checksum_file")"
            return 0
        else
            log "❌ Generowanie checksum nie powiodło się"
            return 1
        fi
    else
        log "❌ ${CHECKSUM_ALGORITHM}sum nie jest dostępny"
        return 1
    fi
    {% else %}
    log "Generowanie checksum wyłączone"
    return 0
    {% endif %}
}

cleanup() {
    if [[ -f "$BACKUP_DIR/$BACKUP_FILE" ]]; then
        rm -f "$BACKUP_DIR/$BACKUP_FILE"
    fi
}

# Przechwytywanie sygnałów
trap cleanup EXIT

# Test mode
if [[ "${1:-}" == "--test" ]]; then
    log "=== TEST MODE ==="
    echo "Sprawdzanie konfiguracji backup..."
    
    # Sprawdź czy katalog istnieje
    if [[ ! -d "$BACKUP_DIR" ]]; then
        echo "❌ Katalog backup nie istnieje: $BACKUP_DIR"
        exit 1
    fi
    
    # Sprawdź czy docker-compose działa
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME"; then
        echo "❌ Kontener bazy danych nie działa: $CONTAINER_NAME"
        exit 1
    fi
    
    # Sprawdź dostęp do bazy
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ Połączenie z bazą danych: OK"
    else
        echo "❌ Brak dostępu do bazy danych"
        exit 1
    fi
    
    echo "✅ Test zakończony pomyślnie"
    exit 0
fi

# Główny proces backup
log "=== ROZPOCZĘCIE BACKUP BAZY DANYCH ==="

# Sprawdź czy kontener działa
if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME"; then
    log "❌ Kontener bazy danych nie działa!"
    exit 1
fi

# Wykonaj backup
log "Tworzenie backup bazy danych..."
docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --verbose \
    --clean \
    --if-exists \
    --no-owner \
    --no-privileges > "$BACKUP_DIR/$BACKUP_FILE"

# Sprawdź czy backup się powiódł
if [[ ! -f "$BACKUP_DIR/$BACKUP_FILE" ]] || [[ ! -s "$BACKUP_DIR/$BACKUP_FILE" ]]; then
    log "❌ Backup nie powiódł się!"
    exit 1
fi

# Kompresuj backup
log "Kompresowanie backup..."
gzip "$BACKUP_DIR/$BACKUP_FILE"

# Zaszyfruj backup jeśli włączone
{% if backup_encryption_enabled %}
FINAL_FILE="${COMPRESSED_FILE}.gpg"
{% else %}
FINAL_FILE="$COMPRESSED_FILE"
{% endif %}

if ! encrypt_backup "$BACKUP_DIR/$COMPRESSED_FILE" "$BACKUP_DIR/$FINAL_FILE"; then
    log "❌ Szyfrowanie backup nie powiodło się!"
    exit 1
fi

# Generuj checksum dla finalnego pliku
if ! generate_checksum "$BACKUP_DIR/$FINAL_FILE"; then
    log "❌ Generowanie checksum nie powiodło się!"
    exit 1
fi

# Sprawdź rozmiar
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$FINAL_FILE" | cut -f1)
log "✅ Backup zakończony pomyślnie: $FINAL_FILE ($BACKUP_SIZE)"

# Wyświetl statystyki
log "=== STATYSTYKI BACKUP ==="
log "Plik: $FINAL_FILE"
log "Rozmiar: $BACKUP_SIZE"
log "Katalog: $BACKUP_DIR"
{% if backup_encryption_enabled %}
log "Szyfrowanie: Włączone (GPG)"
log "Odbiorca: {{ backup_gpg_recipient }}"
log "Liczba backupów: $(find "$BACKUP_DIR" -name "*.sql.gz.gpg" | wc -l)"

# Sprawdź stare backupy
OLD_BACKUPS=$(find "$BACKUP_DIR" -name "*.sql.gz.gpg" -mtime +{{ backup_retention_days }} | wc -l)
{% else %}
log "Szyfrowanie: Wyłączone"
log "Liczba backupów: $(find "$BACKUP_DIR" -name "*.sql.gz" | wc -l)"

# Sprawdź stare backupy
OLD_BACKUPS=$(find "$BACKUP_DIR" -name "*.sql.gz" -mtime +{{ backup_retention_days }} | wc -l)
{% endif %}
if [[ $OLD_BACKUPS -gt 0 ]]; then
    log "⚠️  Znaleziono $OLD_BACKUPS starych backupów do usunięcia"
fi

log "=== BACKUP ZAKOŃCZONY ==="