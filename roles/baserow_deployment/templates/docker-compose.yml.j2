version: '3.8'

# Baserow Docker Compose - Zoptymalizowana konfiguracja dla {{ baserow_domain }}
# Wygenerowane przez Ansible - nie edytować ręcznie!

services:
  # ==========================================================================
  # POSTGRESQL DATABASE - Zoptymalizowana dla {{ ansible_memtotal_mb }}MB RAM
  # ==========================================================================
  postgres:
    image: {{ baserow_postgres_image }}
    container_name: baserow-postgres
    restart: {{ restart_policy }}
    environment:
      POSTGRES_DB: {{ baserow_db_name }}
      POSTGRES_USER: {{ baserow_db_user }}
      POSTGRES_PASSWORD: {{ baserow_db_password }}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      # Optymalizacja PostgreSQL
      POSTGRES_SHARED_BUFFERS: {{ postgres_shared_buffers }}
      POSTGRES_EFFECTIVE_CACHE_SIZE: {{ postgres_effective_cache_size }}
      POSTGRES_WORK_MEM: {{ postgres_work_mem }}
      POSTGRES_MAINTENANCE_WORK_MEM: {{ postgres_maintenance_work_mem }}
      POSTGRES_MAX_CONNECTIONS: {{ postgres_max_connections }}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U {{ baserow_db_user }} -d {{ baserow_db_name }}"]
      interval: {{ health_check_interval }}
      timeout: {{ health_check_timeout }}
      retries: {{ health_check_retries }}
    logging:
      driver: "json-file"
      options:
        max-size: "{{ log_max_size }}"
        max-file: "{{ log_max_files }}"

  # ==========================================================================
  # REDIS CACHE - Zoptymalizowany dla wydajności API
  # ==========================================================================
  redis:
    image: {{ baserow_redis_image }}
    container_name: baserow-redis
    restart: {{ restart_policy }}
    command: >
      redis-server
      --maxmemory {{ redis_maxmemory }}
      --maxmemory-policy {{ redis_maxmemory_policy }}
      --appendonly yes
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: {{ health_check_interval }}
      timeout: {{ health_check_timeout }}
      retries: {{ health_check_retries }}
    logging:
      driver: "json-file"
      options:
        max-size: "{{ log_max_size }}"
        max-file: "{{ log_max_files }}"

  # ==========================================================================
  # BASEROW BACKEND - API i logika biznesowa
  # ==========================================================================
  backend:
    image: {{ baserow_backend_image }}
    container_name: baserow-backend
    restart: {{ restart_policy }}
    ports:
      - "127.0.0.1:8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Konfiguracja bazy danych
      DATABASE_URL: postgresql://{{ baserow_db_user }}:{{ baserow_db_password }}@postgres:5432/{{ baserow_db_name }}
      REDIS_URL: redis://redis:6379
      
      # Konfiguracja Baserow
      BASEROW_PUBLIC_URL: {{ baserow_public_url }}
      SECRET_KEY: {{ baserow_secret_key }}
      BASEROW_AMOUNT_OF_WORKERS: {{ baserow_backend_workers }}
      
      # Email (jeśli skonfigurowane)
{% if email_smtp_enabled %}
      EMAIL_SMTP: "true"
      EMAIL_SMTP_HOST: {{ email_smtp_host }}
      EMAIL_SMTP_PORT: {{ email_smtp_port }}
      EMAIL_SMTP_USER: {{ email_smtp_user }}
      EMAIL_SMTP_PASSWORD: {{ email_smtp_password }}
      FROM_EMAIL: {{ email_from_address }}
{% else %}
      EMAIL_SMTP: "false"
{% endif %}
      
      # Optymalizacja wydajności
      BASEROW_TRIGGER_SYNC_TEMPLATES_AFTER_MIGRATION: "false"
      BASEROW_SYNC_TEMPLATES_TIME_LIMIT: "30"
      
      # Debug (tylko dla development)
{% if debug_mode %}
      DEBUG: "true"
{% else %}
      DEBUG: "false"
{% endif %}
      
      # Bezpieczeństwo
      ALLOWED_HOSTS: {{ baserow_domain }},localhost,127.0.0.1
      CSRF_TRUSTED_ORIGINS: {{ baserow_public_url }}
      
    volumes:
      - baserow_data:/baserow/data
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/_health/"]
      interval: {{ health_check_interval }}
      timeout: {{ health_check_timeout }}
      retries: {{ health_check_retries }}
    logging:
      driver: "json-file"
      options:
        max-size: "{{ log_max_size }}"
        max-file: "{{ log_max_files }}"

  # ==========================================================================
  # BASEROW FRONTEND - Interfejs użytkownika
  # ==========================================================================
  frontend:
    image: {{ baserow_frontend_image }}
    container_name: baserow-frontend
    restart: {{ restart_policy }}
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    environment:
      BASEROW_PUBLIC_URL: {{ baserow_public_url }}
      PRIVATE_BACKEND_URL: http://backend:8000
      PUBLIC_BACKEND_URL: {{ baserow_public_url }}/api
      PUBLIC_WEB_FRONTEND_URL: {{ baserow_public_url }}
      INITIAL_TABLE_DATA_LIMIT: 500
      HOURS_UNTIL_TRASH_PERMANENTLY_DELETED: 72
    networks:
      - baserow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/_health/"]
      interval: {{ health_check_interval }}
      timeout: {{ health_check_timeout }}
      retries: {{ health_check_retries }}
    logging:
      driver: "json-file"
      options:
        max-size: "{{ log_max_size }}"
        max-file: "{{ log_max_files }}"


# =============================================================================
# VOLUMES - Persistent storage
# =============================================================================
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: {{ baserow_home }}/volumes/postgres_data
  
  baserow_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: {{ baserow_home }}/volumes/baserow_data
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: {{ baserow_home }}/volumes/redis_data
  
  nginx_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: {{ baserow_home }}/volumes/nginx_cache

# =============================================================================
# NETWORKS - Izolowana sieć dla kontenerów
# =============================================================================
networks:
  baserow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24