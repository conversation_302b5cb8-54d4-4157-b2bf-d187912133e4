#!/bin/bash
set -e
set -o pipefail

# Konfiguracja
# Ujednolicone zmienne z defaults/main.yml
BACKUP_DIR="{{ backup_dir }}"
ENCRYPTION_KEY_PATH="{{ backup_gpg_keyring_path }}/.gpg-passphrase"
ENCRYPTION_ENABLED="{{ backup_encryption_enabled }}"
DOCKER_COMPOSE_TEST_PATH="{{ baserow_home }}/test-docker-compose.yml"
LOG_FILE="{{ baserow_home }}/logs/backup_verification.log"
ADMIN_EMAIL="{{ backup_verification_email }}"
PERFORM_TEST_RESTORE="{{ backup_verification_enabled }}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
TEST_RESTORE_DIR="/tmp/baserow_restore_test_${TIMESTAMP}"
REPORT_FILE="/tmp/verification_report_${TIMESTAMP}.txt"

# Funkcja logowania
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Funkcja wysyłania raportu
send_report() {
    local subject="$1"
    log "Wysyłanie raportu e-mail: $subject"
    mail -s "$subject" "$ADMIN_EMAIL" < "$REPORT_FILE"
}

# Czyszczenie po teście
cleanup() {
    log "Czyszczenie środowiska testowego..."
    if [ "$PERFORM_TEST_RESTORE" = "true" ] && [ -f "$DOCKER_COMPOSE_TEST_PATH" ]; then
        docker-compose -f "$DOCKER_COMPOSE_TEST_PATH" down -v --remove-orphans >/dev/null 2>&1 || log "Nie udało się wyłączyć kontenerów testowych."
    fi
    rm -rf "$TEST_RESTORE_DIR"
    rm -f "$REPORT_FILE"
    log "Czyszczenie zakończone."
}

trap cleanup EXIT

# --- Główna logika skryptu ---
{
    log "Rozpoczynanie weryfikacji backupu..."
    
    LATEST_DB_BACKUP=$(ls -t "$BACKUP_DIR"/baserow_db_*.sql.gz* 2>/dev/null | head -n 1)
    LATEST_MEDIA_BACKUP=$(ls -t "$BACKUP_DIR"/baserow_media_*.tar.gz* 2>/dev/null | head -n 1)

    if [ -z "$LATEST_DB_BACKUP" ] || [ -z "$LATEST_MEDIA_BACKUP" ]; then
        log "BŁĄD: Nie znaleziono plików backupu."
        echo "BŁĄD: Nie znaleziono plików backupu." > "$REPORT_FILE"
        send_report "[BŁĄD] Weryfikacja backupu Baserow nie powiodła się"
        exit 1
    fi

    echo "--- Raport weryfikacji backupu Baserow ---"
    echo "Data: $(date)"
    echo "Najnowszy backup bazy danych: $(basename "$LATEST_DB_BACKUP")"
    echo "Najnowszy backup mediów: $(basename "$LATEST_MEDIA_BACKUP")"
    echo ""

    # 1. Weryfikacja sum kontrolnych
    log "Weryfikacja sum kontrolnych..."
    CHECKSUM_OK=true
    for backup_file in "$LATEST_DB_BACKUP" "$LATEST_MEDIA_BACKUP"; do
        checksum_file="${backup_file}.sha256"
        if [ -f "$checksum_file" ]; then
            if sha256sum -c "$checksum_file"; then
                log "Suma kontrolna poprawna dla $(basename "$backup_file")"
                echo "✅ Suma kontrolna poprawna dla: $(basename "$backup_file")"
            else
                log "BŁĄD: Suma kontrolna nie zgadza się dla $(basename "$backup_file")"
                echo "❌ BŁĄD: Suma kontrolna nie zgadza się dla $(basename "$backup_file")"
                CHECKSUM_OK=false
            fi
        else
            log "OSTRZEŻENIE: Brak pliku sumy kontrolnej dla $(basename "$backup_file")"
            echo "⚠️ OSTRZEŻENIE: Brak pliku sumy kontrolnej dla $(basename "$backup_file")"
        fi
    done
    
    if [ "$CHECKSUM_OK" = "false" ]; then
        send_report "[BŁĄD] Weryfikacja sum kontrolnych backupu Baserow nie powiodła się"
        exit 1
    fi

    # 2. Test deszyfrowania
    if [ "$ENCRYPTION_ENABLED" = "true" ]; then
        log "Testowanie deszyfrowania..."
        DECRYPTION_OK=true
        mkdir -p "$TEST_RESTORE_DIR"
        DECRYPTED_DB_PATH="$TEST_RESTORE_DIR/decrypted_db.sql.gz"
        DECRYPTED_MEDIA_PATH="$TEST_RESTORE_DIR/decrypted_media.tar.gz"

        if gpg --decrypt --batch --yes --passphrase-file "$ENCRYPTION_KEY_PATH" -o "$DECRYPTED_DB_PATH" "$LATEST_DB_BACKUP"; then
            log "Deszyfrowanie backupu bazy danych powiodło się."
            echo "✅ Deszyfrowanie backupu bazy danych powiodło się."
        else
            log "BŁĄD: Deszyfrowanie backupu bazy danych nie powiodło się."
            echo "❌ BŁĄD: Deszyfrowanie backupu bazy danych nie powiodło się."
            DECRYPTION_OK=false
        fi

        if gpg --decrypt --batch --yes --passphrase-file "$ENCRYPTION_KEY_PATH" -o "$DECRYPTED_MEDIA_PATH" "$LATEST_MEDIA_BACKUP"; then
            log "Deszyfrowanie backupu mediów powiodło się."
            echo "✅ Deszyfrowanie backupu mediów powiodło się."
        else
            log "BŁĄD: Deszyfrowanie backupu mediów nie powiodło się."
            echo "❌ BŁĄD: Deszyfrowanie backupu mediów nie powiodło się."
            DECRYPTION_OK=false
        fi

        if [ "$DECRYPTION_OK" = "false" ]; then
            send_report "[BŁĄD] Test deszyfrowania backupu Baserow nie powiódł się"
            exit 1
        fi
        LATEST_DB_BACKUP="$DECRYPTED_DB_PATH"
        LATEST_MEDIA_BACKUP="$DECRYPTED_MEDIA_PATH"
    fi

    # 3. Testowe odtworzenie
    if [ "$PERFORM_TEST_RESTORE" = "true" ]; then
        log "Przygotowanie do testowego odtworzenia..."
        # Plik test-docker-compose.yml jest już generowany przez Ansible,
        # więc nie ma potrzeby go kopiować.
        # cp "{{ baserow_home }}/test-docker-compose.yml.j2" "$DOCKER_COMPOSE_TEST_PATH"
        
        log "Uruchamianie środowiska testowego Docker..."
        docker-compose -f "$DOCKER_COMPOSE_TEST_PATH" up -d
        
        log "Oczekiwanie na gotowość bazy danych..."
        sleep 15

        log "Odtwarzanie bazy danych..."
        gunzip < "$LATEST_DB_BACKUP" | docker-compose -f "$DOCKER_COMPOSE_TEST_PATH" exec -T db psql -U baserow -d baserow
        
        log "Weryfikacja odtworzenia bazy danych..."
        TABLE_COUNT=$(docker-compose -f "$DOCKER_COMPOSE_TEST_PATH" exec -T db psql -U baserow -d baserow -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
        if [ "$(echo "$TABLE_COUNT" | tr -d '[:space:]')" -gt "0" ]; then
            log "Test odtworzenia bazy danych ZAKOŃCZONY POMYŚLNIE. Znaleziono $TABLE_COUNT tabel."
            echo "✅ Test odtworzenia bazy danych ZAKOŃCZONY POMYŚLNIE."
        else
            log "BŁĄD: Test odtworzenia bazy danych NIE POWIÓDŁ SIĘ."
            echo "❌ BŁĄD: Test odtworzenia bazy danych NIE POWIÓDŁ SIĘ."
            send_report "[BŁĄD] Test odtworzenia backupu Baserow nie powiódł się"
            exit 1
        fi
    fi

    log "Weryfikacja backupu zakończona pomyślnie."
    echo -e "\n🎉 Weryfikacja wszystkich etapów zakończona pomyślnie."
    send_report "[SUKCES] Weryfikacja backupu Baserow zakończona pomyślnie"

} &> "$REPORT_FILE"

exit 0