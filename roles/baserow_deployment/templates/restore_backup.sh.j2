#!/bin/bash
# Skrypt restore backup Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_DIR="{{ baserow_home }}/backups"
DOCKER_COMPOSE_FILE="{{ baserow_home }}/docker-compose.yml"
DB_NAME="{{ baserow_db_name }}"
DB_USER="{{ baserow_db_user }}"
LOG_FILE="{{ baserow_home }}/backups/restore.log"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Funkcja deszyfrowania GPG
decrypt_backup() {
    local input_file="$1"
    local output_file="$2"
    
    {% if backup_encryption_enabled %}
    if [[ "$input_file" == *.gpg ]]; then
        if command -v gpg >/dev/null 2>&1; then
            log "Deszyfrowanie backup za pomocą GPG..."
            
            # Sprawdź czy plik istnieje
            if [[ ! -f "$input_file" ]]; then
                log "❌ Zaszyfrowany plik backup nie istnieje: $input_file"
                return 1
            fi
            
            # Deszyfruj plik
            if gpg --homedir "{{ backup_gpg_keyring_path }}" \
                   --quiet \
                   --batch \
                   --decrypt \
                   --output "$output_file" \
                   "$input_file"; then
                log "✅ Backup odszyfrowany pomyślnie: $output_file"
                return 0
            else
                log "❌ Deszyfrowanie backup nie powiodło się"
                return 1
            fi
        else
            log "❌ GPG nie jest zainstalowany, nie można odszyfrować backup"
            return 1
        fi
    else
        log "Plik nie jest zaszyfrowany, kopiowanie..."
        cp "$input_file" "$output_file"
        return 0
    fi
    {% else %}
    log "Szyfrowanie wyłączone, kopiowanie pliku..."
    cp "$input_file" "$output_file"
    return 0
    {% endif %}
}

usage() {
    cat << EOF
UŻYCIE: $0 [OPCJE] [PLIK_BACKUP]

OPCJE:
    --database FILE     Restore tylko bazy danych z pliku
    --media FILE        Restore tylko plików media z pliku
    --full             Restore kompletny (ostatni backup)
    --list             Lista dostępnych backupów
    --help             Pokaż tę pomoc

PRZYKŁADY:
    $0 --list                                    # Lista backupów
    $0 --database baserow_db_20240101_120000.sql.gz  # Restore bazy
    $0 --media baserow_media_20240101_120000.tar.gz  # Restore media
    $0 --full                                    # Restore kompletny

UWAGA: Restore zatrzyma wszystkie kontenery Baserow!
EOF
}

list_backups() {
    log "=== DOSTĘPNE BACKUPY ==="
    
    echo "BACKUPY BAZY DANYCH:"
    {% if backup_encryption_enabled %}
    find "$BACKUP_DIR/database" -name "*.sql.gz.gpg" -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 | while read timestamp date time timezone file; do
        size=$(du -h "$file" | cut -f1)
        echo "  $(basename "$file") - $date $time ($size) [ZASZYFROWANY]"
    done
    {% else %}
    find "$BACKUP_DIR/database" -name "*.sql.gz" -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 | while read timestamp date time timezone file; do
        size=$(du -h "$file" | cut -f1)
        echo "  $(basename "$file") - $date $time ($size)"
    done
    {% endif %}
    
    echo ""
    echo "BACKUPY MEDIA:"
    {% if backup_encryption_enabled %}
    find "$BACKUP_DIR/media" -name "*.tar.gz.gpg" -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 | while read timestamp date time timezone file; do
        size=$(du -h "$file" | cut -f1)
        echo "  $(basename "$file") - $date $time ($size) [ZASZYFROWANY]"
    done
    {% else %}
    find "$BACKUP_DIR/media" -name "*.tar.gz" -printf "%T@ %Tc %p\n" 2>/dev/null | sort -n | tail -10 | while read timestamp date time timezone file; do
        size=$(du -h "$file" | cut -f1)
        echo "  $(basename "$file") - $date $time ($size)"
    done
    {% endif %}
}

confirm_restore() {
    echo ""
    echo "⚠️  OSTRZEŻENIE: Restore zastąpi obecne dane!"
    echo "Obecne dane zostaną utracone bezpowrotnie."
    echo ""
    read -p "Czy na pewno chcesz kontynuować? (wpisz 'TAK' aby potwierdzić): " confirm
    
    if [[ "$confirm" != "TAK" ]]; then
        echo "Restore anulowany przez użytkownika"
        exit 1
    fi
}

stop_containers() {
    log "Zatrzymywanie kontenerów Baserow..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Sprawdź czy kontenery się zatrzymały
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        log "❌ Błąd: Nie udało się zatrzymać wszystkich kontenerów"
        exit 1
    fi
    
    log "✅ Kontenery zatrzymane"
}

start_containers() {
    log "Uruchamianie kontenerów Baserow..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    # Czekaj na uruchomienie bazy danych
    log "Czekanie na uruchomienie bazy danych..."
    for i in {1..30}; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T database pg_isready -U "$DB_USER" >/dev/null 2>&1; then
            log "✅ Baza danych gotowa"
            break
        fi
        sleep 2
    done
    
    log "✅ Kontenery uruchomione"
}

restore_database() {
    local backup_file="$1"
    
    if [[ ! -f "$backup_file" ]]; then
        log "❌ Plik backup nie istnieje: $backup_file"
        exit 1
    fi
    
    log "=== RESTORE BAZY DANYCH ==="
    log "Plik: $backup_file"
    
    confirm_restore
    stop_containers
    
    # Uruchom tylko bazę danych
    log "Uruchamianie bazy danych..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d database
    
    # Czekaj na bazę danych
    log "Czekanie na bazę danych..."
    for i in {1..30}; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T database pg_isready -U "$DB_USER" >/dev/null 2>&1; then
            break
        fi
        sleep 2
    done
    
    # Deszyfruj backup jeśli potrzeba
    TEMP_FILE="/tmp/restore_db_$(date +%Y%m%d_%H%M%S).sql.gz"
    
    if ! decrypt_backup "$backup_file" "$TEMP_FILE"; then
        log "❌ Deszyfrowanie backup nie powiodło się!"
        exit 1
    fi
    
    # Wykonaj restore
    log "Wykonywanie restore bazy danych..."
    
    if [[ "$TEMP_FILE" == *.gz ]]; then
        gunzip -c "$TEMP_FILE" | docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T database psql -U "$DB_USER" -d "$DB_NAME"
    else
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T database psql -U "$DB_USER" -d "$DB_NAME" < "$TEMP_FILE"
    fi
    
    # Usuń tymczasowy plik
    rm -f "$TEMP_FILE"
    
    start_containers
    log "✅ Restore bazy danych zakończony pomyślnie"
}

restore_media() {
    local backup_file="$1"
    
    if [[ ! -f "$backup_file" ]]; then
        log "❌ Plik backup nie istnieje: $backup_file"
        exit 1
    fi
    
    log "=== RESTORE PLIKÓW MEDIA ==="
    log "Plik: $backup_file"
    
    confirm_restore
    stop_containers
    
    # Backup obecnych plików media
    if [[ -d "{{ baserow_home }}/data/media" ]]; then
        log "Tworzenie backup obecnych plików media..."
        mv "{{ baserow_home }}/data/media" "{{ baserow_home }}/data/media.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Deszyfruj backup jeśli potrzeba
    TEMP_FILE="/tmp/restore_media_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    if ! decrypt_backup "$backup_file" "$TEMP_FILE"; then
        log "❌ Deszyfrowanie backup nie powiodło się!"
        exit 1
    fi
    
    # Wykonaj restore
    log "Wykonywanie restore plików media..."
    tar -xzf "$TEMP_FILE" -C "{{ baserow_home }}/data/"
    
    # Usuń tymczasowy plik
    rm -f "$TEMP_FILE"
    
    # Ustaw właściciela
    chown -R {{ baserow_user }}:{{ baserow_user }} "{{ baserow_home }}/data/media"
    
    start_containers
    log "✅ Restore plików media zakończony pomyślnie"
}

restore_full() {
    log "=== PEŁNY RESTORE BASEROW ==="
    
    # Znajdź ostatnie backupy
    {% if backup_encryption_enabled %}
    LATEST_DB=$(find "$BACKUP_DIR/database" -name "*.sql.gz.gpg" -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2)
    LATEST_MEDIA=$(find "$BACKUP_DIR/media" -name "*.tar.gz.gpg" -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2)
    {% else %}
    LATEST_DB=$(find "$BACKUP_DIR/database" -name "*.sql.gz" -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2)
    LATEST_MEDIA=$(find "$BACKUP_DIR/media" -name "*.tar.gz" -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2)
    {% endif %}
    
    if [[ -z "$LATEST_DB" ]]; then
        log "❌ Nie znaleziono backup bazy danych"
        exit 1
    fi
    
    log "Najnowszy backup bazy: $(basename "$LATEST_DB")"
    if [[ -n "$LATEST_MEDIA" ]]; then
        log "Najnowszy backup media: $(basename "$LATEST_MEDIA")"
    fi
    
    {% if backup_encryption_enabled %}
    log "Backupy są zaszyfrowane GPG"
    {% endif %}
    
    confirm_restore
    
    # Restore bazy danych
    restore_database "$LATEST_DB"
    
    # Restore media jeśli dostępny
    if [[ -n "$LATEST_MEDIA" ]]; then
        restore_media "$LATEST_MEDIA"
    fi
    
    log "✅ Pełny restore zakończony pomyślnie"
}

# Główna logika
case "${1:-}" in
    --help|-h)
        usage
        exit 0
        ;;
    --list)
        list_backups
        exit 0
        ;;
    --database)
        if [[ -z "${2:-}" ]]; then
            echo "❌ Musisz podać plik backup bazy danych"
            usage
            exit 1
        fi
        restore_database "$BACKUP_DIR/database/$2"
        ;;
    --media)
        if [[ -z "${2:-}" ]]; then
            echo "❌ Musisz podać plik backup media"
            usage
            exit 1
        fi
        restore_media "$BACKUP_DIR/media/$2"
        ;;
    --full)
        restore_full
        ;;
    "")
        usage
        exit 1
        ;;
    *)
        echo "❌ Nieznana opcja: $1"
        usage
        exit 1
        ;;
esac