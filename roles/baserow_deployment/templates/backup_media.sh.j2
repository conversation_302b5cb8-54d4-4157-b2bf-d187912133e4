#!/bin/bash
# Skrypt backup plików media Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_DIR="{{ baserow_home }}/backups/media"
MEDIA_DIR="{{ baserow_home }}/data/media"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="baserow_media_${TIMESTAMP}.tar.gz"
LOG_FILE="{{ baserow_home }}/backups/backup.log"
CHECKSUM_ALGORITHM="{{ backup_checksum_algorithm }}"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Funkcja szyfrowania GPG
encrypt_backup() {
    local input_file="$1"
    local output_file="$2"
    
    {% if backup_encryption_enabled %}
    if command -v gpg >/dev/null 2>&1; then
        log "Szyfrowanie backup za pomocą GPG..."
        
        # Sprawdź czy klucz odbiorcy istnieje
        if ! gpg --homedir "{{ backup_gpg_keyring_path }}" --list-keys "{{ backup_gpg_recipient }}" >/dev/null 2>&1; then
            log "❌ Nie znaleziono klucza GPG dla odbiorcy: {{ backup_gpg_recipient }}"
            return 1
        fi
        
        # Szyfruj plik
        if gpg --homedir "{{ backup_gpg_keyring_path }}" \
               --trust-model always \
               --cipher-algo AES256 \
               --compress-algo 2 \
               --recipient "{{ backup_gpg_recipient }}" \
               --encrypt \
               --output "$output_file" \
               "$input_file"; then
            log "✅ Backup zaszyfrowany pomyślnie: $output_file"
            # Usuń niezaszyfrowany plik
            rm -f "$input_file"
            return 0
        else
            log "❌ Szyfrowanie backup nie powiodło się"
            return 1
        fi
    else
        log "❌ GPG nie jest zainstalowany, pomijanie szyfrowania"
        mv "$input_file" "$output_file"
        return 0
    fi
    {% else %}
    log "Szyfrowanie wyłączone, kopiowanie pliku..."
    mv "$input_file" "$output_file"
    return 0
    {% endif %}
}

# Funkcja generowania checksum
generate_checksum() {
    local file_path="$1"
    local checksum_file="${file_path}.${CHECKSUM_ALGORITHM}"
    
    {% if backup_checksum_enabled %}
    if command -v ${CHECKSUM_ALGORITHM}sum >/dev/null 2>&1; then
        log "Generowanie checksum ${CHECKSUM_ALGORITHM} dla $(basename "$file_path")..."
        
        # Generuj checksum w katalogu pliku
        if cd "$(dirname "$file_path")" && ${CHECKSUM_ALGORITHM}sum "$(basename "$file_path")" > "$(basename "$checksum_file")"; then
            log "✅ Checksum wygenerowany: $(basename "$checksum_file")"
            return 0
        else
            log "❌ Generowanie checksum nie powiodło się"
            return 1
        fi
    else
        log "❌ ${CHECKSUM_ALGORITHM}sum nie jest dostępny"
        return 1
    fi
    {% else %}
    log "Generowanie checksum wyłączone"
    return 0
    {% endif %}
}

# Test mode
if [[ "${1:-}" == "--test" ]]; then
    log "=== TEST MODE MEDIA ==="
    echo "Sprawdzanie konfiguracji backup media..."
    
    # Sprawdź katalogi
    if [[ ! -d "$BACKUP_DIR" ]]; then
        echo "❌ Katalog backup nie istnieje: $BACKUP_DIR"
        exit 1
    fi
    
    if [[ ! -d "$MEDIA_DIR" ]]; then
        echo "❌ Katalog media nie istnieje: $MEDIA_DIR"
        exit 1
    fi
    
    # Sprawdź miejsce na dysku
    AVAILABLE_SPACE=$(df "$BACKUP_DIR" | tail -1 | awk '{print $4}')
    MEDIA_SIZE=$(du -s "$MEDIA_DIR" | awk '{print $1}')
    
    if [[ $AVAILABLE_SPACE -lt $((MEDIA_SIZE * 2)) ]]; then
        echo "⚠️  Uwaga: Może brakować miejsca na dysku"
        echo "Dostępne: ${AVAILABLE_SPACE}K, Potrzebne: ~$((MEDIA_SIZE * 2))K"
    fi
    
    echo "✅ Test media zakończony pomyślnie"
    exit 0
fi

# Główny proces backup
log "=== ROZPOCZĘCIE BACKUP PLIKÓW MEDIA ==="

# Sprawdź czy katalog media istnieje
if [[ ! -d "$MEDIA_DIR" ]]; then
    log "❌ Katalog media nie istnieje: $MEDIA_DIR"
    exit 1
fi

# Sprawdź czy są pliki do backup
FILE_COUNT=$(find "$MEDIA_DIR" -type f | wc -l)
if [[ $FILE_COUNT -eq 0 ]]; then
    log "⚠️  Brak plików w katalogu media"
    exit 0
fi

# Wykonaj backup
log "Tworzenie archiwum plików media ($FILE_COUNT plików)..."
tar -czf "$BACKUP_DIR/$BACKUP_FILE" -C "{{ baserow_home }}/data" media/

# Sprawdź czy backup się powiódł
if [[ ! -f "$BACKUP_DIR/$BACKUP_FILE" ]] || [[ ! -s "$BACKUP_DIR/$BACKUP_FILE" ]]; then
    log "❌ Backup media nie powiódł się!"
    exit 1
fi

# Zaszyfruj backup jeśli włączone
{% if backup_encryption_enabled %}
FINAL_FILE="${BACKUP_FILE}.gpg"
{% else %}
FINAL_FILE="$BACKUP_FILE"
{% endif %}

if ! encrypt_backup "$BACKUP_DIR/$BACKUP_FILE" "$BACKUP_DIR/$FINAL_FILE"; then
    log "❌ Szyfrowanie backup nie powiodło się!"
    exit 1
fi

# Generuj checksum dla finalnego pliku
if ! generate_checksum "$BACKUP_DIR/$FINAL_FILE"; then
    log "❌ Generowanie checksum nie powiodło się!"
    exit 1
fi

# Wyświetl statystyki
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$FINAL_FILE" | cut -f1)
log "✅ Backup media zakończony pomyślnie: $FINAL_FILE ($BACKUP_SIZE)"

log "=== STATYSTYKI BACKUP MEDIA ==="
log "Plik: $FINAL_FILE"
log "Rozmiar: $BACKUP_SIZE"
log "Plików: $FILE_COUNT"
log "Katalog: $BACKUP_DIR"
{% if backup_encryption_enabled %}
log "Szyfrowanie: Włączone (GPG)"
log "Odbiorca: {{ backup_gpg_recipient }}"
{% else %}
log "Szyfrowanie: Wyłączone"
{% endif %}

log "=== BACKUP MEDIA ZAKOŃCZONY ==="