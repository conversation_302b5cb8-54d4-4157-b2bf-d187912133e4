#!/bin/bash
# Skrypt pełnego backup Baserow (baza + media)
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_SCRIPTS_DIR="{{ baserow_home }}/backups/scripts"
LOG_FILE="{{ baserow_home }}/backups/backup.log"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
CHECKSUM_ALGORITHM="{{ backup_checksum_algorithm }}"
BACKUP_DIR="{{ baserow_home }}/backups"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Funkcja generowania zbiorczego checksum
generate_summary_checksum() {
    {% if backup_checksum_enabled %}
    log "Generowanie zbiorczego checksum dla wszystkich backupów..."
    
    local summary_file="$BACKUP_DIR/backup_summary_${TIMESTAMP}.${CHECKSUM_ALGORITHM}"
    
    # <PERSON><PERSON><PERSON><PERSON><PERSON> najnowsze pliki backupów
    {% if backup_encryption_enabled %}
    local latest_db=$(find "$BACKUP_DIR/database" -name "*.sql.gz.gpg" -type f | sort -r | head -n 1)
    local latest_media=$(find "$BACKUP_DIR/media" -name "*.tar.gz.gpg" -type f | sort -r | head -n 1)
    {% else %}
    local latest_db=$(find "$BACKUP_DIR/database" -name "*.sql.gz" -type f | sort -r | head -n 1)
    local latest_media=$(find "$BACKUP_DIR/media" -name "*.tar.gz" -type f | sort -r | head -n 1)
    {% endif %}
    
    # Generuj summary checksum
    {
        echo "# Baserow Backup Summary Checksum - $(date)"
        echo "# Timestamp: $TIMESTAMP"
        echo "# Host: $(hostname)"
        echo ""
        
        if [[ -n "$latest_db" ]]; then
            echo "# Database backup:"
            cd "$(dirname "$latest_db")" && ${CHECKSUM_ALGORITHM}sum "$(basename "$latest_db")"
        fi
        
        if [[ -n "$latest_media" ]]; then
            echo "# Media backup:"
            cd "$(dirname "$latest_media")" && ${CHECKSUM_ALGORITHM}sum "$(basename "$latest_media")"
        fi
        
        echo ""
        echo "# Individual checksums:"
        if [[ -n "$latest_db" && -f "${latest_db}.${CHECKSUM_ALGORITHM}" ]]; then
            echo "# DB checksum file: $(basename "${latest_db}.${CHECKSUM_ALGORITHM}")"
            cat "${latest_db}.${CHECKSUM_ALGORITHM}"
        fi
        
        if [[ -n "$latest_media" && -f "${latest_media}.${CHECKSUM_ALGORITHM}" ]]; then
            echo "# Media checksum file: $(basename "${latest_media}.${CHECKSUM_ALGORITHM}")"
            cat "${latest_media}.${CHECKSUM_ALGORITHM}"
        fi
        
    } > "$summary_file"
    
    log "✅ Zbiorczy checksum wygenerowany: $(basename "$summary_file")"
    {% else %}
    log "Generowanie checksum wyłączone"
    {% endif %}
}

# Test mode
if [[ "${1:-}" == "--test" ]]; then
    log "=== TEST MODE FULL BACKUP ==="
    echo "Sprawdzanie wszystkich komponentów backup..."
    
    # Test backup bazy danych
    echo "Testing database backup..."
    if ! "$BACKUP_SCRIPTS_DIR/backup_database.sh" --test; then
        echo "❌ Test backup bazy danych nie powiódł się"
        exit 1
    fi
    
    # Test backup media
    echo "Testing media backup..."
    if ! "$BACKUP_SCRIPTS_DIR/backup_media.sh" --test; then
        echo "❌ Test backup media nie powiódł się"
        exit 1
    fi
    
    echo "✅ Test pełnego backup zakończony pomyślnie"
    exit 0
fi

# Główny proces backup
log "=== ROZPOCZĘCIE PEŁNEGO BACKUP BASEROW ==="
log "Timestamp: $TIMESTAMP"

# Sprawdź stan systemu
log "Sprawdzanie stanu systemu..."
DOCKER_STATUS=$(docker-compose -f "{{ baserow_home }}/docker-compose.yml" ps --services --filter "status=running" | wc -l)
log "Aktywnych kontenerów: $DOCKER_STATUS"

# Sprawdź miejsce na dysku
DISK_USAGE=$(df -h "{{ baserow_home }}/backups" | tail -1 | awk '{print $5}' | sed 's/%//')
if [[ $DISK_USAGE -gt 80 ]]; then
    log "⚠️  Uwaga: Dysk zapełniony w ${DISK_USAGE}%"
fi

# Wykonaj backup bazy danych
log "=== BACKUP BAZY DANYCH ==="
if "$BACKUP_SCRIPTS_DIR/backup_database.sh"; then
    log "✅ Backup bazy danych zakończony pomyślnie"
else
    log "❌ Backup bazy danych nie powiódł się!"
    exit 1
fi

# Wykonaj backup plików media
log "=== BACKUP PLIKÓW MEDIA ==="
if "$BACKUP_SCRIPTS_DIR/backup_media.sh"; then
    log "✅ Backup plików media zakończony pomyślnie"
else
    log "❌ Backup plików media nie powiódł się!"
    exit 1
fi

# Generuj zbiorczy checksum
generate_summary_checksum

# Podsumowanie
log "=== PODSUMOWANIE PEŁNEGO BACKUP ==="
{% if backup_encryption_enabled %}
DB_BACKUPS=$(find "{{ baserow_home }}/backups/database" -name "*.sql.gz.gpg" | wc -l)
MEDIA_BACKUPS=$(find "{{ baserow_home }}/backups/media" -name "*.tar.gz.gpg" | wc -l)
{% else %}
DB_BACKUPS=$(find "{{ baserow_home }}/backups/database" -name "*.sql.gz" | wc -l)
MEDIA_BACKUPS=$(find "{{ baserow_home }}/backups/media" -name "*.tar.gz" | wc -l)
{% endif %}
TOTAL_SIZE=$(du -sh "{{ baserow_home }}/backups" | cut -f1)

log "Backup bazy danych: $DB_BACKUPS plików"
log "Backup media: $MEDIA_BACKUPS plików"
log "Całkowity rozmiar backupów: $TOTAL_SIZE"
log "Użycie dysku: ${DISK_USAGE}%"
{% if backup_encryption_enabled %}
log "Szyfrowanie: Włączone (GPG)"
log "Odbiorca: {{ backup_gpg_recipient }}"
{% else %}
log "Szyfrowanie: Wyłączone"
{% endif %}

# Sprawdź stare backupy
{% if backup_encryption_enabled %}
OLD_DB_BACKUPS=$(find "{{ baserow_home }}/backups/database" -name "*.sql.gz.gpg" -mtime +{{ backup_retention_days }} | wc -l)
OLD_MEDIA_BACKUPS=$(find "{{ baserow_home }}/backups/media" -name "*.tar.gz.gpg" -mtime +{{ backup_retention_days }} | wc -l)
{% else %}
OLD_DB_BACKUPS=$(find "{{ baserow_home }}/backups/database" -name "*.sql.gz" -mtime +{{ backup_retention_days }} | wc -l)
OLD_MEDIA_BACKUPS=$(find "{{ baserow_home }}/backups/media" -name "*.tar.gz" -mtime +{{ backup_retention_days }} | wc -l)
{% endif %}

if [[ $OLD_DB_BACKUPS -gt 0 ]] || [[ $OLD_MEDIA_BACKUPS -gt 0 ]]; then
    log "⚠️  Stare backupy do usunięcia: DB=$OLD_DB_BACKUPS, Media=$OLD_MEDIA_BACKUPS"
fi

log "=== PEŁNY BACKUP ZAKOŃCZONY POMYŚLNIE ==="

# Wyślij notification jeśli skonfigurowany
{% if backup_notification_email %}
if command -v mail >/dev/null 2>&1; then
    echo "Pełny backup Baserow zakończony pomyślnie na $(hostname) o $(date)" | \
        mail -s "Baserow Backup Complete" {{ backup_notification_email }}
fi
{% endif %}