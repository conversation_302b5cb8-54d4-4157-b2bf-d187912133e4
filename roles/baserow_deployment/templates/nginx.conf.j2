# Nginx configuration for Baserow on ARM64
# Optymalizowane dla: {{ ansible_architecture }}, {{ ansible_memtotal_mb }}MB RAM
# Domeny: {{ baserow_domain }}, {{ baserow_alternative_domains | join(', ') }}

user www-data;
worker_processes {{ ansible_processor_vcpus }};  # ARM: {{ ansible_processor_vcpus }} vCPU
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# ARM-specific worker limits  
worker_rlimit_nofile 32768;  # Mniej dla ARM (słabsze CPU)

events {
    worker_connections 512;   # ARM: 512 per worker (2 vCPU * 512 = 1024 total)
    use epoll;
    multi_accept on;
}

http {
    # ==========================================================================
    # BASIC SETTINGS
    # ==========================================================================
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging with performance metrics
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # ARM Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;      # Krótsze dla ARM (oszczędność RAM)
    types_hash_max_size 1024;  # Mniejsze dla ARM
    server_tokens off;
    
    # Client settings (ARM-optimized)
    client_max_body_size {{ nginx_client_max_body_size }};
    client_header_timeout 30s;     # Krótsze dla ARM
    client_body_timeout 30s;       # Krótsze dla ARM
    client_header_buffer_size 2k;  # Mniejsze dla ARM 
    large_client_header_buffers 4 4k;  # ARM-friendly size
    
    # ==========================================================================
    # GZIP COMPRESSION (ARM-optimized levels)
    # ==========================================================================
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 4;  # Niższe dla ARM (CPU-intensive)
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # ==========================================================================
    # SSL/TLS SETTINGS
    # ==========================================================================
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # ==========================================================================
    # ENHANCED SECURITY HEADERS
    # ==========================================================================
    
    # Basic security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Enhanced HSTS with preload
    {% if nginx_hsts_enabled | default(true) %}
    add_header Strict-Transport-Security "max-age={{ nginx_hsts_max_age | default('63072000') }}{% if nginx_hsts_include_subdomains | default(true) %}; includeSubDomains{% endif %}{% if nginx_hsts_preload | default(true) %}; preload{% endif %}" always;
    {% endif %}
    
    # Content Security Policy
    {% if nginx_csp_enabled | default(true) %}
    add_header Content-Security-Policy "{{ nginx_csp_policy | default('default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\'; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data: https:; font-src \'self\' data:; connect-src \'self\' ws: wss:; media-src \'self\'; object-src \'none\'; frame-src \'none\'; base-uri \'self\'; form-action \'self\'') }}" always;
    {% endif %}
    
    # Permissions Policy (Feature Policy replacement)
    {% if nginx_permissions_policy_enabled | default(true) %}
    add_header Permissions-Policy "{{ nginx_permissions_policy | default('camera=(), microphone=(), geolocation=(), gyroscope=(), magnetometer=(), payment=(), usb=(), fullscreen=(self), display-capture=()') }}" always;
    {% endif %}
    
    # Cross-Origin headers for Spectre protection
    {% if nginx_cross_origin_policy_enabled | default(true) %}
    add_header Cross-Origin-Embedder-Policy "{{ nginx_cross_origin_embedder_policy | default('require-corp') }}" always;
    add_header Cross-Origin-Opener-Policy "{{ nginx_cross_origin_opener_policy | default('same-origin') }}" always;
    add_header Cross-Origin-Resource-Policy "{{ nginx_cross_origin_resource_policy | default('same-origin') }}" always;
    {% endif %}
    
    # Additional security headers
    {% if nginx_security_headers_enabled | default(true) %}
    add_header X-Permitted-Cross-Domain-Policies "{{ nginx_x_permitted_cross_domain_policies | default('none') }}" always;
    {% endif %}
    
    # ==========================================================================
    # UPSTREAM SERVERS
    # ==========================================================================
    upstream baserow_backend {
        server 127.0.0.1:8000 max_fails=2 fail_timeout=20s;  # ARM: szybsze fail detection
        keepalive 16;  # Mniej keepalive dla ARM
    }
    
    upstream baserow_frontend {
        server 127.0.0.1:3000 max_fails=2 fail_timeout=20s;
        keepalive 16;  # Mniej keepalive dla ARM
    }
    
    # ==========================================================================
    # RATE LIMITING (ARM-conservative)
    # ==========================================================================
    limit_req_zone $binary_remote_addr zone=api:5m rate=8r/s;      # ARM: mniej agresywne
    limit_req_zone $binary_remote_addr zone=login:5m rate=1r/s;    # Bez zmian
    limit_req_zone $binary_remote_addr zone=general:5m rate=50r/s; # ARM: mniej agresywne
    
    # ==========================================================================
    # HTTP -> HTTPS REDIRECT
    # ==========================================================================
    server {
        listen 80;
        server_name {{ baserow_domain }}{% for domain in baserow_alternative_domains %} {{ domain }}{% endfor %};
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Let's Encrypt challenge
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
            try_files $uri =404;
        }
        
        # Redirect all HTTP to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }
    
    # ==========================================================================
    # HTTPS SERVER - Primary Domain
    # ==========================================================================
    server {
        listen 443 ssl http2;
        server_name {{ baserow_domain }};
        
        # SSL certificates (Let's Encrypt)
        ssl_certificate /etc/nginx/ssl/{{ baserow_domain }}.crt;
        ssl_certificate_key /etc/nginx/ssl/{{ baserow_domain }}.key;
        
        # Logging
        access_log /var/log/nginx/{{ baserow_domain }}.access.log main;
        error_log /var/log/nginx/{{ baserow_domain }}.error.log;
        
        # =======================================================================
        # API ENDPOINTS - Backend
        # =======================================================================
        location /api/ {
            limit_req zone=api burst=15 nodelay;  # ARM: mniejszy burst
            
            # API-specific security headers
            {% if nginx_csp_enabled | default(true) %}
            add_header Content-Security-Policy "{{ nginx_csp_api_policy | default('default-src \'none\'; connect-src \'self\'') }}" always;
            {% endif %}
            
            # Disable caching for API responses
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Pragma "no-cache" always;
            add_header Expires "0" always;
            
            # API-specific cross-origin headers
            {% if nginx_cross_origin_policy_enabled | default(true) %}
            add_header Cross-Origin-Resource-Policy "same-origin" always;
            {% endif %}
            
            proxy_pass http://baserow_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            
            # ARM-optimized timeouts
            proxy_connect_timeout {{ nginx_proxy_connect_timeout }};
            proxy_send_timeout {{ nginx_proxy_read_timeout }};
            proxy_read_timeout {{ nginx_proxy_read_timeout }};
            
            # ARM-optimized buffering
            proxy_buffering on;
            proxy_buffer_size 2k;    # Mniejsze dla ARM
            proxy_buffers 4 4k;      # ARM-friendly
            proxy_busy_buffers_size 8k;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # =======================================================================
        # MEDIA FILES - Backend
        # =======================================================================
        location /media/ {
            # No rate limiting for media files by default, adjust if needed
            
            # Disable caching for media files to ensure freshness
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Pragma "no-cache" always;
            add_header Expires "0" always;

            proxy_pass http://baserow_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            
            # ARM-optimized timeouts
            proxy_connect_timeout {{ nginx_proxy_connect_timeout }};
            proxy_send_timeout {{ nginx_proxy_read_timeout }};
            proxy_read_timeout {{ nginx_proxy_read_timeout }};
            
            # ARM-optimized buffering
            proxy_buffering on;
            proxy_buffer_size 2k;
            proxy_buffers 4 4k;
            proxy_busy_buffers_size 8k;
        }
        
        # =======================================================================
        # ADMIN/AUTH ENDPOINTS - Rate limited
        # =======================================================================
        location ~ ^/(admin|auth)/ {
            limit_req zone=login burst=3 nodelay;  # ARM: mniejszy burst
            
            # Enhanced security headers for admin/auth endpoints
            {% if nginx_csp_enabled | default(true) %}
            add_header Content-Security-Policy "{{ nginx_csp_policy | default('default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\'; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data: https:; font-src \'self\' data:; connect-src \'self\' ws: wss:; media-src \'self\'; object-src \'none\'; frame-src \'none\'; base-uri \'self\'; form-action \'self\'') }}" always;
            {% endif %}
            
            # Strict caching policy for sensitive endpoints
            add_header Cache-Control "no-cache, no-store, must-revalidate, private" always;
            add_header Pragma "no-cache" always;
            add_header Expires "0" always;
            
            # Enhanced frame protection for admin
            add_header X-Frame-Options "DENY" always;
            
            # Clear site data on logout (conditional)
            {% if nginx_clear_site_data_enabled | default(false) %}
            add_header Clear-Site-Data "{{ nginx_clear_site_data_policy | default('cache, cookies, storage, executionContexts') }}" always;
            {% endif %}
            
            proxy_pass http://baserow_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            
            proxy_connect_timeout {{ nginx_proxy_connect_timeout }};
            proxy_send_timeout {{ nginx_proxy_read_timeout }};
            proxy_read_timeout {{ nginx_proxy_read_timeout }};
        }
        
        # =======================================================================
        # STATIC FILES - Frontend
        # =======================================================================
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            limit_req zone=general burst=30 nodelay;  # ARM: mniejszy burst
            
            # Enhanced security headers for static files
            {% if nginx_static_files_security_enabled | default(true) %}
            add_header Referrer-Policy "{{ nginx_static_files_referrer_policy | default('strict-origin-when-cross-origin') }}" always;
            add_header X-Content-Type-Options "nosniff" always;
            {% endif %}
            
            # Cross-Origin headers for static resources
            {% if nginx_cross_origin_policy_enabled | default(true) %}
            add_header Cross-Origin-Resource-Policy "cross-origin" always;
            {% endif %}
            
            proxy_pass http://baserow_frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Enhanced caching for static files
            expires 1M;
            {% if nginx_static_files_security_enabled | default(true) %}
            add_header Cache-Control "{{ nginx_static_files_cache_control | default('public, max-age=31536000, immutable') }}" always;
            {% else %}
            add_header Cache-Control "public, immutable";
            {% endif %}
            
            # Gzip for static files
            gzip_static on;
        }
        
        # =======================================================================
        # FRONTEND - Main application
        # =======================================================================
        location / {
            limit_req zone=general burst=30 nodelay;  # ARM: mniejszy burst
            
            proxy_pass http://baserow_frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            
            proxy_connect_timeout {{ nginx_proxy_connect_timeout }};
            proxy_send_timeout {{ nginx_proxy_read_timeout }};
            proxy_read_timeout {{ nginx_proxy_read_timeout }};
            
            # WebSocket support for real-time updates
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        
        # =======================================================================
        # SECURITY & MONITORING
        # =======================================================================
        
        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Nginx status for monitoring (ARM-friendly)
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 172.20.0.0/24;  # Docker network
            deny all;
        }
    }
    
    # ==========================================================================
    # HTTPS SERVER - Alternative Domains (www.baserow.simetria.pl)
    # ==========================================================================
    server {
        listen 443 ssl http2;
        server_name{% for domain in baserow_alternative_domains %} {{ domain }}{% endfor %};
        
        # SSL certificates (same as primary)
        ssl_certificate /etc/nginx/ssl/{{ baserow_domain }}.crt;
        ssl_certificate_key /etc/nginx/ssl/{{ baserow_domain }}.key;
        
        # Redirect to primary domain
        return 301 https://{{ baserow_domain }}$request_uri;
    }
}