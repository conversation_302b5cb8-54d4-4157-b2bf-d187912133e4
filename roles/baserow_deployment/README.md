# Rola Ansible: baserow_deployment

## Opis

Rola Ansible do automatycznego deployment aplikacji Baserow z wykorzystaniem Docker Compose. Rola zapewnia kompletną instalację wraz z konfiguracją bezpieczeństwa, backup, monitoring, szyfrowaniem GPG, automatyczną weryfikacją backupów, wzmocnionymi nagłówkami SSL oraz centralnym systemem logowania.

## Wymagania

- **System operacyjny**: Ubuntu 20.04+ / Debian 11+ (obsługa ARM64 i x86_64)
- **Pamięć RAM**: Minimum 2GB, zalecane 4GB
- **Dysk**: Minimum 10GB wolnego miejsca
- **Uprawnienia**: sudo/root access
- **Sieć**: Otwarte porty 80, 443, 22
- **GPG**: Zainstalowany GPG dla szyfrowania backupów

## Zmienne

### Wymagane zmienne

```yaml
baserow_domain: "baserow.example.com"    # Domena dla Baserow
```

### Opcjonalne zmienne (z wartościami domyślnymi)

```yaml
# Podstawowa konfiguracja
baserow_user: "baserow"                   # Użytkownik systemowy
baserow_home: "/opt/baserow"              # Katalog instalacji
baserow_port: 3000                        # Port aplikacji

# Baza danych
baserow_db_name: "baserow"                # Nazwa bazy danych
baserow_db_user: "baserow"                # Użytkownik bazy danych
baserow_db_password: "{{ vault_baserow_db_password }}"  # Hasło bazy (z vault)

# Konfiguracja Redis
redis_maxmemory: "128MB"                  # Maksymalna pamięć Redis
redis_maxmemory_policy: "allkeys-lru"    # Polityka zarządzania pamięcią

# Konfiguracja zasobów (ARM-optimized)
baserow_backend_memory: "1g"              # Pamięć dla backend
baserow_database_memory: "512m"           # Pamięć dla PostgreSQL
baserow_redis_memory: "256m"              # Pamięć dla Redis

# Backup
backup_cron_enabled: true                 # Czy włączyć cron backup
backup_cron_hour: "2"                     # Godzina backup (02:00)
backup_cron_minute: "0"                   # Minuta backup
backup_retention_days: 7                  # Ile dni trzymać backupy

# Szyfrowanie GPG
gpg_encryption_enabled: true              # Czy włączyć szyfrowanie GPG
gpg_recipient: "<EMAIL>"      # Odbiorca GPG
gpg_public_key: "-----BEGIN PGP PUBLIC KEY BLOCK-----\n..."  # Klucz publiczny

# Weryfikacja backupów
backup_verification_enabled: true       # Czy włączyć weryfikację backupów
backup_verification_cron_hour: "3"     # Godzina weryfikacji (03:00)
backup_verification_cron_minute: "0"    # Minuta weryfikacji

# Bezpieczeństwo
fail2ban_enabled: true                    # Czy skonfigurować Fail2ban
fail2ban_maxretry: 5                      # Maksymalna liczba prób
fail2ban_bantime: "1h"                    # Czas bana
fail2ban_findtime: "10m"                  # Okno czasowe

# Wzmocnione nagłówki SSL
ssl_hardening_enabled: true              # Czy włączyć wzmocnione nagłówki SSL
ssl_hsts_max_age: 31536000              # Maksymalny wiek HSTS (1 rok)
ssl_ssl_session_timeout: "1d"          # Czas życia sesji SSL
ssl_ssl_session_cache: "shared:SSL:10m" # Cache sesji SSL

# Firewall
firewall_allowed_ports:                   # Dozwolone porty
  - "22"
  - "80"
  - "443"

# Monitoring
baserow_enable_metrics: true              # Czy włączyć metryki
baserow_metrics_port: 9090                # Port dla metryk

# Centralne logowanie
central_logging_enabled: true           # Czy włączyć centralne logowanie
loki_server: "loki.example.com"         # Adres serwera Loki
loki_port: 3100                         # Port Loki
promtail_enabled: true                # Czy włączyć Promtail

# Notyfikacje
backup_notification_email: ""             # Email dla powiadomień backup
```

## Przykład użycia

### Playbook

```yaml
---
- name: Deploy Baserow
  hosts: baserow_servers
  become: true
  vars:
    baserow_domain: "baserow.simetria.pl"
    baserow_db_password: "{{ vault_baserow_db_password }}"
    baserow_secret_key: "{{ vault_baserow_secret_key }}"
    backup_notification_email: "<EMAIL>"
    gpg_encryption_enabled: true
    gpg_recipient: "<EMAIL>"
    gpg_public_key: "{{ vault_gpg_public_key }}"
    central_logging_enabled: true
    loki_server: "logs.simetria.pl"
  
  roles:
    - baserow_deployment
```

### Inventory

```ini
[baserow_servers]
simetria-hetzner001 ansible_host=************

[baserow_servers:vars]
ansible_user=root
ansible_ssh_private_key_file=~/.ssh/id_rsa
```

### Vault (ansible-vault)

```bash
ansible-vault create group_vars/all/vault.yml
```

```yaml
# group_vars/all/vault.yml
vault_baserow_db_password: "super_secure_password_123"
vault_baserow_secret_key: "super_secure_secret_key_456"
vault_gpg_public_key: "-----BEGIN PGP PUBLIC KEY BLOCK-----\n..."
```

## Struktura roli

```
roles/baserow_deployment/
├── tasks/
│   ├── main.yml              # Główny plik zadań
│   ├── prepare_system.yml    # Przygotowanie systemu
│   ├── install_docker.yml    # Instalacja Docker
│   ├── setup_user.yml        # Konfiguracja użytkownika
│   ├── deploy_baserow.yml    # Deployment Baserow
│   ├── setup_nginx.yml       # Konfiguracja Nginx/SSL
│   ├── setup_firewall.yml    # Konfiguracja firewall
│   ├── setup_backup.yml      # Konfiguracja backup
│   ├── setup_gpg.yml         # Konfiguracja GPG
│   ├── setup_ssl_hardening.yml # Wzmocnione SSL
│   ├── setup_logging.yml     # Centralne logowanie
│   └── verify_installation.yml # Weryfikacja instalacji
├── templates/
│   ├── docker-compose.yml.j2 # Template Docker Compose
│   ├── nginx.conf.j2         # Template Nginx
│   ├── backup_database.sh.j2 # Skrypt backup bazy
│   ├── backup_media.sh.j2    # Skrypt backup media
│   ├── full_backup.sh.j2     # Skrypt pełnego backup
│   ├── restore_backup.sh.j2  # Skrypt restore
│   ├── verify_backup.sh.j2   # Skrypt weryfikacji backupów
│   └── gpg_keys.sh.j2        # Skrypt zarządzania kluczami GPG
├── handlers/
│   └── main.yml              # Handlery (restart usług)
├── defaults/
│   └── main.yml              # Domyślne zmienne
└── README.md                 # Ta dokumentacja
```

## Proces instalacji

1. **Przygotowanie systemu** - aktualizacja pakietów, instalacja zależności
2. **Instalacja Docker** - Docker Engine + Docker Compose
3. **Konfiguracja użytkownika** - tworzenie użytkownika systemowego
4. **Deployment Baserow** - konfiguracja i uruchomienie kontenerów
5. **Nginx/SSL** - reverse proxy i konfiguracja SSL
6. **Firewall** - konfiguracja UFW i Fail2ban
7. **Backup** - skrypty backup i cron jobs
8. **Szyfrowanie GPG** - konfiguracja szyfrowania backupów
9. **Weryfikacja backupów** - automatyczna weryfikacja integralności
10. **Wzmocnione SSL** - konfiguracja wzmocnionych nagłówków bezpieczeństwa
11. **Centralne logowanie** - integracja z systemem centralnego logowania
12. **Weryfikacja** - sprawdzenie poprawności instalacji

## Zarządzanie po instalacji

### Podstawowe komendy

```bash
# Start/Stop/Restart
cd /opt/baserow
docker-compose up -d
docker-compose down
docker-compose restart

# Logi
docker-compose logs -f
docker-compose logs backend
docker-compose logs database

# Status
docker-compose ps
```

### Backup i Restore

```bash
# Pełny backup
/opt/baserow/backups/scripts/full_backup.sh

# Backup tylko bazy danych
/opt/baserow/backups/scripts/backup_database.sh

# Backup tylko plików media
/opt/baserow/backups/scripts/backup_media.sh

# Weryfikacja backupów
/opt/baserow/backups/scripts/verify_backup.sh

# Lista dostępnych backupów
/opt/baserow/backups/scripts/restore_backup.sh --list

# Restore kompletny
/opt/baserow/backups/scripts/restore_backup.sh --full

# Restore tylko bazy
/opt/baserow/backups/scripts/restore_backup.sh --database backup_file.sql.gz
```

### Szyfrowanie GPG

```bash
# Import klucza publicznego
/opt/baserow/scripts/gpg_keys.sh --import-public-key

# Lista kluczy
/opt/baserow/scripts/gpg_keys.sh --list-keys

# Test szyfrowania
echo "test" | gpg --encrypt --recipient <EMAIL> --armor
```

### Weryfikacja backupów

```bash
# Manualna weryfikacja
/opt/baserow/backups/scripts/verify_backup.sh --manual

# Sprawdzenie logów weryfikacji
tail -f /opt/baserow/logs/backup_verification.log

# Test restore (dry-run)
/opt/baserow/backups/scripts/verify_backup.sh --test-restore
```

### Monitoring

```bash
# Sprawdzenie statusu
docker-compose ps

# Użycie zasobów
docker stats

# Logi systemu
journalctl -u docker
tail -f /opt/baserow/logs/nginx/error.log

# Metryki Prometheus
curl http://localhost:9090/metrics
```

### Centralne logowanie

```bash
# Status Promtail
systemctl status promtail

# Logi Promtail
journalctl -u promtail -f

# Test wysyłania logów
/opt/baserow/scripts/test_logging.sh

# Dashboard Grafana
# URL: https://logs.simetria.pl
```

## Bezpieczeństwo

### Firewall (UFW)

```bash
# Sprawdzenie statusu
sudo ufw status

# Włączenie (po instalacji)
sudo ufw enable
```

### Fail2ban

```bash
# Status
sudo fail2ban-client status

# Status konkretnego jail
sudo fail2ban-client status nginx-http-auth
```

### SSL/TLS

```bash
# Automatyczna konfiguracja SSL z Let's Encrypt
sudo certbot --nginx -d baserow.simetria.pl

# Odnowienie certyfikatu
sudo certbot renew

# Test wzmocnionych nagłówków
curl -I https://baserow.simetria.pl
```

### GPG Encryption

```bash
# Lista kluczy
gpg --list-keys

# Szyfrowanie pliku
gpg --encrypt --recipient <EMAIL> backup.tar.gz

# Odszyfrowanie pliku
gpg --decrypt backup.tar.gz.gpg
```

## Troubleshooting

### Problemy z kontenerami

```bash
# Sprawdzenie logów
docker-compose logs --tail=50 backend
docker-compose logs --tail=50 database

# Restart usług
docker-compose restart backend
docker-compose restart database
```

### Problemy z bazą danych

```bash
# Sprawdzenie połączenia
docker-compose exec postgres psql -U baserow -d baserow -c "SELECT 1;"

# Backup manualny
docker-compose exec -T postgres pg_dump -U baserow -d baserow > manual_backup.sql
```

### Problemy z Nginx

```bash
# Test konfiguracji
nginx -t

# Restart Nginx
systemctl restart nginx

# Sprawdzenie logów
tail -f /opt/baserow/logs/nginx/error.log
```

### Problemy z GPG

```bash
# Sprawdzenie kluczy
gpg --list-keys

# Import klucza
gpg --import public.key

# Test szyfrowania
echo "test" | gpg --encrypt --recipient <EMAIL>
```

### Problemy z weryfikacją backupów

```bash
# Sprawdzenie logów
tail -f /opt/baserow/logs/backup_verification.log

# Manualna weryfikacja
/opt/baserow/backups/scripts/verify_backup.sh --manual

# Sprawdzenie integralności
sha256sum backup.tar.gz.gpg
```

## Wspierane architektury

- **x86_64** (Intel/AMD 64-bit)
- **ARM64** (ARM 64-bit) - zoptymalizowane dla serwerów ARM

## Changelog

### v2.0.0
- Szyfrowanie GPG backupów
- Automatyczna weryfikacja backupów
- Wzmocnione nagłówki bezpieczeństwa SSL
- Centralny system logowania z Loki
- Monitoring z Prometheus i Grafana
- Alertmanager dla powiadomień
- Optymalizacja dla ARM64
- Ulepszone skrypty backup/restore

### v1.0.0
- Pierwsza wersja roli
- Obsługa Docker Compose
- Automatyczny backup
- Konfiguracja bezpieczeństwa
- Obsługa ARM64

## Autorzy

- **Zespół Simetria** - *Implementacja początkowa*

## Licencja

Projekt jest licencjonowany na licencji MIT.