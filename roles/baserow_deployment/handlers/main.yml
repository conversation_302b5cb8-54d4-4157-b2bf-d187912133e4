---
# Handlers dla roli baserow_deployment

- name: restart docker
  service:
    name: docker
    state: restarted
  become: true

- name: restart cron
  service:
    name: cron
    state: restarted
  become: true

- name: restart nginx
  service:
    name: nginx
    state: restarted
  become: true

- name: reload nginx
  service:
    name: nginx
    state: reloaded
  become: true

- name: restart baserow
  shell: |
    cd {{ baserow_home }}
    docker-compose down
    sleep 5
    docker-compose up -d
  become_user: "{{ baserow_user }}"
  become: true

- name: restart baserow backend
  shell: |
    cd {{ baserow_home }}
    docker-compose restart backend
  become_user: "{{ baserow_user }}"
  become: true

- name: restart baserow frontend
  shell: |
    cd {{ baserow_home }}
    docker-compose restart frontend
  become_user: "{{ baserow_user }}"
  become: true

- name: restart postgres
  shell: |
    cd {{ baserow_home }}
    docker-compose restart postgres
  become_user: "{{ baserow_user }}"
  become: true

- name: restart redis
  shell: |
    cd {{ baserow_home }}
    docker-compose restart redis
  become_user: "{{ baserow_user }}"
  become: true

- name: reload fail2ban
  service:
    name: fail2ban
    state: reloaded
  become: true

- name: reload ufw
  shell: ufw --force reload
  become: true
