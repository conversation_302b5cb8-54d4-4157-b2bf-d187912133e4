---
# Konfiguracja firewall UFW dla Baserow

- name: "Sprawdź czy UFW jest zainstalowany"
  command: which ufw
  register: ufw_check
  changed_when: false
  ignore_errors: true

- name: "Wyświetl status UFW"
  debug:
    msg: "UFW: {{ 'Zainstalowany' if ufw_check.rc == 0 else 'Niezainstalowany' }}"

- name: "Ustaw domyślną politykę UFW - DENY incoming"
  ufw:
    direction: incoming
    policy: deny
  become: true

- name: "Ustaw domyślną politykę UFW - ALLOW outgoing"
  ufw:
    direction: outgoing
    policy: allow
  become: true

- name: "Otwórz porty dla Baserow"
  ufw:
    rule: allow
    port: "{{ item }}"
  become: true
  loop: "{{ firewall_allowed_ports }}"

- name: "Sprawdź obecne reguły UFW"
  shell: ufw --force status numbered
  become: true
  register: ufw_status
  changed_when: false

- name: "Wyświetl reguły UFW"
  debug:
    msg:
      - "=== REGUŁY UFW ==="
      - "{{ ufw_status.stdout_lines }}"

# =============================================================================
# KONFIGURACJA FAIL2BAN DLA NGINX
# =============================================================================

- name: "Sprawdź czy Fail2ban jest zainstalowany"
  stat:
    path: /etc/fail2ban/fail2ban.conf
  register: fail2ban_check

- name: "Stwórz jail dla Nginx (jeśli Fail2ban istnieje)"
  copy:
    content: |
      # Fail2ban jail dla Nginx Baserow
      # ARM-optimized settings (mniej agresywne)
      
      [nginx-http-auth]
      enabled = true
      port = http,https
      filter = nginx-http-auth
      logpath = {{ baserow_home }}/logs/nginx/*error.log
      maxretry = {{ fail2ban_maxretry }}
      bantime = {{ fail2ban_bantime }}
      findtime = {{ fail2ban_findtime }}
      
      [nginx-limit-req]
      enabled = true
      port = http,https
      filter = nginx-limit-req
      logpath = {{ baserow_home }}/logs/nginx/*error.log
      maxretry = {{ fail2ban_maxretry }}
      bantime = {{ fail2ban_bantime }}
      findtime = {{ fail2ban_findtime }}
      
      [nginx-badbots]
      enabled = true
      port = http,https
      filter = nginx-badbots
      logpath = {{ baserow_home }}/logs/nginx/*access.log
      maxretry = 3
      bantime = 1h
      findtime = 10m
    dest: /etc/fail2ban/jail.d/baserow-nginx.conf
    mode: '0644'
  become: true
  when: fail2ban_check.stat.exists and fail2ban_enabled
  notify: reload fail2ban

- name: "Stwórz filter dla rate limiting Nginx"
  copy:
    content: |
      # Fail2ban filter for nginx rate limiting
      [Definition]
      failregex = limiting requests, excess: .* by zone .*, client: <HOST>
      ignoreregex =
    dest: /etc/fail2ban/filter.d/nginx-limit-req.conf
    mode: '0644'
  become: true
  when: fail2ban_check.stat.exists and fail2ban_enabled

- name: "Stwórz filter dla bad bots"
  copy:
    content: |
      # Fail2ban filter for nginx bad bots
      [Definition]
      failregex = ^<HOST> -.*"(GET|POST|HEAD).*HTTP.*" (403|404|444) .*$
      ignoreregex = .*robots\.txt.*
                    .*favicon\.ico.*
                    .*\.(css|js|png|jpg|jpeg|gif|ico|svg).*
    dest: /etc/fail2ban/filter.d/nginx-badbots.conf
    mode: '0644'
  become: true
  when: fail2ban_check.stat.exists and fail2ban_enabled

# =============================================================================
# BEZPIECZEŃSTWO DOCKER
# =============================================================================

- name: "Sprawdź bezpieczeństwo Docker"
  shell: |
    echo "=== DOCKER SECURITY AUDIT ==="
    echo "Docker daemon user:"
    ps aux | grep dockerd | head -1
    echo ""
    echo "Docker socket permissions:"
    ls -la /var/run/docker.sock
    echo ""
    echo "Docker grupa members:"
    getent group docker
  register: docker_security_audit
  changed_when: false

- name: "Wyświetl audit bezpieczeństwa Docker"
  debug:
    msg:
      - "=== AUDIT BEZPIECZEŃSTWA DOCKER ==="
      - "{{ docker_security_audit.stdout_lines }}"

- name: "Ustaw bezpieczne uprawnienia dla Docker socket"
  file:
    path: /var/run/docker.sock
    owner: root
    group: docker
    mode: '0660'
  become: true

# =============================================================================
# MONITORING PORTÓW I USŁUG
# =============================================================================

- name: "Sprawdź otwarte porty na serwerze"
  shell: |
    echo "=== OTWARTE PORTY ==="
    ss -tulpn | grep LISTEN | sort
    echo ""
    echo "=== AKTYWNE KONTENERY ==="
    docker ps --format '{% raw %}"table {{.Names}}\t{{.Status}}\t{{.Ports}}"{% endraw %}' 2>/dev/null || echo "Docker nie działa"
  register: port_audit
  changed_when: false

- name: "Wyświetl audit portów"
  debug:
    msg:
      - "=== AUDIT PORTÓW I USŁUG ==="
      - "{{ port_audit.stdout_lines }}"

# =============================================================================
# PODSUMOWANIE BEZPIECZEŃSTWA
# =============================================================================

- name: "Podsumowanie konfiguracji firewall"
  debug:
    msg:
      - "=== KONFIGURACJA FIREWALL UKOŃCZONA ==="
      - "UFW status: {{ 'Aktywny' if ufw_status.stdout.find('Status: active') != -1 else 'Nieaktywny' }}"
      - "Otwarte porty: {{ firewall_allowed_ports | join(', ') }}"
      - "Fail2ban: {{ 'Aktywny' if fail2ban_check.stat.exists and fail2ban_enabled else 'Wyłączony' }}"
      - "Jails Nginx: {{ 'Skonfigurowane' if fail2ban_check.stat.exists and fail2ban_enabled else 'Brak' }}"
      - "Docker socket: Zabezpieczony"
      - ""
      - "UWAGA: UFW musi być ręcznie włączony komendą 'ufw enable'"
      - "Sprawdź logi: tail -f {{ baserow_home }}/logs/nginx/*error.log"

- name: "Ostrzeżenie o UFW"
  pause:
    prompt: |
      
      WAŻNE: UFW nie został automatycznie włączony aby nie zablokować dostępu SSH.
      
      Po zakończeniu instalacji, włącz UFW komendą:
      sudo ufw enable
      
      Naciśnij Enter aby kontynuować...
    seconds: 1