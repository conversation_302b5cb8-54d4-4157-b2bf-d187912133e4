---
# Konfigu<PERSON><PERSON> użytkownika baserow i struktura katalogów

- name: "Stwórz grupę baserow"
  group:
    name: "{{ baserow_group }}"
    gid: 1500
    state: present
  become: true

- name: "St<PERSON><PERSON>rz użytkownika baserow"
  user:
    name: "{{ baserow_user }}"
    uid: 1500
    group: "{{ baserow_group }}"
    groups: docker
    home: "{{ baserow_home }}"
    shell: /bin/bash
    create_home: true
    system: false
    state: present
  become: true

- name: "Stwórz główny katalog baserow"
  file:
    path: "{{ baserow_home }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true

- name: "St<PERSON><PERSON>rz strukturę katalogów dla Baserow"
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  loop:
    - "{{ baserow_home }}/config"
    - "{{ baserow_home }}/data"
    - "{{ baserow_home }}/logs"
    - "{{ baserow_home }}/nginx"
    - "{{ baserow_home }}/nginx/conf.d"
    - "{{ baserow_home }}/nginx/ssl"
    - "{{ baserow_home }}/postgres"
    - "{{ baserow_home }}/redis"
    - "{{ backup_dir }}"
    - "{{ backup_dir }}/daily"
    - "{{ backup_dir }}/weekly"
    - "{{ backup_dir }}/monthly"

- name: "Ustaw specjalne uprawnienia dla katalogów wrażliwych"
  file:
    path: "{{ item.path }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: "{{ item.mode }}"
  become: true
  loop:
    - { path: "{{ baserow_home }}/nginx/ssl", mode: '0700' }
    - { path: "{{ backup_dir }}", mode: '0750' }
    - { path: "{{ baserow_home }}/postgres", mode: '0750' }

- name: "Stwórz katalog dla Docker volumes"
  file:
    path: "{{ baserow_home }}/volumes"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true

- name: "Stwórz podkatalogi dla volumes"
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  loop:
    - "{{ baserow_home }}/volumes/postgres_data"
    - "{{ baserow_home }}/volumes/baserow_data"
    - "{{ baserow_home }}/volumes/redis_data"
    - "{{ baserow_home }}/volumes/nginx_cache"

- name: "Stwórz katalog dla skryptów"
  file:
    path: "{{ baserow_home }}/scripts"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true

- name: "Stwórz katalog dla certyfikatów SSL"
  file:
    path: "{{ baserow_home }}/ssl"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0700'
  become: true

- name: "Ustaw odpowiednie uprawnienia dla użytkownika baserow"
  user:
    name: "{{ baserow_user }}"
    groups: "{{ baserow_group }},docker"
    append: false
  become: true

- name: "Stwórz plik .bashrc dla użytkownika baserow"
  copy:
    content: |
      # .bashrc dla użytkownika baserow
      
      # Jeśli nie działa interaktywnie, nic nie rób
      [[ $- != *i* ]] && return
      
      # Historia
      HISTCONTROL=ignoredups:ignorespace
      HISTSIZE=1000
      HISTFILESIZE=2000
      shopt -s histappend
      
      # Aliasy
      alias ll='ls -alF'
      alias la='ls -A'
      alias l='ls -CF'
      alias grep='grep --color=auto'
      
      # Docker aliasy
      alias dps='docker ps'
      alias dpsa='docker ps -a'
      alias dlogs='docker logs'
      alias dexec='docker exec -it'
      alias dcompose='docker compose'
      alias dcup='docker compose up -d'
      alias dcdown='docker compose down'
      alias dcrestart='docker compose restart'
      alias dclogs='docker compose logs -f'
      
      # Baserow aliasy
      alias baserow-logs='docker compose -f {{ baserow_home }}/docker-compose.yml logs -f'
      alias baserow-status='docker compose -f {{ baserow_home }}/docker-compose.yml ps'
      alias baserow-restart='docker compose -f {{ baserow_home }}/docker-compose.yml restart'
      alias baserow-backup='{{ baserow_home }}/scripts/backup.sh'
      
      # Prompt
      PS1='\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '
      
      # Zmienne środowiskowe
      export EDITOR=nano
      export COMPOSE_FILE="{{ baserow_home }}/docker-compose.yml"
      
      # Funkcje pomocnicze
      baserow_health() {
          echo "=== Baserow Health Check ==="
          docker compose -f {{ baserow_home }}/docker-compose.yml ps
          echo ""
          echo "=== Disk Usage ==="
          du -sh {{ baserow_home }}/*
          echo ""
          echo "=== Memory Usage ==="
          free -h
      }
      
      baserow_backup_list() {
          echo "=== Dostępne backupy ==="
          ls -la {{ backup_dir }}/daily/
      }
    dest: "{{ baserow_home }}/.bashrc"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0644'
  become: true

- name: "Wyświetl strukturę katalogów"
  command: tree "{{ baserow_home }}" -L 3
  register: directory_tree
  changed_when: false
  ignore_errors: true

- name: "Wyświetl utworzoną strukturę katalogów"
  debug:
    msg:
      - "=== STRUKTURA KATALOGÓW BASEROW ==="
      - "{{ directory_tree.stdout_lines if directory_tree.rc == 0 else 'Tree nie jest dostępne - katalogi utworzone' }}"

- name: "Sprawdź uprawnienia użytkownika baserow"
  command: "id {{ baserow_user }}"
  register: user_info
  changed_when: false

- name: "Wyświetl informacje o użytkowniku baserow"
  debug:
    msg:
      - "=== UŻYTKOWNIK BASEROW ==="
      - "{{ user_info.stdout }}"
      - "Katalog domowy: {{ baserow_home }}"
      - "Grupy: docker, {{ baserow_group }}"
