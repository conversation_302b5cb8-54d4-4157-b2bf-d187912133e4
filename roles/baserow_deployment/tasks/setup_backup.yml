---
# Skrypty backup dla Baserow

- name: "Stwórz katalog dla backupów"
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  loop:
    - "{{ baserow_home }}/backups"
    - "{{ baserow_home }}/backups/database"
    - "{{ baserow_home }}/backups/media"
    - "{{ baserow_home }}/backups/scripts"
  become: true

- name: "Zainstaluj GPG dla szyfrowania backupów"
  package:
    name: gnupg
    state: present
  become: true
  when: backup_encryption_enabled

- name: "Stwórz katalog GPG dla użytkownika baserow"
  file:
    path: "{{ backup_gpg_keyring_path }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0700'
  become: true
  when: backup_encryption_enabled

- name: "Sprawdź czy klucz GPG już istnieje"
  shell: |
    gpg --homedir "{{ backup_gpg_keyring_path }}" --list-secret-keys "{{ backup_gpg_recipient }}" >/dev/null 2>&1
  become: true
  become_user: "{{ baserow_user }}"
  register: gpg_key_exists
  failed_when: false
  changed_when: false
  when: backup_encryption_enabled

- name: "Generuj klucz GPG dla backupów"
  shell: |
    gpg --homedir "{{ backup_gpg_keyring_path }}" --batch --generate-key << EOF
    Key-Type: {{ backup_gpg_key_type }}
    Key-Length: {{ backup_gpg_key_length }}
    Subkey-Type: {{ backup_gpg_key_type }}
    Subkey-Length: {{ backup_gpg_key_length }}
    Name-Real: Baserow Backup
    Name-Email: {{ backup_gpg_recipient }}
    Expire-Date: {{ backup_gpg_key_expire }}
    %no-protection
    %commit
    EOF
  become: true
  become_user: "{{ baserow_user }}"
  when:
    - backup_encryption_enabled
    - gpg_key_exists.rc != 0
  register: gpg_key_generation

- name: "Wyświetl informacje o kluczu GPG"
  shell: |
    gpg --homedir "{{ backup_gpg_keyring_path }}" --list-keys "{{ backup_gpg_recipient }}"
  become: true
  become_user: "{{ baserow_user }}"
  register: gpg_key_info
  when: backup_encryption_enabled

- name: "Pokaż informacje o kluczu GPG"
  debug:
    msg:
      - "=== KLUCZ GPG DLA BACKUPÓW ==="
      - "{{ gpg_key_info.stdout_lines }}"
  when: backup_encryption_enabled

- name: "Stwórz skrypt backup bazy danych"
  template:
    src: backup_database.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/backup_database.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  become: true

- name: "Stwórz skrypt backup plików media"
  template:
    src: backup_media.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/backup_media.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  become: true

- name: "Stwórz skrypt pełnego backup"
  template:
    src: full_backup.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/full_backup.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  become: true

- name: "Stwórz skrypt restore"
  template:
    src: restore_backup.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/restore_backup.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  become: true

- name: "Stwórz konfigurację logrotate dla backupów"
  copy:
    content: |
      # Logrotate dla backupów Baserow
      {{ baserow_home }}/backups/*.log {
          daily
          rotate 7
          compress
          delaycompress
          missingok
          notifempty
          create 0644 {{ baserow_user }} {{ baserow_user }}
      }
    dest: /etc/logrotate.d/baserow-backup
    mode: '0644'
  become: true

- name: "Test skryptu backup bazy danych"
  shell: |
    cd {{ baserow_home }}/backups/scripts
    ./backup_database.sh --test
  become: true
  become_user: "{{ baserow_user }}"
  register: backup_test
  changed_when: false

- name: "Wyświetl wynik testu backup"
  debug:
    msg:
      - "=== TEST SKRYPTU BACKUP ===" 
      - "{{ backup_test.stdout_lines }}"

- name: "Dodaj cron job dla codziennego backup"
  cron:
    name: "Baserow daily backup"
    minute: "{{ backup_cron_minute }}"
    hour: "{{ backup_cron_hour }}"
    job: "{{ baserow_home }}/backups/scripts/full_backup.sh >> {{ baserow_home }}/backups/backup.log 2>&1"
    user: "{{ baserow_user }}"
  become: true
  when: backup_cron_enabled

- name: "Dodaj cron job dla czyszczenia starych backupów"
  cron:
    name: "Baserow cleanup old backups"
    minute: "0"
    hour: "3"
    job: "{% if backup_encryption_enabled %}find {{ baserow_home }}/backups/database -name '*.sql.gz.gpg' -mtime +{{ backup_retention_days }} -delete && find {{ baserow_home }}/backups/media -name '*.tar.gz.gpg' -mtime +{{ backup_retention_days }} -delete{% else %}find {{ baserow_home }}/backups/database -name '*.sql.gz' -mtime +{{ backup_retention_days }} -delete && find {{ baserow_home }}/backups/media -name '*.tar.gz' -mtime +{{ backup_retention_days }} -delete{% endif %}"
    user: "{{ baserow_user }}"
  become: true
  when: backup_cron_enabled

- name: "Stwórz katalog dla testów restore"
  file:
    path: "{{ backup_test_restore_dir }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  become: true

- name: "Zainstaluj skrypt weryfikacji backupów"
  template:
    src: verify_backup.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/verify_backup.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_user }}"
    mode: '0755'
  become: true

- name: "Dodaj cron job dla weryfikacji backupów"
  cron:
    name: "Baserow backup verification"
    minute: "{{ backup_verification_schedule.split(' ')[0] }}"
    hour: "{{ backup_verification_schedule.split(' ')[1] }}"
    day: "{{ backup_verification_schedule.split(' ')[2] }}"
    month: "{{ backup_verification_schedule.split(' ')[3] }}"
    weekday: "{{ backup_verification_schedule.split(' ')[4] }}"
    job: "{{ baserow_home }}/backups/scripts/verify_backup.sh >> {{ baserow_home }}/backups/verification.log 2>&1"
    user: "{{ baserow_user }}"
  become: true
  when: backup_verification_enabled

- name: "Konfiguracja powiadomień email (opcjonalne)"
  block:
    - name: "Sprawdź czy komenda mail jest dostępna"
      command: which mail
      register: mail_command
      ignore_errors: true
      changed_when: false

    - name: "Wyświetl informację o braku komendy mail"
      debug:
        msg: "Komenda 'mail' nie jest dostępna, powiadomienia email nie będą wysyłane."
      when: mail_command.rc != 0

    - name: "Zainstaluj pakiet mailutils (Debian/Ubuntu)"
      apt:
        name: mailutils
        state: present
      become: true
      when:
        - ansible_os_family == "Debian"
        - mail_command.rc != 0

    - name: "Zainstaluj pakiet mailx (CentOS/RHEL)"
      yum:
        name: mailx
        state: present
      become: true
      when:
        - ansible_os_family == "RedHat"
        - mail_command.rc != 0
  when: backup_verification_email

- name: "Podsumowanie konfiguracji backup"
  debug:
    msg:
      - "=== KONFIGURACJA BACKUP UKOŃCZONA ==="
      - "Katalog backupów: {{ baserow_home }}/backups"
      - "Skrypty backup: {{ baserow_home }}/backups/scripts/"
      - "Cron backup: {{ 'Aktywny' if backup_cron_enabled else 'Wyłączony' }}"
      - "Czas backup: {{ backup_cron_hour }}:{{ backup_cron_minute }} codziennie"
      - "Retencja: {{ backup_retention_days }} dni"
      - "Logrotate: Skonfigurowany"
      - "Szyfrowanie GPG: {{ 'Włączone' if backup_encryption_enabled else 'Wyłączone' }}"
      - "{% if backup_encryption_enabled %}Odbiorca GPG: {{ backup_gpg_recipient }}{% endif %}"
      - "{% if backup_encryption_enabled %}Keyring: {{ backup_gpg_keyring_path }}{% endif %}"
      - ""
      - "DOSTĘPNE KOMENDY:"
      - "  Backup bazy: {{ baserow_home }}/backups/scripts/backup_database.sh"
      - "  Backup media: {{ baserow_home }}/backups/scripts/backup_media.sh"
      - "  Pełny backup: {{ baserow_home }}/backups/scripts/full_backup.sh"
      - "  Restore: {{ baserow_home }}/backups/scripts/restore_backup.sh"
      - "  Verify: {{ baserow_home }}/backups/scripts/verify_backup.sh"
      - ""
      - "{% if backup_encryption_enabled %}UWAGA: Backupy są szyfrowane GPG! Zachowaj bezpiecznie klucz prywatny.{% endif %}"