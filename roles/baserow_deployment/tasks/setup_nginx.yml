---
# Konfigu<PERSON><PERSON> i SSL dla Baserow

- name: "Debug variables"
  debug:
    msg:
      - "baserow_domain: {{ baserow_domain }}"
      - "baserow_alternative_domains: {{ baserow_alternative_domains }}"
  tags: debug_nginx_vars

- name: "<PERSON>ain<PERSON>uj Nginx"
  apt:
    name: nginx
    state: present
  become: true
  tags: setup_nginx

- name: "Zatrzymaj domyślny Nginx (je<PERSON><PERSON> d<PERSON>ła)"
  service:
    name: nginx
    state: stopped
  become: true
  ignore_errors: true
  tags: setup_nginx

- name: "<PERSON><PERSON><PERSON> domyślną konfigurację Nginx"
  file:
    path: "{{ item }}"
    state: absent
  become: true
  loop:
    - /etc/nginx/sites-enabled/default
    - /etc/nginx/sites-available/default
    - /var/www/html
  tags: setup_nginx

- name: "Stwórz katalogi dla Nginx"
  file:
    path: "{{ item }}"
    state: directory
    owner: www-data
    group: www-data
    mode: '0755'
  become: true
  loop:
    - /var/www/certbot
    - /etc/nginx/snippets
    - "{{ baserow_home }}/nginx/conf.d"
    - "{{ baserow_home }}/ssl"
    - /etc/nginx/ssl
  tags: setup_nginx

- name: "Zainstaluj Certbot dla Let's Encrypt"
  apt:
    name:
      - certbot
      - python3-certbot-nginx
    state: present
  become: true
  tags: setup_nginx

- name: "Stwórz tymczasową konfigurację Nginx (dla certyfikatów)"
  copy:
    content: |
      server {
          listen 80;
          server_name {{ baserow_domain }}{% for domain in baserow_alternative_domains %} {{ domain }}{% endfor %};
          
          location /.well-known/acme-challenge/ {
              root /var/www/certbot;
              try_files $uri =404;
          }
          
          location /health {
              return 200 "OK";
              add_header Content-Type text/plain;
          }
          
          location / {
              return 200 "Baserow installation in progress...";
              add_header Content-Type text/plain;
          }
      }
    dest: /etc/nginx/sites-available/{{ baserow_domain }}
    mode: '0644'
  become: true
  tags: setup_nginx

- name: "Aktywuj tymczasową konfigurację Nginx"
  file:
    src: /etc/nginx/sites-available/{{ baserow_domain }}
    dest: /etc/nginx/sites-enabled/{{ baserow_domain }}
    state: link
  become: true
  notify: restart nginx
  tags: setup_nginx

- name: "Uruchom Nginx"
  service:
    name: nginx
    state: started
    enabled: true
  become: true
  tags: setup_nginx

- name: "Sprawdź czy Nginx działa"
  uri:
    url: "http://{{ baserow_domain }}/health"
    method: GET
    status_code: 200
  register: nginx_health_check
  retries: 3
  delay: 5
  ignore_errors: true
  tags: setup_nginx

- name: "Wyświetl status Nginx"
  debug:
    msg: "Nginx health check: {{ 'OK' if nginx_health_check is success else 'BŁĄD' }}"
  tags: setup_nginx

# =============================================================================
# SSL CERTIFICATES - Let's Encrypt
# =============================================================================

- name: "Sprawdź czy certyfikaty już istnieją"
  stat:
    path: "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem"
  register: existing_cert
  tags: setup_nginx

- name: "Uzyskaj certyfikat SSL dla {{ baserow_domain }}"
  shell: |
    certbot certonly \
      --webroot \
      --webroot-path=/var/www/certbot \
      --email {{ ssl_email }} \
      --agree-tos \
      --no-eff-email \
      -d {{ baserow_domain }} \
      {% for domain in baserow_alternative_domains %}-d {{ domain }} {% endfor %}\
      --non-interactive
  become: true
  when: not existing_cert.stat.exists
  register: cert_result
  tags: setup_nginx

- name: "Wyświetl rezultat certyfikatu"
  debug:
    msg:
      - "Certyfikat SSL: {{ 'SUKCES' if cert_result is success else 'BŁĄD' }}"
      - "{{ cert_result.stdout_lines if cert_result.stdout_lines is defined else 'Certyfikat już istniał' }}"
  tags: setup_nginx

- name: "Skopiuj certyfikat do katalogu Nginx"
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    remote_src: true
    owner: root
    group: root
    mode: '0644'
  become: true
  loop:
    - { src: "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem", dest: "{{ baserow_home }}/ssl/{{ baserow_domain }}.crt" }
    - { src: "/etc/letsencrypt/live/{{ baserow_domain }}/privkey.pem", dest: "{{ baserow_home }}/ssl/{{ baserow_domain }}.key" }
    - { src: "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem", dest: "/etc/nginx/ssl/{{ baserow_domain }}.crt" }
    - { src: "/etc/letsencrypt/live/{{ baserow_domain }}/privkey.pem", dest: "/etc/nginx/ssl/{{ baserow_domain }}.key" }
  when: existing_cert.stat.exists or (cert_result is defined and cert_result is success)
  tags: setup_nginx

- name: "Ustaw bezpieczne uprawnienia dla kluczy prywatnych"
  file:
    path: "{{ item }}"
    mode: '0600'
    owner: root
    group: root
  become: true
  loop:
    - "{{ baserow_home }}/ssl/{{ baserow_domain }}.key"
    - "/etc/nginx/ssl/{{ baserow_domain }}.key"
  tags: setup_nginx

# =============================================================================
# GŁÓWNA KONFIGURACJA NGINX
# =============================================================================

- name: "Stwórz główną konfigurację Nginx z template"
  template:
    src: nginx.conf.j2
    dest: "{{ baserow_home }}/nginx/nginx.conf"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0644'
  become: true
  notify: reload nginx
  tags: setup_nginx

- name: "Zastąp główną konfigurację nginx systemową"
  copy:
    src: "{{ baserow_home }}/nginx/nginx.conf"
    dest: "/etc/nginx/nginx.conf"
    remote_src: true
    owner: root
    group: root
    mode: '0644'
    backup: true
  become: true
  notify: reload nginx
  tags: setup_nginx

- name: "Sprawdź składnię konfiguracji Nginx"
  shell: nginx -t
  become: true
  register: nginx_syntax_check
  changed_when: false
  tags: setup_nginx

- name: "Wyświetl wynik sprawdzenia składni"
  debug:
    msg: "Składnia Nginx: {{ 'OK' if nginx_syntax_check.rc == 0 else 'BŁĄD' }}"
  tags: setup_nginx

- name: "Stwórz cron job dla odnowienia certyfikatów"
  cron:
    name: "Renew Let's Encrypt certificates"
    minute: "0"
    hour: "3"
    job: "certbot renew --quiet && systemctl reload nginx"
    user: root
  become: true
  tags: setup_nginx

- name: "Skonfiguruj logrotate dla Nginx"
  copy:
    content: |
      {{ baserow_home }}/logs/nginx/*.log {
          daily
          rotate 30
          compress
          delaycompress
          missingok
          notifempty
          create 0644 www-data www-data
          postrotate
              systemctl reload nginx > /dev/null 2>&1 || true
          endscript
      }
    dest: /etc/logrotate.d/baserow-nginx
    mode: '0644'
  become: true
  tags: setup_nginx

- name: "Wyświetl podsumowanie konfiguracji SSL"
  debug:
    msg:
      - "=== KONFIGURACJA SSL UKOŃCZONA ==="
      - "Domena główna: {{ baserow_domain }}"
      - "Domeny dodatkowe: {{ baserow_alternative_domains | join(', ') }}"
      - "Certyfikat: /etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem"
      - "Auto-renewal: Codziennie o 3:00"
      - "Nginx config: {{ baserow_home }}/nginx/nginx.conf"
      - "Logs: {{ baserow_home }}/logs/nginx/"
  tags: setup_nginx