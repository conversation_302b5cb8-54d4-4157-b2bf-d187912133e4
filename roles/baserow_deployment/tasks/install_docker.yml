---
# Instal<PERSON>ja i konfiguracja Docker oraz Docker Compose

- name: "<PERSON><PERSON><PERSON> starsze wersje Docker jeśli istniej<PERSON>"
  apt:
    name:
      - docker
      - docker-engine
      - docker.io
      - containerd
      - runc
    state: absent
  become: true
  ignore_errors: true

- name: "<PERSON>ainstal<PERSON><PERSON> wymagane pakiety dla Docker"
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gnupg
      - lsb-release
    state: present
    update_cache: true
  become: true

- name: "Dodaj klucz GPG Docker"
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
  become: true

- name: "Dodaj repozytorium Docker"
  apt_repository:
    repo: "deb [arch=arm64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
    state: present
    update_cache: true
  become: true

- name: "Zainstaluj Docker Engine"
  apt:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
      - docker-compose-plugin
    state: present
    update_cache: true
  become: true
  notify: restart docker

- name: "<PERSON><PERSON><PERSON> użytkownika do grupy docker"
  user:
    name: "{{ ansible_user }}"
    groups: docker
    append: true
  become: true
  when: not ansible_check_mode

- name: "Stwórz katalog konfiguracyjny Docker"
  file:
    path: /etc/docker
    state: directory
    mode: '0755'
  become: true

- name: "Skonfiguruj Docker daemon (optymalizacja)"
  copy:
    content: |
      {
        "log-driver": "json-file",
        "log-opts": {
          "max-size": "{{ log_max_size | default('10m') }}",
          "max-file": "{{ log_max_files | default('3') }}"
        },
        "storage-driver": "overlay2",
        "live-restore": true,
        "userland-proxy": false,
        "experimental": false,
        "metrics-addr": "127.0.0.1:9323",
        "default-ulimits": {
          "nofile": {
            "Name": "nofile",
            "Hard": 65536,
            "Soft": 65536
          }
        }
      }
    dest: /etc/docker/daemon.json
    mode: '0644'
  become: true
  notify: restart docker

  
- name: "Sprawdź czy Docker działa po konfiguracji"
  systemd:
    name: docker
    state: started
  become: true
  when: not ansible_check_mode
  register: docker_status
  ignore_errors: true

- name: "Wyświetl status Docker po konfiguracji"
  debug:
    msg: "Docker status: {{ 'DZIAŁA' if docker_status is success else 'PROBLEM - będzie próba naprawy' }}"

- name: "Sprawdź wersję Docker"
  command: docker --version
  register: docker_version
  changed_when: false
  when: not ansible_check_mode

- name: "Wyświetl wersję Docker"
  debug:
    msg: "Zainstalowana wersja Docker: {{ docker_version.stdout if docker_version.stdout is defined else 'N/A (dry-run mode)' }}"

- name: "Sprawdź wersję Docker Compose"
  command: docker-compose --version
  register: compose_version
  changed_when: false
  when: not ansible_check_mode

- name: "Wyświetl wersję Docker Compose"
  debug:
    msg: "Zainstalowana wersja Docker Compose: {{ compose_version.stdout if compose_version.stdout is defined else 'N/A (dry-run mode)' }}"

- name: "Przetestuj Docker uruchamiając kontener hello-world"
  command: docker run --rm hello-world
  register: docker_test
  ignore_errors: true
  changed_when: false
  when: not ansible_check_mode

- name: "Wyświetl wynik testu Docker"
  debug:
    msg:
      - "Test Docker: {{ 'SUKCES' if (docker_test.rc is defined and docker_test.rc == 0) else 'POMINIĘTO (dry-run mode)' if ansible_check_mode else 'BŁĄD' }}"
      - "{{ docker_test.stdout if docker_test.stdout is defined else 'Brak stdout' }}"

- name: "Sprawdź czy Docker działa poprawnie"
  command: docker info
  register: docker_info
  changed_when: false
  when: not ansible_check_mode

- name: "Wyświetl podstawowe informacje o Docker"
  debug:
    msg:
      - "=== DOCKER STATUS ==="
      - "Wersja: {{ docker_version.stdout if docker_version.stdout is defined else 'N/A (dry-run mode)' }}"
      - "Compose: {{ compose_version.stdout.split('\\n')[0] if compose_version.stdout is defined else 'N/A (dry-run mode)' }}"
      - "Test kontener: {{ 'OK' if (docker_test.rc is defined and docker_test.rc == 0) else 'POMINIĘTO (dry-run mode)' if ansible_check_mode else 'BŁĄD' }}"

- name: "Stwórz katalog dla Docker secrets"
  file:
    path: /etc/docker/secrets
    state: directory
    mode: '0700'
    owner: root
    group: root
  become: true

- name: "Skonfiguruj logrotate dla Docker logów"
  copy:
    content: |
      /var/lib/docker/containers/*/*.log {
          daily
          rotate 7
          compress
          size {{ log_max_size | default('10m') }}
          missingok
          delaycompress
          copytruncate
      }
    dest: /etc/logrotate.d/docker
    mode: '0644'
  become: true
