---
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> końcowa instalacji Baserow

- name: "Sprawd<PERSON> czy wszystkie kontenery działają"
  command: docker-compose ps --services --filter "status=running"
  args:
    chdir: "{{ baserow_home }}"
  register: running_services
  changed_when: false

- name: "Sprawdź status kontenerów"
  command: docker-compose ps
  args:
    chdir: "{{ baserow_home }}"
  register: container_status
  changed_when: false

- name: "Sprawdź czy Baserow odpowiada"
  uri:
    url: "http://localhost:{{ baserow_port }}"
    method: GET
    timeout: 30
  register: baserow_health
  ignore_errors: true

- name: "Sprawd<PERSON> czy Nginx działa"
  uri:
    url: "http://localhost:80"
    method: GET
    timeout: 10
  register: nginx_health
  ignore_errors: true

- name: "Sprawdź backup scripts"
  stat:
    path: "{{ item }}"
  register: backup_scripts_check
  loop:
    - "{{ baserow_home }}/backups/scripts/backup_database.sh"
    - "{{ baserow_home }}/backups/scripts/backup_media.sh"
    - "{{ baserow_home }}/backups/scripts/full_backup.sh"
    - "{{ baserow_home }}/backups/scripts/restore_backup.sh"

- name: "Sprawdź UFW status"
  command: ufw status
  become: true
  register: ufw_status
  changed_when: false
  ignore_errors: true

- name: "Sprawdź cron jobs"
  command: crontab -l -u {{ baserow_user }}
  become: true
  register: cron_status
  changed_when: false
  ignore_errors: true

- name: "Podsumowanie instalacji"
  debug:
    msg:
      - "=== PODSUMOWANIE INSTALACJI BASEROW ==="
      - ""
      - "📍 SERWER: {{ ansible_hostname }}"
      - "📍 SYSTEM: {{ ansible_distribution }} {{ ansible_distribution_version }}"
      - ""
      - "🏠 KATALOG BASEROW: {{ baserow_home }}"
      - "👤 UŻYTKOWNIK: {{ baserow_user }}"
      - "🌐 DOMENA: {{ baserow_domain }}"
      - "🔌 PORT: {{ baserow_port }}"
      - ""
      - "📊 STATUS:"
      - "  Kontenery uruchomione: {{ running_services.stdout_lines | length }}/4"
      - "  Baserow (localhost): {{ '✅ OK' if baserow_health.status == 200 else '❌ BŁĄD' }}"
      - "  Nginx: {{ '✅ OK' if nginx_health.status == 200 else '❌ BŁĄD' }}"
      - "  Backup scripts: {{ '✅ OK' if backup_scripts_check.results | selectattr('stat.exists') | list | length == 4 else '❌ BRAK' }}"
      - ""
      - "🔐 BEZPIECZEŃSTWO:"
      - "  UFW: {{ 'Skonfigurowany' if ufw_status.rc == 0 else 'Do konfiguracji' }}"
      - "  Cron backup: {{ 'Aktywny' if cron_status.rc == 0 else 'Do konfiguracji' }}"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Start: cd {{ baserow_home }} && docker-compose up -d"
      - "  Stop: cd {{ baserow_home }} && docker-compose down"
      - "  Logi: cd {{ baserow_home }} && docker-compose logs -f"
      - "  Backup: {{ baserow_home }}/backups/scripts/full_backup.sh"
      - "  Restore: {{ baserow_home }}/backups/scripts/restore_backup.sh --help"
      - ""
      - "🌐 DOSTĘP:"
      - "  Lokalny: http://localhost:{{ baserow_port }}"
      - "  Publiczny: http://{{ baserow_domain }} (po konfiguracji SSL)"
      - ""
      - "📝 NASTĘPNE KROKI:"
      - "  1. Włącz UFW: sudo ufw enable"
      - "  2. Skonfiguruj SSL: sudo certbot --nginx -d {{ baserow_domain }}"
      - "  3. Sprawdź backup: {{ baserow_home }}/backups/scripts/full_backup.sh --test"
      - "  4. Dodaj monitoring (Prometheus/Grafana)"

- name: "Wyświetl szczegółowy status"
  debug:
    msg:
      - "=== SZCZEGÓŁOWY STATUS ==="
      - ""
      - "KONTENERY:"
      - "{{ container_status.stdout_lines }}"
      - ""
      - "RUNNING SERVICES:"
      - "{{ running_services.stdout_lines }}"
      - ""
      - "UFW STATUS:"
      - "{{ ufw_status.stdout if ufw_status.rc == 0 else 'UFW nie jest aktywny' }}"