---
# Przygotowanie systemu dla wdrożenia Baserow

- name: "Aktualizuj cache pakietów"
  apt:
    update_cache: true
    cache_valid_time: 3600
  become: true

- name: "Zaktualizuj wszystkie pakiety systemowe"
  apt:
    upgrade: dist
    autoremove: true
  become: true
  register: system_update

- name: "Wyświetl informacje o aktualizacji"
  debug:
    msg: "Pakiety zostały zaktualizowane: {{ system_update.changed }}"

- name: "Zainstaluj niezbędne pakiety systemowe"
  apt:
    name:
      - curl
      - wget
      - gnupg
      - lsb-release
      - ca-certificates
      - software-properties-common
      - apt-transport-https
      - python3-pip
      - python3-setuptools
      - unzip
      - tree
      - htop
      - rsync
      - logrotate
      - cron
    state: present
  become: true

- name: "Zainstaluj pip3 jeśli nie jest dostępny"
  apt:
    name:
      - python3-pip
      - python3-setuptools
    state: present
  become: true

- name: "Zainstaluj pakiety Python dla Ansible"
  pip:
    name:
      - docker
      - docker-compose
      - requests
    state: present
    executable: pip3
  become: true
  when: ansible_facts.packages['python3-pip'] is defined

- name: "Sprawdź dostępność domeny baserow.simetria.pl"
  uri:
    url: "http://{{ baserow_domain }}"
    method: HEAD
    status_code: [200, 301, 302, 404, 403, -1]
  register: domain_check
  ignore_errors: true

- name: "Wyświetl status domeny"
  debug:
    msg: 
      - "Status domeny {{ baserow_domain }}: {{ 'DOSTĘPNA' if domain_check.status is defined else 'NIEOSIĄGALNA' }}"
      - "Kod odpowiedzi: {{ domain_check.status | default('N/A') }}"

- name: "Sprawdź dostępne miejsce na dysku"
  shell: df -h / | awk 'NR==2 {print $4}'
  register: disk_space
  changed_when: false

- name: "Wyświetl dostępne miejsce na dysku"
  debug:
    msg: "Dostępne miejsce na dysku: {{ disk_space.stdout }}"

- name: "Sprawdź czy jest wystarczająco miejsca na dysku (minimum 2GB)"
  shell: df / | awk 'NR==2 {print $4}'
  register: disk_space_kb
  failed_when: disk_space_kb.stdout | int < 2097152  # 2GB w KB
  changed_when: false

- name: "Wyświetl informacje o pamięci RAM"
  debug:
    msg: "Dostępna pamięć RAM: {{ ansible_memory_mb.nocache.free }}MB (wolna: {{ ansible_memfree_mb }}MB, całkowita: {{ ansible_memtotal_mb }}MB)"

- name: "Sprawdź czy jest wystarczająco RAM (minimum 512MB dostępne)"
  fail:
    msg: "Niewystarczająca ilość dostępnej pamięci RAM: {{ ansible_memory_mb.nocache.free }}MB (wymagane minimum: 512MB)"
  when: ansible_memory_mb.nocache.free | int < 512

- name: "Ustaw timezone na Europe/Warsaw"
  timezone:
    name: Europe/Warsaw
  become: true
  notify: restart cron

- name: "Sprawdź czy NTP jest skonfigurowane"
  shell: timedatectl status | grep "NTP synchronized" | awk '{print $3}'
  register: ntp_status
  changed_when: false

- name: "Wyświetl status synchronizacji czasu"
  debug:
    msg: "NTP synchronizacja: {{ ntp_status.stdout }}"

- name: "Skonfiguruj limity systemowe dla kontenerów"
  pam_limits:
    domain: "{{ baserow_user }}"
    limit_type: "{{ item.type }}"
    limit_item: "{{ item.item }}"
    value: "{{ item.value }}"
  loop:
    - { type: 'soft', item: 'nofile', value: '65536' }
    - { type: 'hard', item: 'nofile', value: '65536' }
    - { type: 'soft', item: 'nproc', value: '4096' }
    - { type: 'hard', item: 'nproc', value: '4096' }
  become: true

- name: "Wyłącz swap jeśli jest włączony (optymalizacja dla kontenerów)"
  shell: swapoff -a
  become: true
  ignore_errors: true
  when: ansible_swaptotal_mb > 0

- name: "Zaktualizuj /etc/fstab aby wyłączyć swap na stałe"
  replace:
    path: /etc/fstab
    regexp: '^([^#].*?\sswap\s+sw\s+.*)$'
    replace: '# \1'
  become: true
  when: ansible_swaptotal_mb > 0

- name: "Sprawdź status systemu po przygotowaniu"
  debug:
    msg:
      - "=== STATUS SYSTEMU ==="
      - "Hostname: {{ ansible_hostname }}"
      - "OS: {{ ansible_distribution }} {{ ansible_distribution_version }}"
      - "Kernel: {{ ansible_kernel }}"
      - "Architektura: {{ ansible_architecture }}"
      - "Pamięć: {{ ansible_memtotal_mb }}MB"
      - "Dostępne miejsce: {{ disk_space.stdout }}"
      - "Timezone: {{ ansible_date_time.tz }}"
      - "Swap wyłączony: {{ ansible_swaptotal_mb == 0 }}"
