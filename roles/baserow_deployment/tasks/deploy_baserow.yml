---
# W<PERSON><PERSON><PERSON><PERSON><PERSON> z Docker Compose

- name: "<PERSON><PERSON><PERSON><PERSON><PERSON> istniej<PERSON><PERSON> kontenery Baserow (jeśli istniej<PERSON>)"
  shell: |
    cd {{ baserow_home }}
    if [ -f docker-compose.yml ]; then
      docker-compose down || true
    fi
  become_user: "{{ baserow_user }}"
  become: true
  ignore_errors: true
  tags:
    - always

- name: "Konfiguracja"
  block:
    - name: "Stwórz plik docker-compose.yml z template"
      template:
        src: docker-compose.yml.j2
        dest: "{{ baserow_home }}/docker-compose.yml"
        owner: "{{ baserow_user }}"
        group: "{{ baserow_group }}"
        mode: '0644'
      become: true
      notify: restart baserow

    - name: "Stw<PERSON>rz plik .env z zmiennymi środowiskowymi"
      copy:
        content: |
          # Baserow Environment Variables
          # Wygenerowane przez Ansible
          
          COMPOSE_PROJECT_NAME={{ compose_project_name }}
          BASEROW_PUBLIC_URL={{ baserow_public_url }}
          
          # Database
          POSTGRES_DB={{ baserow_db_name }}
          POSTGRES_USER={{ baserow_db_user }}
          POSTGRES_PASSWORD={{ baserow_db_password }}
          
          # Security
          SECRET_KEY={{ baserow_secret_key }}
          
          # Performance
          BASEROW_AMOUNT_OF_WORKERS={{ baserow_backend_workers }}
          
          # Redis
          REDIS_MAXMEMORY={{ redis_maxmemory }}
          
          # Email
          {% if email_smtp_enabled %}
          EMAIL_SMTP=true
          EMAIL_SMTP_HOST={{ email_smtp_host }}
          EMAIL_SMTP_PORT={{ email_smtp_port }}
          EMAIL_SMTP_USER={{ email_smtp_user }}
          EMAIL_SMTP_PASSWORD={{ email_smtp_password }}
          FROM_EMAIL={{ email_from_address }}
          {% else %}
          EMAIL_SMTP=false
          {% endif %}
        dest: "{{ baserow_home }}/.env"
        owner: "{{ baserow_user }}"
        group: "{{ baserow_group }}"
        mode: '0600'
      become: true

    - name: "Stwórz konfigurację PostgreSQL"
      copy:
        content: |
          # PostgreSQL Configuration for Baserow
          # Zoptymalizowana dla {{ ansible_memtotal_mb }}MB RAM
          
          # Memory settings
          shared_buffers = {{ postgres_shared_buffers }}
          effective_cache_size = {{ postgres_effective_cache_size }}
          work_mem = {{ postgres_work_mem }}
          maintenance_work_mem = {{ postgres_maintenance_work_mem }}
          
          # Connection settings
          max_connections = {{ postgres_max_connections }}
          
          # Checkpoints
          checkpoint_completion_target = 0.9
          wal_buffers = 16MB
          
          # Performance
          random_page_cost = 1.1
          effective_io_concurrency = 200
          
          # Logging
          log_statement = 'mod'
          log_min_duration_statement = 1000
          log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
          
          # Security
          ssl = off
          log_connections = on
          log_disconnections = on
        dest: "{{ baserow_home }}/postgres/postgresql.conf"
        owner: "{{ baserow_user }}"
        group: "{{ baserow_group }}"
        mode: '0644'
      become: true
  tags:
    - config

- name: "Pobieranie obrazów"
  block:
    - name: "Pobierz najnowsze obrazy Docker"
      shell: |
        cd {{ baserow_home }}
        docker-compose pull
      become_user: "{{ baserow_user }}"
      become: true
      register: docker_pull_result

    - name: "Wyświetl rezultat pobierania obrazów"
      debug:
        msg: "Pobieranie obrazów Docker: {{ 'SUKCES' if docker_pull_result.rc == 0 else 'BŁĄD' }}"
  tags:
    - pull

- name: "Uruchamianie"
  block:
    - name: "Uruchom Baserow"
      shell: |
        cd {{ baserow_home }}
        docker-compose up -d
      become_user: "{{ baserow_user }}"
      become: true
      register: baserow_start_result

    - name: "Wyświetl rezultat uruchomienia Baserow"
      debug:
        msg: "Uruchomienie Baserow: {{ 'SUKCES' if baserow_start_result.rc == 0 else 'BŁĄD' }}"

    - name: "Poczekaj na uruchomienie kontenerów (10 sekund)"
      pause:
        seconds: 10
        
    - name: "Sprawdź czy kontenery są uruchomione"
      shell: |
        cd {{ baserow_home }}
        docker-compose ps | grep -c "Up" || echo "0"
      become_user: "{{ baserow_user }}"
      become: true
      register: container_check
      ignore_errors: true
      
    - name: "Wyświetl wynik sprawdzenia kontenerów"
      debug:
        msg: "Liczba działających kontenerów: {{ container_check.stdout }}"
  tags:
    - run

- name: "Status"
  block:
    - name: "Sprawdź status kontenerów"
      shell: |
        cd {{ baserow_home }}
        docker-compose ps
      become_user: "{{ baserow_user }}"
      become: true
      register: containers_status

    - name: "Wyświetl status kontenerów"
      debug:
        msg: 
          - "=== STATUS KONTENERÓW BASEROW ==="
          - "{{ containers_status.stdout_lines }}"

    - name: "Sprawdź logi kontenerów w poszukiwaniu błędów"
      shell: |
        cd {{ baserow_home }}
        echo "=== BACKEND LOGS ==="
        docker-compose logs --tail 20 backend | grep -i error || echo "Brak błędów w backend"
        echo ""
        echo "=== FRONTEND LOGS ==="
        docker-compose logs --tail 20 frontend | grep -i error || echo "Brak błędów w frontend"
        echo ""
        echo "=== POSTGRES LOGS ==="
        docker-compose logs --tail 20 postgres | grep -i error || echo "Brak błędów w postgres"
      become_user: "{{ baserow_user }}"
      become: true
      register: container_logs

    - name: "Wyświetl logi kontenerów"
      debug:
        msg:
          - "=== ANALIZA LOGÓW ==="
          - "{{ container_logs.stdout_lines }}"
  tags:
    - status

- name: "Testy łączności"
  block:
    - name: "Test connectivity do backend"
      uri:
        url: "http://localhost:8000/_health/"
        method: GET
        status_code: 200
      register: backend_health
      ignore_errors: true
      retries: 5
      delay: 10

    - name: "Test connectivity do frontend"
      uri:
        url: "http://localhost:3000/_health/"
        method: GET
        status_code: 200
      register: frontend_health
      ignore_errors: true
      retries: 5
      delay: 10

    - name: "Wyświetl wyniki testów connectivity"
      debug:
        msg:
          - "=== TESTY CONNECTIVITY ==="
          - "Backend health: {{ 'OK' if backend_health is success else 'BŁĄD' }}"
          - "Frontend health: {{ 'OK' if frontend_health is success else 'BŁĄD' }}"
  tags:
    - healthcheck

- name: "Zasoby"
  block:
    - name: "Sprawdź wykorzystanie zasobów"
      shell: |
        echo "=== DOCKER STATS ==="
        docker stats --no-stream --format '{% raw %}table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}{% endraw %}'
        echo ""
        echo "=== SYSTEM RESOURCES ==="
        echo "RAM:"
        free -h
        echo ""
        echo "DISK:"
        df -h "{{ baserow_home }}"
      register: resource_usage

    - name: "Wyświetl wykorzystanie zasobów"
      debug:
        msg:
          - "=== WYKORZYSTANIE ZASOBÓW ==="
          - "{{ resource_usage.stdout_lines }}"
  tags:
    - resources
