---
# Główny plik zadań dla roli baserow_deployment

- name: "Rozpoczęcie instalacji Baserow"
  debug:
    msg:
      - "=== ROZPOCZĘCIE INSTALACJI BASEROW ==="
      - "Serwer: {{ ansible_hostname }}"
      - "Architektura: {{ ansible_architecture }}"
      - "Użytkownik: {{ baserow_user }}"
      - "Katalog: {{ baserow_home }}"
      - "Domena: {{ baserow_domain }}"
      - "Port: {{ baserow_port }}"

# =============================================================================
# ETAP 1: PRZYGOTOWANIE SYSTEMU
# =============================================================================

- name: "Przygotowanie systemu"
  include_tasks: prepare_system.yml

# =============================================================================
# ETAP 2: INSTALACJA DOCKER
# =============================================================================

- name: "Instalacja Docker"
  include_tasks: install_docker.yml

# =============================================================================
# ETAP 3: KONFIGURACJA UŻYTKOWNIKA
# =============================================================================

- name: "Konfiguracja użytkownika"
  include_tasks: setup_user.yml

# =============================================================================
# ETAP 4: DEPLOYMENT BASEROW
# =============================================================================

- name: "Deployment Baserow"
  import_tasks: deploy_baserow.yml

# =============================================================================
# ETAP 5: KONFIGURACJA NGINX I SSL
# =============================================================================

- name: "Konfiguracja Nginx i SSL"
  include_tasks: setup_nginx.yml
  tags: setup_nginx

# =============================================================================
# ETAP 6: KONFIGURACJA FIREWALL
# =============================================================================

- name: "Konfiguracja firewall"
  include_tasks: setup_firewall.yml
  tags: firewall

# =============================================================================
# ETAP 7: SETUP BACKUP
# =============================================================================

- name: "Konfiguracja backup"
  include_tasks: setup_backup.yml

# =============================================================================
# ETAP 8: WERYFIKACJA KOŃCOWA
# =============================================================================

- name: "Weryfikacja instalacji"
  include_tasks: verify_installation.yml

# =============================================================================
# PODSUMOWANIE KOŃCOWE
# =============================================================================

- name: "Podsumowanie instalacji"
  debug:
    msg:
      - "=== INSTALACJA BASEROW ZAKOŃCZONA ==="
      - ""
      - "🎉 GRATULACJE! Baserow został zainstalowany pomyślnie!"
      - ""
      - "🔗 DOSTĘP:"
      - "  Lokalny: http://localhost:{{ baserow_port }}"
      - "  Publiczny: http://{{ baserow_domain }} (po konfiguracji SSL)"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Katalog: {{ baserow_home }}"
      - "  Start: cd {{ baserow_home }} && docker-compose up -d"
      - "  Stop: cd {{ baserow_home }} && docker-compose down"
      - "  Logi: cd {{ baserow_home }} && docker-compose logs -f"
      - ""
      - "💾 BACKUP:"
      - "  Pełny backup: {{ baserow_home }}/backups/scripts/full_backup.sh"
      - "  Restore: {{ baserow_home }}/backups/scripts/restore_backup.sh --help"
      - ""
      - "🔐 BEZPIECZEŃSTWO:"
      - "  1. Włącz UFW: sudo ufw enable"
      - "  2. Skonfiguruj SSL: sudo certbot --nginx -d {{ baserow_domain }}"
      - "  3. Sprawdź backup: {{ baserow_home }}/backups/scripts/full_backup.sh --test"
      - ""
      - "📊 MONITORING:"
      - "  Dodaj monitoring Prometheus/Grafana w następnym kroku"
      - ""
      - "📚 DOKUMENTACJA:"
      - "  README: {{ baserow_home }}/README.md"
      - "  Logs: {{ baserow_home }}/logs/"
      - ""
      - "🚀 BASEROW GOTOWY DO UŻYCIA!"
