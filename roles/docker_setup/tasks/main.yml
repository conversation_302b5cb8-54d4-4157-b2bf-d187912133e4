---
# Instalacja i konfiguracja Docker oraz Docker Compose

- name: "Rozpoczęcie instalacji Docker"
  debug:
    msg:
      - "=== INSTALACJA DOCKER ==="
      - "Serwer: {{ ansible_hostname }}"
      - "Architektura: {{ ansible_architecture }}"
  tags: [docker, info]

# =============================================================================
# PRZYGOTOWANIE SYSTEMU
# =============================================================================

- name: "Usuń starsze wersje Docker jeśli istnieją"
  apt:
    name:
      - docker
      - docker-engine
      - docker.io
      - containerd
      - runc
    state: absent
  become: true
  ignore_errors: true
  tags: [docker, cleanup]

- name: "<PERSON><PERSON><PERSON>u<PERSON> wymagane pakiety dla Docker"
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gnupg
      - lsb-release
    state: present
    update_cache: true
  become: true
  tags: [docker, packages]

# =============================================================================
# INSTALACJA DOCKER
# =============================================================================

- name: "Dodaj klucz GPG Docker"
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
  become: true
  tags: [docker, repository]

- name: "Dodaj repozytorium Docker"
  apt_repository:
    repo: "deb [arch={{ docker_arch }}] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
    state: present
    update_cache: true
  become: true
  tags: [docker, repository]

- name: "Zainstaluj Docker Engine"
  apt:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
      - docker-compose-plugin
    state: present
    update_cache: true
  become: true
  notify: restart docker
  tags: [docker, install]

# =============================================================================
# KONFIGURACJA DOCKER
# =============================================================================

- name: "Dodaj użytkownika do grupy docker"
  user:
    name: "{{ ansible_user }}"
    groups: docker
    append: true
  become: true
  when: not ansible_check_mode
  tags: [docker, user]

- name: "Stwórz katalog konfiguracyjny Docker"
  file:
    path: /etc/docker
    state: directory
    mode: '0755'
  become: true
  tags: [docker, config]

- name: "Skonfiguruj Docker daemon (optymalizacja)"
  copy:
    content: |
      {
        "log-driver": "json-file",
        "log-opts": {
          "max-size": "{{ docker_log_max_size }}",
          "max-file": "{{ docker_log_max_files }}"
        },
        "storage-driver": "overlay2",
        "live-restore": true,
        "userland-proxy": false,
        "experimental": false,
        "metrics-addr": "127.0.0.1:9323",
        "default-ulimits": {
          "nofile": {
            "Name": "nofile",
            "Hard": 65536,
            "Soft": 65536
          }
        }
      }
    dest: /etc/docker/daemon.json
    mode: '0644'
  become: true
  notify: restart docker
  tags: [docker, config]

# =============================================================================
# WERYFIKACJA INSTALACJI
# =============================================================================

- name: "Sprawdź czy Docker działa po konfiguracji"
  systemd:
    name: docker
    state: started
    enabled: true
  become: true
  when: not ansible_check_mode
  register: docker_status
  ignore_errors: true
  tags: [docker, verify]

- name: "Sprawdź wersję Docker"
  command: docker --version
  register: docker_version
  changed_when: false
  tags: [docker, verify]

- name: "Sprawdź wersję Docker Compose"
  command: docker compose version
  register: docker_compose_version
  changed_when: false
  tags: [docker, verify]

- name: "Test Docker - uruchom hello-world"
  command: docker run --rm hello-world
  register: docker_test
  changed_when: false
  ignore_errors: true
  tags: [docker, test]

# =============================================================================
# KONFIGURACJA LOGROTATE
# =============================================================================

- name: "Skonfiguruj logrotate dla Docker logów"
  copy:
    content: |
      /var/lib/docker/containers/*/*.log {
          daily
          rotate 7
          compress
          size {{ docker_log_max_size }}
          missingok
          delaycompress
          copytruncate
      }
    dest: /etc/logrotate.d/docker
    mode: '0644'
  become: true
  tags: [docker, logrotate]

# =============================================================================
# PODSUMOWANIE
# =============================================================================

- name: "Podsumowanie instalacji Docker"
  debug:
    msg:
      - "=== INSTALACJA DOCKER ZAKOŃCZONA ==="
      - ""
      - "🐳 DOCKER:"
      - "  Wersja: {{ docker_version.stdout }}"
      - "  Status: {{ 'Uruchomiony' if docker_status.state == 'started' else 'Błąd' }}"
      - ""
      - "🔧 DOCKER COMPOSE:"
      - "  Wersja: {{ docker_compose_version.stdout }}"
      - ""
      - "✅ TEST:"
      - "  Hello World: {{ 'OK' if docker_test.rc == 0 else 'BŁĄD' }}"
      - ""
      - "📊 KONFIGURACJA:"
      - "  Log driver: json-file"
      - "  Max log size: {{ docker_log_max_size }}"
      - "  Max log files: {{ docker_log_max_files }}"
      - "  Storage driver: overlay2"
      - "  Metrics: 127.0.0.1:9323"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Status: sudo systemctl status docker"
      - "  Restart: sudo systemctl restart docker"
      - "  Logi: sudo journalctl -u docker"
  tags: [docker, info]
