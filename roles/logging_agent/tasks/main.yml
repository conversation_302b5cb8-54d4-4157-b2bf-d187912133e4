---
# <PERSON><PERSON><PERSON>ne zadania dla roli logging_agent
# Instal<PERSON><PERSON> Pro<PERSON>tail, Node Exporter i innych eksporterów

- name: Utwórz grupę systemową dla logging agent
  group:
    name: "{{ logging_agent_group }}"
    state: present
    system: true

- name: Utw<PERSON>rz użytkownika systemowego dla logging agent
  user:
    name: "{{ logging_agent_user }}"
    group: "{{ logging_agent_group }}"
    home: "{{ logging_agent_home }}"
    shell: /bin/bash
    system: true
    create_home: true
    state: present

- name: Utwórz katalogi dla logging agent
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ logging_agent_user }}"
    group: "{{ logging_agent_group }}"
    mode: '0755'
  loop:
    - "{{ logging_agent_home }}"
    - "{{ logging_agent_config_dir }}"
    - "{{ logging_agent_data_dir }}"
    - "{{ logging_agent_data_dir }}/promtail"
    - "{{ logging_agent_data_dir }}/node_exporter"
    - "{{ node_exporter_textfile_dir }}"

- name: <PERSON><PERSON><PERSON>kownika logging-agent do grupy systemowych
  user:
    name: "{{ logging_agent_user }}"
    groups: adm,systemd-journal
    append: true

- name: <PERSON><PERSON><PERSON> konfigurację Promtail
  template:
    src: promtail-config.yml.j2
    dest: "{{ logging_agent_config_dir }}/promtail-config.yml"
    owner: "{{ logging_agent_user }}"
    group: "{{ logging_agent_group }}"
    mode: '0644'
  notify:
    - restart promtail

- name: Generuj systemd service dla Node Exporter
  template:
    src: node-exporter.service.j2
    dest: /etc/systemd/system/node-exporter.service
    owner: root
    group: root
    mode: '0644'
  notify:
    - reload systemd
    - restart node-exporter
  when: node_exporter_enabled | bool

- name: Generuj systemd service dla Promtail
  template:
    src: promtail.service.j2
    dest: /etc/systemd/system/promtail.service
    owner: root
    group: root
    mode: '0644'
  notify:
    - reload systemd
    - restart promtail

- name: Generuj systemd service dla Postgres Exporter
  template:
    src: postgres-exporter.service.j2
    dest: /etc/systemd/system/postgres-exporter.service
    owner: root
    group: root
    mode: '0644'
  notify:
    - reload systemd
    - restart postgres-exporter
  when: postgres_exporter_enabled | bool

- name: Generuj systemd service dla Redis Exporter
  template:
    src: redis-exporter.service.j2
    dest: /etc/systemd/system/redis-exporter.service
    owner: root
    group: root
    mode: '0644'
  notify:
    - reload systemd
    - restart redis-exporter
  when: redis_exporter_enabled | bool

- name: Generuj systemd service dla Nginx Exporter
  template:
    src: nginx-exporter.service.j2
    dest: /etc/systemd/system/nginx-exporter.service
    owner: root
    group: root
    mode: '0644'
  notify:
    - reload systemd
    - restart nginx-exporter
  when: nginx_exporter_enabled | bool

- name: Sprawdź czy Docker jest zainstalowany
  command: docker --version
  register: docker_check
  ignore_errors: true
  changed_when: false

- name: Zainstaluj Docker jeśli nie jest zainstalowany
  include_role:
    name: baserow_deployment
    tasks_from: install_docker
  when: docker_check.rc != 0


  

- name: Sprawdź dostępność binarek dla architektury x86_64
  uri:
    url: "https://github.com/prometheus/node_exporter/releases/latest"
    return_content: true
  register: node_exporter_release
  when: ansible_architecture == "x86_64"

- name: Pobierz i zainstaluj Node Exporter (binarny)
  get_url:
    url: "https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-arm64.tar.gz"
    dest: "/tmp/node_exporter.tar.gz"
    mode: '0644'
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Rozpakuj Node Exporter
  unarchive:
    src: "/tmp/node_exporter.tar.gz"
    dest: "/tmp"
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Skopiuj binarkę Node Exporter
  copy:
    src: "/tmp/node_exporter-1.6.1.linux-arm64/node_exporter"
    dest: "/usr/local/bin/node_exporter"
    owner: root
    group: root
    mode: '0755'
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Pobierz i zainstaluj Promtail (binarny)
  get_url:
    url: "https://github.com/grafana/loki/releases/download/v2.9.0/promtail-linux-arm64.zip"
    dest: "/tmp/promtail.zip"
    mode: '0644'
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Rozpakuj Promtail
  unarchive:
    src: "/tmp/promtail.zip"
    dest: "/tmp"
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Skopiuj binarkę Promtail
  copy:
    src: "/tmp/promtail-linux-arm64"
    dest: "/usr/local/bin/promtail"
    owner: root
    group: root
    mode: '0755'
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Pobierz i zainstaluj Postgres Exporter (binarny)
  get_url:
    url: "https://github.com/prometheus-community/postgres_exporter/releases/download/v0.15.0/postgres_exporter-0.15.0.linux-arm64.tar.gz"
    dest: "/tmp/postgres_exporter.tar.gz"
    mode: '0644'
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Rozpakuj Postgres Exporter
  unarchive:
    src: "/tmp/postgres_exporter.tar.gz"
    dest: "/tmp"
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Skopiuj binarkę Postgres Exporter
  copy:
    src: "/tmp/postgres_exporter-0.15.0.linux-arm64/postgres_exporter"
    dest: "/usr/local/bin/postgres_exporter"
    owner: root
    group: root
    mode: '0755'
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Pobierz i zainstaluj Redis Exporter (binarny)
  get_url:
    url: "https://github.com/oliver006/redis_exporter/releases/download/v1.58.0/redis_exporter-v1.58.0.linux-arm64.tar.gz"
    dest: "/tmp/redis_exporter.tar.gz"
    mode: '0644'
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Rozpakuj Redis Exporter
  unarchive:
    src: "/tmp/redis_exporter.tar.gz"
    dest: "/tmp"
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Skopiuj binarkę Redis Exporter
  copy:
    src: "/tmp/redis_exporter-v1.58.0.linux-arm64/redis_exporter"
    dest: "/usr/local/bin/redis_exporter"
    owner: root
    group: root
    mode: '0755'
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Pobierz i zainstaluj Nginx Exporter (binarny)
  get_url:
    url: "https://github.com/nginxinc/nginx-prometheus-exporter/releases/download/v1.1.0/nginx-prometheus-exporter_1.1.0_linux_arm64.tar.gz"
    dest: "/tmp/nginx_exporter.tar.gz"
    mode: '0644'
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Rozpakuj Nginx Exporter
  unarchive:
    src: "/tmp/nginx_exporter.tar.gz"
    dest: "/tmp"
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode

- name: Skopiuj binarkę Nginx Exporter
  copy:
    src: "/tmp/nginx-prometheus-exporter"
    dest: "/usr/local/bin/nginx_exporter"
    owner: root
    group: root
    mode: '0755'
    remote_src: true
  when: ansible_architecture == "aarch64" and not ansible_check_mode
  
- name: Utwórz puste pliki binarne w trybie check
  file:
    path: "/usr/local/bin/{{ item }}"
    state: touch
    mode: '0755'
    owner: root
    group: root
  loop:
    - node_exporter
    - promtail
    - postgres_exporter
    - redis_exporter
    - nginx_exporter
  when: ansible_check_mode

  

- name: Uruchom i włącz serwisy systemd
  systemd:
    name: "{{ item.name }}"
    state: started
    enabled: true
    daemon_reload: true
  loop: "{{ systemd_services }}"
  when: item.enabled | bool

- name: Konfiguruj firewall dla logging agent
  ufw:
    rule: allow
    port: "{{ item.split('/')[0] }}"
    proto: "{{ item.split('/')[1] }}"
    comment: "Logging Agent - {{ item }}"
  loop: "{{ logging_agent_firewall_ports }}"
  when: ansible_distribution == 'Ubuntu'

- name: Konfiguruj logrotate
  template:
    src: logrotate.j2
    dest: "/etc/logrotate.d/{{ item.name }}"
    owner: root
    group: root
    mode: '0644'
  loop: "{{ logrotate_rules }}"
  when: logrotate_enabled | bool

- name: Generuj skrypt monitoringu zdrowia
  template:
    src: health_check.sh.j2
    dest: "{{ logging_agent_home }}/health_check.sh"
    owner: "{{ logging_agent_user }}"
    group: "{{ logging_agent_group }}"
    mode: '0755'

- name: Dodaj zadanie cron dla health check
  cron:
    name: "Logging agent health check"
    minute: "*/5"
    job: "{{ logging_agent_home }}/health_check.sh"
    user: "{{ logging_agent_user }}"
    state: present

- name: Sprawdź status serwisów
  systemd:
    name: "{{ item.name }}"
  register: service_status
  loop: "{{ systemd_services }}"
  when: item.enabled | bool
  ignore_errors: true

- name: Wyświetl status serwisów
  debug:
    msg: "Serwis {{ item.item.name }} jest {{ 'uruchomiony' if item.status.ActiveState == 'active' else 'zatrzymany' }}"
  loop: "{{ service_status.results }}"
  when: item.item.enabled | bool and item.status is defined

- name: Sprawdź dostępność portów
  wait_for:
    host: "{{ ansible_default_ipv4.address }}"
    port: "{{ item.split('/')[0] }}"
    timeout: 10
  loop: "{{ logging_agent_firewall_ports }}"
  ignore_errors: true

- name: Sprawdź połączenie z centralnym serwerem logowania
  uri:
    url: "{{ loki_url }}/ready"
    method: GET
    timeout: 10
  register: loki_health
  ignore_errors: true

- name: Wyświetl status połączenia z Loki
  debug:
    msg: "Połączenie z Loki: {{ 'OK' if loki_health.status == 200 else 'BŁĄD' }}"
  when: loki_health.status is defined

- name: Czyść tymczasowe pliki
  file:
    path: "{{ item }}"
    state: absent
  loop:
    - "/tmp/node_exporter.tar.gz"
    - "/tmp/promtail.zip"
    - "/tmp/promtail-linux-arm64"
    - "/tmp/node_exporter-1.6.1.linux-arm64"