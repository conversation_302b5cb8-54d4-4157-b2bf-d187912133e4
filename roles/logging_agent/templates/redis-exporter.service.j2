[Unit]
Description=Redis Exporter
After=network.target

[Service]
Type=simple
User={{ logging_agent_user }}
Group={{ logging_agent_group }}
ExecStart=/usr/local/bin/redis_exporter --web.listen-address=0.0.0.0:{{ redis_exporter_port }} --redis.addr={{ redis_exporter_redis_addr }}
WorkingDirectory={{ logging_agent_home }}
Restart=always
RestartSec=10
SyslogIdentifier=redis_exporter

[Install]
WantedBy=multi-user.target