server:
  http_listen_port: {{ promtail_port }}
  grpc_listen_port: 0

positions:
  filename: {{ logging_agent_data_dir }}/promtail/positions.yaml

clients:
  - url: {{ loki_url }}/loki/api/v1/push

scrape_configs:
  - job_name: syslog
    static_configs:
      - targets:
          - localhost
        labels:
          job: syslog
          __path__: /var/log/syslog
          host: "{{ ansible_hostname }}"
          
  - job_name: auth
    static_configs:
      - targets:
          - localhost
        labels:
          job: auth
          __path__: /var/log/auth.log
          host: "{{ ansible_hostname }}"
          
  - job_name: kern
    static_configs:
      - targets:
          - localhost
        labels:
          job: kern
          __path__: /var/log/kern.log
          host: "{{ ansible_hostname }}"

{% if baserow_enabled | default(false) %}
  - job_name: baserow
    static_configs:
      - targets:
          - localhost
        labels:
          job: baserow
          __path__: {{ baserow_data_dir }}/logs/*.log
          host: "{{ ansible_hostname }}"
{% endif %}

{% if nginx_enabled | default(false) %}
  - job_name: nginx-access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-access
          __path__: /var/log/nginx/access.log
          host: "{{ ansible_hostname }}"
          
  - job_name: nginx-error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-error
          __path__: /var/log/nginx/error.log
          host: "{{ ansible_hostname }}"
{% endif %}

{% if docker_enabled | default(false) %}
  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          __path__: /var/lib/docker/containers/*/*.log
          host: "{{ ansible_hostname }}"
{% endif %}