[Unit]
Description=Postgres Exporter
After=network.target

[Service]
Type=simple
User={{ logging_agent_user }}
Group={{ logging_agent_group }}
Environment=DATA_SOURCE_NAME={{ postgres_exporter_data_source_name }}
ExecStart=/usr/local/bin/postgres_exporter --web.listen-address=0.0.0.0:{{ postgres_exporter_port }}
WorkingDirectory={{ logging_agent_home }}
Restart=always
RestartSec=10
SyslogIdentifier=postgres_exporter

[Install]
WantedBy=multi-user.target