[Unit]
Description=Nginx Exporter
After=network.target

[Service]
Type=simple
User={{ logging_agent_user }}
Group={{ logging_agent_group }}
ExecStart=/usr/local/bin/nginx_exporter --web.listen-address=0.0.0.0:{{ nginx_exporter_port }} --nginx.scrape-uri={{ nginx_exporter_scrape_uri }}
WorkingDirectory={{ logging_agent_home }}
Restart=always
RestartSec=10
SyslogIdentifier=nginx_exporter

[Install]
WantedBy=multi-user.target