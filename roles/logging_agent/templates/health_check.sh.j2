#!/bin/bash

# Skrypt monitoringu zdrowia dla logging agent
# Generowany przez Ansible

LOG_FILE="{{ logging_agent_home }}/health_check.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] Starting health check" >> "$LOG_FILE"

# Funkcja do sprawdzania statusu serwisu
check_service() {
    local service_name=$1
    local service_port=$2
    
    if systemctl is-active --quiet "$service_name"; then
        echo "[$DATE] $service_name: RUNNING" >> "$LOG_FILE"
        
        # Sprawdź czy port jest dostępny
        if [ -n "$service_port" ]; then
            if netstat -tln | grep -q ":$service_port "; then
                echo "[$DATE] $service_name port $service_port: LISTENING" >> "$LOG_FILE"
            else
                echo "[$DATE] $service_name port $service_port: NOT LISTENING" >> "$LOG_FILE"
            fi
        fi
    else
        echo "[$DATE] $service_name: STOPPED" >> "$LOG_FILE"
    fi
}

# Sprawdź serwisy
check_service "promtail" "{{ promtail_port }}"

{% if node_exporter_enabled | default(true) %}
check_service "node-exporter" "{{ node_exporter_port }}"
{% endif %}

{% if postgres_exporter_enabled | default(false) %}
check_service "postgres-exporter" "{{ postgres_exporter_port }}"
{% endif %}

{% if redis_exporter_enabled | default(false) %}
check_service "redis-exporter" "{{ redis_exporter_port }}"
{% endif %}

{% if nginx_exporter_enabled | default(false) %}
check_service "nginx-exporter" "{{ nginx_exporter_port }}"
{% endif %}

# Sprawdź połączenie z Loki
if curl -s "{{ loki_url }}/ready" > /dev/null 2>&1; then
    echo "[$DATE] Loki connection: OK" >> "$LOG_FILE"
else
    echo "[$DATE] Loki connection: FAILED" >> "$LOG_FILE"
fi

# Sprawdź użycie dysku
DISK_USAGE=$(df {{ logging_agent_home }} | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "[$DATE] Disk usage warning: ${DISK_USAGE}%" >> "$LOG_FILE"
fi

echo "[$DATE] Health check completed" >> "$LOG_FILE"

# Ograniczenie rozmiaru logu do 1000 linii
tail -n 1000 "$LOG_FILE" > "${LOG_FILE}.tmp" && mv "${LOG_FILE}.tmp" "$LOG_FILE"