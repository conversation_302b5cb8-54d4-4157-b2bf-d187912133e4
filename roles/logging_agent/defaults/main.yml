---
# <PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> dla roli logging_agent
# Agent logowania: Promtail, Node Exporter, inne eksportery

# =============================================================================
# KONFIGURACJA PODSTAWOWA
# =============================================================================
logging_agent_user: "logging-agent"
logging_agent_group: "logging-agent"
logging_agent_home: "/opt/logging-agent"
logging_agent_config_dir: "{{ logging_agent_home }}/config"
logging_agent_data_dir: "{{ logging_agent_home }}/data"

# =============================================================================
# KONFIGURACJA CENTRALNEGO SERWERA LOGOWANIA
# =============================================================================
logging_central_server: "{{ groups['logging_central'][0] | default('127.0.0.1') }}"
loki_url: "http://{{ logging_central_server }}:{{ loki_port | default(3100) }}"
prometheus_url: "http://{{ logging_central_server }}:{{ prometheus_port | default(9090) }}"

# =============================================================================
# KONFIGURACJA PORTÓW
# =============================================================================
promtail_port: 9080
node_exporter_port: 9100
postgres_exporter_port: 9187
redis_exporter_port: 9121
nginx_exporter_port: 9113
docker_exporter_port: 9323

# =============================================================================
# KONFIGURACJA KONTENERÓW (ARM64 compatible)
# =============================================================================
promtail_image: "grafana/promtail:latest"
node_exporter_image: "prom/node-exporter:latest"
postgres_exporter_image: "prometheuscommunity/postgres-exporter:latest"
redis_exporter_image: "oliver006/redis_exporter:latest"
nginx_exporter_image: "nginx/nginx-prometheus-exporter:latest"

# =============================================================================
# KONFIGURACJA PROMTAIL
# =============================================================================
promtail_server_http_listen_port: "{{ promtail_port }}"
promtail_server_grpc_listen_port: 9095
promtail_positions_file: "{{ logging_agent_data_dir }}/promtail/positions.yaml"
promtail_client_url: "{{ loki_url }}/loki/api/v1/push"

# Konfiguracja logów do zbierania
promtail_scrape_configs:
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*.log
  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          __path__: /var/lib/docker/containers/*/*.log
  - job_name: baserow
    static_configs:
      - targets:
          - localhost
        labels:
          job: baserow
          __path__: "{{ baserow_log_dir | default('/opt/baserow/logs') }}/*.log"
  - job_name: nginx
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          __path__: /var/log/nginx/*.log
  - job_name: auth
    static_configs:
      - targets:
          - localhost
        labels:
          job: auth
          __path__: /var/log/auth.log
  - job_name: syslog
    static_configs:
      - targets:
          - localhost
        labels:
          job: syslog
          __path__: /var/log/syslog

# =============================================================================
# KONFIGURACJA NODE EXPORTER
# =============================================================================
node_exporter_enabled: true
node_exporter_web_listen_address: "0.0.0.0:{{ node_exporter_port }}"
node_exporter_collectors:
  - systemd
  - textfile
  - filesystem
  - diskstats
  - netdev
  - meminfo
  - loadavg
  - cpu
  - time
  - boottime
  - hwmon
  - thermal_zone

# Tekstowe metryki
node_exporter_textfile_dir: "{{ logging_agent_data_dir }}/node_exporter"

# =============================================================================
# KONFIGURACJA POSTGRES EXPORTER
# =============================================================================
postgres_exporter_enabled: true
postgres_exporter_web_listen_address: "0.0.0.0:{{ postgres_exporter_port }}"
postgres_exporter_data_source_name: "postgresql://{{ baserow_db_user | default('baserow') }}:{{ baserow_db_password | default('password') }}@localhost:5432/{{ baserow_db_name | default('baserow') }}?sslmode=disable"

# =============================================================================
# KONFIGURACJA REDIS EXPORTER
# =============================================================================
redis_exporter_enabled: true
redis_exporter_web_listen_address: "0.0.0.0:{{ redis_exporter_port }}"
redis_exporter_redis_addr: "redis://localhost:6379"

# =============================================================================
# KONFIGURACJA NGINX EXPORTER
# =============================================================================
nginx_exporter_enabled: true
nginx_exporter_web_listen_address: "0.0.0.0:{{ nginx_exporter_port }}"
nginx_exporter_nginx_plus: false
nginx_exporter_nginx_plus_api_url: "http://localhost:8080/api"
nginx_exporter_scrape_uri: "http://localhost:8080/stub_status"

# =============================================================================
# KONFIGURACJA DOCKER EXPORTER
# =============================================================================
docker_exporter_enabled: true
docker_exporter_web_listen_address: "0.0.0.0:{{ docker_exporter_port }}"

# =============================================================================
# KONFIGURACJA FIREWALL
# =============================================================================
logging_agent_firewall_ports:
  - "{{ promtail_port }}/tcp"
  - "{{ node_exporter_port }}/tcp"
  - "{{ postgres_exporter_port }}/tcp"
  - "{{ redis_exporter_port }}/tcp"
  - "{{ nginx_exporter_port }}/tcp"
  - "{{ docker_exporter_port }}/tcp"

# =============================================================================
# KONFIGURACJA SYSTEMD
# =============================================================================
systemd_services:
  - name: promtail
    enabled: true
  - name: node-exporter
    enabled: "{{ node_exporter_enabled }}"
  - name: postgres-exporter
    enabled: "{{ postgres_exporter_enabled }}"
  - name: redis-exporter
    enabled: "{{ redis_exporter_enabled }}"
  - name: nginx-exporter
    enabled: "{{ nginx_exporter_enabled }}"

# =============================================================================
# OPTYMALIZACJA ARM64
# =============================================================================
# Limity pamięci dla ARM64 (4GB RAM)
promtail_memory_limit: "128m"
node_exporter_memory_limit: "64m"
postgres_exporter_memory_limit: "64m"
redis_exporter_memory_limit: "32m"
nginx_exporter_memory_limit: "32m"

# =============================================================================
# MONITORING I HEALTH CHECKS
# =============================================================================
logging_agent_health_check_interval: "30s"
logging_agent_health_check_timeout: "10s"
logging_agent_health_check_retries: 3

# =============================================================================
# KONFIGURACJA LOGROTATE
# =============================================================================
logrotate_enabled: true
logrotate_rules:
  - name: promtail
    path: "{{ logging_agent_data_dir }}/promtail/*.log"
    options:
      - daily
      - rotate 7
      - compress
      - delaycompress
      - missingok
      - notifempty
      - create 0644 {{ logging_agent_user }} {{ logging_agent_group }}

# =============================================================================
# KONFIGURACJA RESTART POLICY
# =============================================================================
restart_policy: "unless-stopped"
restart_on_failure: true
restart_sec: "30s"