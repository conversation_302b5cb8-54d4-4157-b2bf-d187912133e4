---
# defaults file for backup_setup role

# Konfiguracja użytkownika
baserow_user: baserow
baserow_group: baserow
baserow_home: /opt/baserow

# Konfiguracja bazy danych
baserow_db_name: baserow
baserow_db_user: baserow

# Konfiguracja backupów
backup_retention_days: 7
backup_cron_enabled: true
backup_cron_hour: 2
backup_cron_minute: 0

# Konfiguracja szyfrowania backupów GPG
backup_encryption_enabled: false
backup_gpg_keyring_path: "{{ baserow_home }}/.gnupg"
backup_gpg_recipient: "backup@{{ baserow_domain | default('localhost') }}"
backup_gpg_key_type: "RSA"
backup_gpg_key_length: 2048
backup_gpg_key_expire: "0"

# Konfiguracja weryfikacji backupów
backup_verification_enabled: true
backup_checksum_algorithm: "sha256"
backup_checksum_enabled: true
