#!/bin/bash
# Skrypt restore backup Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_DIR="{{ baserow_home }}/backups"
DOCKER_COMPOSE_FILE="{{ baserow_home }}/docker-compose.yml"
CONTAINER_NAME="baserow-postgres"
DB_NAME="{{ baserow_db_name }}"
DB_USER="{{ baserow_db_user }}"
LOG_FILE="{{ baserow_home }}/backups/logs/restore.log"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

error_exit() {
    log "❌ BŁĄD: $1"
    exit 1
}

show_help() {
    cat << EOF
Skrypt restore backup Baserow

Użycie:
    $0 --database <backup_file>     Przywróć backup bazy danych
    $0 --media <backup_file>        Przywróć backup mediów
    $0 --list                       Pokaż dostępne backupy
    $0 --help                       <PERSON><PERSON><PERSON> tę pomoc

Przykłady:
    $0 --database baserow_db_20231201_120000.sql.gz
    $0 --media baserow_media_20231201_120000.tar.gz
    $0 --list

UWAGA: Restore zatrzyma aplikację Baserow na czas przywracania!
EOF
}

list_backups() {
    log "📋 Dostępne backupy:"
    echo ""
    echo "=== BACKUPY BAZY DANYCH ==="
    if ls "$BACKUP_DIR/database/"baserow_db_*.sql.gz 2>/dev/null; then
        for backup in "$BACKUP_DIR/database/"baserow_db_*.sql.gz; do
            if [[ -f "$backup" ]]; then
                SIZE=$(du -h "$backup" | cut -f1)
                DATE=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
                echo "  $(basename "$backup") - $SIZE - $DATE"
            fi
        done
    else
        echo "  Brak backupów bazy danych"
    fi
    
    echo ""
    echo "=== BACKUPY MEDIÓW ==="
    if ls "$BACKUP_DIR/media/"baserow_media_*.tar.gz 2>/dev/null; then
        for backup in "$BACKUP_DIR/media/"baserow_media_*.tar.gz; do
            if [[ -f "$backup" ]]; then
                SIZE=$(du -h "$backup" | cut -f1)
                DATE=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
                echo "  $(basename "$backup") - $SIZE - $DATE"
            fi
        done
    else
        echo "  Brak backupów mediów"
    fi
}

restore_database() {
    local backup_file="$1"
    local full_path="$BACKUP_DIR/database/$backup_file"
    
    if [[ ! -f "$full_path" ]]; then
        error_exit "Plik backup nie istnieje: $full_path"
    fi
    
    log "🚀 Rozpoczęcie restore bazy danych z: $backup_file"
    
    # Zatrzymaj aplikację
    log "⏹️ Zatrzymywanie aplikacji Baserow..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" stop backend frontend || true
    
    # Sprawdź czy kontener bazy danych działa
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME.*Up"; then
        log "🚀 Uruchamianie kontenera bazy danych..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d postgres
        sleep 10
    fi
    
    # Przywróć bazę danych
    log "📦 Przywracanie bazy danych..."
    if zcat "$full_path" | docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres psql -U "$DB_USER" -d "$DB_NAME"; then
        log "✅ Baza danych przywrócona pomyślnie"
    else
        error_exit "Nie udało się przywrócić bazy danych"
    fi
    
    # Uruchom aplikację
    log "🚀 Uruchamianie aplikacji Baserow..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log "✅ Restore bazy danych zakończony pomyślnie"
}

restore_media() {
    local backup_file="$1"
    local full_path="$BACKUP_DIR/media/$backup_file"
    local media_dir="{{ baserow_home }}/volumes/baserow_data"
    
    if [[ ! -f "$full_path" ]]; then
        error_exit "Plik backup nie istnieje: $full_path"
    fi
    
    log "🚀 Rozpoczęcie restore mediów z: $backup_file"
    
    # Zatrzymaj aplikację
    log "⏹️ Zatrzymywanie aplikacji Baserow..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" stop backend frontend || true
    
    # Utwórz backup aktualnych mediów
    if [[ -d "$media_dir" ]]; then
        log "💾 Tworzenie backup aktualnych mediów..."
        tar -czf "$BACKUP_DIR/media/current_media_backup_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$(dirname "$media_dir")" "$(basename "$media_dir")" || true
    fi
    
    # Przywróć media
    log "📦 Przywracanie mediów..."
    if tar -xzf "$full_path" -C "$(dirname "$media_dir")"; then
        log "✅ Media przywrócone pomyślnie"
    else
        error_exit "Nie udało się przywrócić mediów"
    fi
    
    # Uruchom aplikację
    log "🚀 Uruchamianie aplikacji Baserow..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log "✅ Restore mediów zakończony pomyślnie"
}

# Główna logika
case "${1:-}" in
    --help)
        show_help
        ;;
    --list)
        list_backups
        ;;
    --database)
        if [[ -z "${2:-}" ]]; then
            error_exit "Nie podano nazwy pliku backup bazy danych"
        fi
        restore_database "$2"
        ;;
    --media)
        if [[ -z "${2:-}" ]]; then
            error_exit "Nie podano nazwy pliku backup mediów"
        fi
        restore_media "$2"
        ;;
    *)
        echo "Nieprawidłowe argumenty. Użyj --help aby zobaczyć pomoc."
        exit 1
        ;;
esac
