#!/bin/bash
# Skrypt pełnego backup Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_BASE_DIR="{{ baserow_home }}/backups"
LOG_FILE="{{ baserow_home }}/backups/logs/backup.log"
SCRIPTS_DIR="{{ baserow_home }}/backups/scripts"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

error_exit() {
    log "❌ BŁĄD: $1"
    exit 1
}

# Sprawdź argumenty
if [[ "${1:-}" == "--test" ]]; then
    log "🧪 TEST MODE: Sprawdzanie konfiguracji pełnego backup"
    
    # Test backup bazy danych
    if [[ -x "$SCRIPTS_DIR/backup_database.sh" ]]; then
        log "🧪 Testowanie backup bazy danych..."
        if "$SCRIPTS_DIR/backup_database.sh" --test; then
            log "✅ Test backup bazy danych: OK"
        else
            error_exit "Test backup bazy danych nie powiódł się"
        fi
    else
        error_exit "Skrypt backup bazy danych nie istnieje lub nie jest wykonywalny"
    fi
    
    # Test backup mediów
    if [[ -x "$SCRIPTS_DIR/backup_media.sh" ]]; then
        log "🧪 Testowanie backup mediów..."
        if "$SCRIPTS_DIR/backup_media.sh" --test; then
            log "✅ Test backup mediów: OK"
        else
            error_exit "Test backup mediów nie powiódł się"
        fi
    else
        error_exit "Skrypt backup mediów nie istnieje lub nie jest wykonywalny"
    fi
    
    log "✅ Test pełnego backup zakończony pomyślnie"
    exit 0
fi

# Główna logika pełnego backup
log "🚀 Rozpoczęcie pełnego backup Baserow"
START_TIME=$(date +%s)

# Sprawdź czy katalogi istnieją
mkdir -p "$BACKUP_BASE_DIR/logs"

# Wykonaj backup bazy danych
log "📦 Wykonywanie backup bazy danych..."
if [[ -x "$SCRIPTS_DIR/backup_database.sh" ]]; then
    if "$SCRIPTS_DIR/backup_database.sh"; then
        log "✅ Backup bazy danych zakończony pomyślnie"
    else
        error_exit "Backup bazy danych nie powiódł się"
    fi
else
    error_exit "Skrypt backup bazy danych nie istnieje"
fi

# Wykonaj backup mediów
log "📦 Wykonywanie backup mediów..."
if [[ -x "$SCRIPTS_DIR/backup_media.sh" ]]; then
    if "$SCRIPTS_DIR/backup_media.sh"; then
        log "✅ Backup mediów zakończony pomyślnie"
    else
        log "⚠️ Backup mediów nie powiódł się, ale kontynuujemy"
    fi
else
    log "⚠️ Skrypt backup mediów nie istnieje"
fi

# Oblicz czas wykonania
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
DURATION_MIN=$((DURATION / 60))
DURATION_SEC=$((DURATION % 60))

# Sprawdź rozmiary backupów
DB_BACKUP_SIZE=$(du -sh "$BACKUP_BASE_DIR/database" 2>/dev/null | cut -f1 || echo "N/A")
MEDIA_BACKUP_SIZE=$(du -sh "$BACKUP_BASE_DIR/media" 2>/dev/null | cut -f1 || echo "N/A")
TOTAL_BACKUP_SIZE=$(du -sh "$BACKUP_BASE_DIR" 2>/dev/null | cut -f1 || echo "N/A")

# Podsumowanie
log "✅ Pełny backup Baserow zakończony pomyślnie"
log "⏱️ Czas wykonania: ${DURATION_MIN}m ${DURATION_SEC}s"
log "📊 Rozmiary backupów:"
log "   - Baza danych: $DB_BACKUP_SIZE"
log "   - Media: $MEDIA_BACKUP_SIZE"
log "   - Łącznie: $TOTAL_BACKUP_SIZE"
log "📁 Lokalizacja: $BACKUP_BASE_DIR"

# Sprawdź dostępne miejsce na dysku
AVAILABLE_SPACE=$(df -h "$BACKUP_BASE_DIR" | awk 'NR==2 {print $4}')
log "💾 Dostępne miejsce na dysku: $AVAILABLE_SPACE"
