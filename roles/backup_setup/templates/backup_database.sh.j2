#!/bin/bash
# Skrypt backup bazy danych Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_DIR="{{ baserow_home }}/backups/database"
DOCKER_COMPOSE_FILE="{{ baserow_home }}/docker-compose.yml"
CONTAINER_NAME="baserow-postgres"
DB_NAME="{{ baserow_db_name }}"
DB_USER="{{ baserow_db_user }}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="baserow_db_${TIMESTAMP}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"
LOG_FILE="{{ baserow_home }}/backups/logs/backup.log"
RETENTION_DAYS="{{ backup_retention_days }}"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

error_exit() {
    log "❌ BŁĄD: $1"
    exit 1
}

# Sprawdź argumenty
if [[ "${1:-}" == "--test" ]]; then
    log "🧪 TEST MODE: Sprawdzanie konfiguracji backup bazy danych"
    
    # Sprawdź czy Docker Compose działa
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME"; then
        error_exit "Kontener $CONTAINER_NAME nie jest uruchomiony"
    fi
    
    # Sprawdź połączenie z bazą danych
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
        log "✅ Połączenie z bazą danych: OK"
    else
        error_exit "Nie można połączyć się z bazą danych"
    fi
    
    log "✅ Test zakończony pomyślnie"
    exit 0
fi

# Główna logika backup
log "🚀 Rozpoczęcie backup bazy danych Baserow"

# Sprawdź czy katalog backup istnieje
mkdir -p "$BACKUP_DIR"

# Sprawdź czy kontener działa
if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "$CONTAINER_NAME.*Up"; then
    error_exit "Kontener $CONTAINER_NAME nie jest uruchomiony"
fi

# Wykonaj backup bazy danych
log "📦 Tworzenie backup bazy danych..."
if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U "$DB_USER" -d "$DB_NAME" --no-password > "$BACKUP_DIR/$BACKUP_FILE"; then
    log "✅ Backup bazy danych utworzony: $BACKUP_FILE"
else
    error_exit "Nie udało się utworzyć backup bazy danych"
fi

# Kompresuj backup
log "🗜️ Kompresowanie backup..."
if gzip "$BACKUP_DIR/$BACKUP_FILE"; then
    log "✅ Backup skompresowany: $COMPRESSED_FILE"
else
    error_exit "Nie udało się skompresować backup"
fi

# Sprawdź rozmiar backup
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$COMPRESSED_FILE" | cut -f1)
log "📊 Rozmiar backup: $BACKUP_SIZE"

# Usuń stare backupy
log "🧹 Usuwanie starych backupów (starszych niż $RETENTION_DAYS dni)..."
find "$BACKUP_DIR" -name "baserow_db_*.sql.gz" -mtime +$RETENTION_DAYS -delete
REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "baserow_db_*.sql.gz" | wc -l)
log "📁 Pozostałe backupy: $REMAINING_BACKUPS"

log "✅ Backup bazy danych zakończony pomyślnie"
log "📁 Lokalizacja: $BACKUP_DIR/$COMPRESSED_FILE"
log "📊 Rozmiar: $BACKUP_SIZE"
