#!/bin/bash
# Skrypt backup mediów Baserow
# Generowany przez Ansible - nie edytuj ręcznie

set -euo pipefail

# Konfiguracja
BACKUP_DIR="{{ baserow_home }}/backups/media"
MEDIA_DIR="{{ baserow_home }}/volumes/baserow_data"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="baserow_media_${TIMESTAMP}.tar.gz"
LOG_FILE="{{ baserow_home }}/backups/logs/backup.log"
RETENTION_DAYS="{{ backup_retention_days }}"

# Funkcje pomocnicze
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

error_exit() {
    log "❌ BŁĄD: $1"
    exit 1
}

# Sprawdź argumenty
if [[ "${1:-}" == "--test" ]]; then
    log "🧪 TEST MODE: Sprawdzanie konfiguracji backup mediów"
    
    # Sprawdź czy katalog mediów istnieje
    if [[ ! -d "$MEDIA_DIR" ]]; then
        error_exit "Katalog mediów nie istnieje: $MEDIA_DIR"
    fi
    
    # Sprawdź uprawnienia
    if [[ ! -r "$MEDIA_DIR" ]]; then
        error_exit "Brak uprawnień do odczytu katalogu mediów: $MEDIA_DIR"
    fi
    
    log "✅ Test zakończony pomyślnie"
    exit 0
fi

# Główna logika backup
log "🚀 Rozpoczęcie backup mediów Baserow"

# Sprawdź czy katalog backup istnieje
mkdir -p "$BACKUP_DIR"

# Sprawdź czy katalog mediów istnieje
if [[ ! -d "$MEDIA_DIR" ]]; then
    error_exit "Katalog mediów nie istnieje: $MEDIA_DIR"
fi

# Sprawdź czy są jakieś pliki do backup
MEDIA_COUNT=$(find "$MEDIA_DIR" -type f 2>/dev/null | wc -l)
log "📁 Znaleziono plików mediów: $MEDIA_COUNT"

if [[ $MEDIA_COUNT -eq 0 ]]; then
    log "⚠️ Brak plików mediów do backup"
    exit 0
fi

# Wykonaj backup mediów
log "📦 Tworzenie backup mediów..."
if tar -czf "$BACKUP_DIR/$BACKUP_FILE" -C "$(dirname "$MEDIA_DIR")" "$(basename "$MEDIA_DIR")" 2>/dev/null; then
    log "✅ Backup mediów utworzony: $BACKUP_FILE"
else
    error_exit "Nie udało się utworzyć backup mediów"
fi

# Sprawdź rozmiar backup
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
log "📊 Rozmiar backup: $BACKUP_SIZE"

# Usuń stare backupy
log "🧹 Usuwanie starych backupów (starszych niż $RETENTION_DAYS dni)..."
find "$BACKUP_DIR" -name "baserow_media_*.tar.gz" -mtime +$RETENTION_DAYS -delete
REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "baserow_media_*.tar.gz" | wc -l)
log "📁 Pozostałe backupy: $REMAINING_BACKUPS"

log "✅ Backup mediów zakończony pomyślnie"
log "📁 Lokalizacja: $BACKUP_DIR/$BACKUP_FILE"
log "📊 Rozmiar: $BACKUP_SIZE"
