---
# Konfigu<PERSON><PERSON> backupów dla Baserow

- name: "Rozpocz<PERSON>cie konfiguracji backupów"
  debug:
    msg:
      - "=== KONFIGURACJA BACKUPÓW ==="
      - "Serwer: {{ ansible_hostname }}"
      - "Katalog: {{ baserow_home }}/backups"
      - "Szyfrowanie: {{ backup_encryption_enabled }}"
      - "Cron: {{ backup_cron_enabled }}"
  tags: [backup, info]

# =============================================================================
# STRUKTURA KATALOGÓW
# =============================================================================

- name: "<PERSON><PERSON><PERSON><PERSON> katalog dla backupów"
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  loop:
    - "{{ baserow_home }}/backups"
    - "{{ baserow_home }}/backups/database"
    - "{{ baserow_home }}/backups/media"
    - "{{ baserow_home }}/backups/scripts"
    - "{{ baserow_home }}/backups/logs"
  become: true
  tags: [backup, directories]

# =============================================================================
# INSTALACJA NARZĘDZI
# =============================================================================

- name: "Zainstaluj narzędzia do backupów"
  apt:
    name:
      - postgresql-client
      - rsync
      - gzip
      - tar
    state: present
  become: true
  tags: [backup, packages]

- name: "Zainstaluj GPG dla szyfrowania backupów"
  package:
    name: gnupg
    state: present
  become: true
  when: backup_encryption_enabled
  tags: [backup, packages]

# =============================================================================
# SKRYPTY BACKUP
# =============================================================================

- name: "Stwórz skrypt backup bazy danych"
  template:
    src: backup_database.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/backup_database.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  tags: [backup, scripts]

- name: "Stwórz skrypt backup mediów"
  template:
    src: backup_media.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/backup_media.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  tags: [backup, scripts]

- name: "Stwórz skrypt pełnego backup"
  template:
    src: full_backup.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/full_backup.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  tags: [backup, scripts]

- name: "Stwórz skrypt restore"
  template:
    src: restore_backup.sh.j2
    dest: "{{ baserow_home }}/backups/scripts/restore_backup.sh"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  tags: [backup, scripts]

# =============================================================================
# KONFIGURACJA LOGROTATE
# =============================================================================

- name: "Stwórz konfigurację logrotate dla backupów"
  copy:
    content: |
      # Logrotate dla backupów Baserow
      {{ baserow_home }}/backups/logs/*.log {
          daily
          rotate 7
          compress
          delaycompress
          missingok
          notifempty
          create 0644 {{ baserow_user }} {{ baserow_group }}
      }
    dest: /etc/logrotate.d/baserow-backup
    mode: '0644'
  become: true
  tags: [backup, logrotate]

# =============================================================================
# TEST SKRYPTÓW
# =============================================================================

- name: "Test skryptu backup bazy danych"
  shell: |
    cd {{ baserow_home }}/backups/scripts
    ./backup_database.sh --test
  become: true
  become_user: "{{ baserow_user }}"
  register: backup_test
  changed_when: false
  ignore_errors: true
  tags: [backup, test]

- name: "Wyświetl wynik testu backup"
  debug:
    msg:
      - "=== TEST SKRYPTU BACKUP ===" 
      - "{{ backup_test.stdout_lines if backup_test.stdout_lines is defined else ['Test nie został wykonany'] }}"
  tags: [backup, info]

# =============================================================================
# KONFIGURACJA CRON
# =============================================================================

- name: "Dodaj cron job dla codziennego backup"
  cron:
    name: "Baserow daily backup"
    minute: "{{ backup_cron_minute }}"
    hour: "{{ backup_cron_hour }}"
    job: "{{ baserow_home }}/backups/scripts/full_backup.sh >> {{ baserow_home }}/backups/logs/backup.log 2>&1"
    user: "{{ baserow_user }}"
  become: true
  when: backup_cron_enabled
  tags: [backup, cron]

# =============================================================================
# PODSUMOWANIE
# =============================================================================

- name: "Podsumowanie konfiguracji backupów"
  debug:
    msg:
      - "=== KONFIGURACJA BACKUPÓW ZAKOŃCZONA ==="
      - ""
      - "📁 KATALOGI:"
      - "  Główny: {{ baserow_home }}/backups"
      - "  Baza danych: {{ baserow_home }}/backups/database"
      - "  Media: {{ baserow_home }}/backups/media"
      - "  Skrypty: {{ baserow_home }}/backups/scripts"
      - "  Logi: {{ baserow_home }}/backups/logs"
      - ""
      - "📜 SKRYPTY:"
      - "  Backup bazy: {{ baserow_home }}/backups/scripts/backup_database.sh"
      - "  Backup media: {{ baserow_home }}/backups/scripts/backup_media.sh"
      - "  Pełny backup: {{ baserow_home }}/backups/scripts/full_backup.sh"
      - "  Restore: {{ baserow_home }}/backups/scripts/restore_backup.sh"
      - ""
      - "⏰ HARMONOGRAM:"
      - "  Automatyczny backup: {{ 'Tak' if backup_cron_enabled else 'Nie' }}"
      - "  Czas: {{ backup_cron_hour }}:{{ backup_cron_minute }} codziennie"
      - ""
      - "🔐 BEZPIECZEŃSTWO:"
      - "  Szyfrowanie GPG: {{ 'Włączone' if backup_encryption_enabled else 'Wyłączone' }}"
      - "  Retencja: {{ backup_retention_days }} dni"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Pełny backup: {{ baserow_home }}/backups/scripts/full_backup.sh"
      - "  Restore: {{ baserow_home }}/backups/scripts/restore_backup.sh --help"
      - "  Logi: tail -f {{ baserow_home }}/backups/logs/backup.log"
  tags: [backup, info]
