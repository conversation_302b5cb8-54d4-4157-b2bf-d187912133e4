---
# defaults file for nginx_setup role

# Konfiguracja Baserow
baserow_user: baserow
baserow_group: baserow
baserow_home: /opt/baserow
baserow_domain: baserow.simetria.pl
baserow_alternative_domains: ["www.baserow.simetria.pl"]

# Konfiguracja Nginx
nginx_client_max_body_size: "100M"
nginx_proxy_connect_timeout: "30s"
nginx_proxy_read_timeout: "30s"

# Konfiguracja HSTS
nginx_hsts_enabled: true
nginx_hsts_max_age: "63072000"
nginx_hsts_include_subdomains: true
nginx_hsts_preload: true

# Konfiguracja CSP
nginx_csp_enabled: true
nginx_csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:; media-src 'self'; object-src 'none'; frame-src 'none'; base-uri 'self'; form-action 'self'"
nginx_csp_api_policy: "default-src 'none'; connect-src 'self'"

# Konfiguracja Permissions Policy
nginx_permissions_policy_enabled: true
nginx_permissions_policy: "camera=(), microphone=(), geolocation=(), gyroscope=(), magnetometer=(), payment=(), usb=(), fullscreen=(self), display-capture=()"

# Konfiguracja Cross-Origin Policy
nginx_cross_origin_policy_enabled: true
nginx_cross_origin_embedder_policy: "require-corp"
nginx_cross_origin_opener_policy: "same-origin"
nginx_cross_origin_resource_policy: "same-origin"

# Dodatkowe nagłówki bezpieczeństwa
nginx_security_headers_enabled: true
nginx_x_permitted_cross_domain_policies: "none"

# Konfiguracja Clear Site Data
nginx_clear_site_data_enabled: false
nginx_clear_site_data_policy: "cache, cookies, storage, executionContexts"

# Konfiguracja plików statycznych
nginx_static_files_security_enabled: true
nginx_static_files_referrer_policy: "strict-origin-when-cross-origin"
nginx_static_files_cache_control: "public, max-age=31536000, immutable"
