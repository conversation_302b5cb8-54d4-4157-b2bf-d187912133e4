---
# Konfi<PERSON><PERSON><PERSON> Nginx jako reverse proxy dla Baserow

- name: "Rozpoczęcie konfiguracji Nginx"
  debug:
    msg:
      - "=== KONFIGURACJA NGINX ==="
      - "Serwer: {{ ansible_hostname }}"
      - "Domena: {{ baserow_domain }}"
      - "Domeny alternatywne: {{ baserow_alternative_domains }}"
  tags: [nginx, info]

# =============================================================================
# INSTALACJA NGINX
# =============================================================================

- name: "Zainstaluj Nginx"
  apt:
    name: nginx
    state: present
    update_cache: true
  become: true
  tags: [nginx, install]

- name: "Zatrz<PERSON>aj domyślny Nginx (jeśli d<PERSON>ła)"
  service:
    name: nginx
    state: stopped
  become: true
  ignore_errors: true
  tags: [nginx, stop]

# =============================================================================
# CZYSZCZENIE DOMYŚLNEJ KONFIGURACJI
# =============================================================================

- name: "Usuń domyślną konfigurację Nginx"
  file:
    path: "{{ item }}"
    state: absent
  become: true
  loop:
    - /etc/nginx/sites-enabled/default
    - /etc/nginx/sites-available/default
    - /var/www/html
  tags: [nginx, cleanup]

# =============================================================================
# STRUKTURA KATALOGÓW
# =============================================================================

- name: "Stwórz strukturę katalogów Nginx"
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  loop:
    - "{{ baserow_home }}/nginx"
    - "{{ baserow_home }}/nginx/conf.d"
    - "{{ baserow_home }}/logs/nginx"
    - "{{ baserow_home }}/ssl"
    - "/etc/nginx/ssl"
  tags: [nginx, directories]

# =============================================================================
# KONFIGURACJA SSL (SELF-SIGNED CERTIFICATES)
# =============================================================================

- name: "Sprawdź czy certyfikat SSL już istnieje"
  stat:
    path: "{{ baserow_home }}/ssl/{{ baserow_domain }}.crt"
  register: ssl_cert_check
  tags: [nginx, ssl]

- name: "Generuj self-signed SSL certificate (tymczasowy)"
  shell: |
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
      -keyout {{ baserow_home }}/ssl/{{ baserow_domain }}.key \
      -out {{ baserow_home }}/ssl/{{ baserow_domain }}.crt \
      -subj "/C=PL/ST=Mazowieckie/L=Warsaw/O=Simetria/OU=IT/CN={{ baserow_domain }}"
  become: true
  when: not ssl_cert_check.stat.exists
  tags: [nginx, ssl]

- name: "Skopiuj certyfikaty SSL do katalogu systemowego"
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    remote_src: true
    owner: root
    group: root
    mode: "{{ item.mode }}"
  become: true
  loop:
    - { src: "{{ baserow_home }}/ssl/{{ baserow_domain }}.crt", dest: "/etc/nginx/ssl/{{ baserow_domain }}.crt", mode: "0644" }
    - { src: "{{ baserow_home }}/ssl/{{ baserow_domain }}.key", dest: "/etc/nginx/ssl/{{ baserow_domain }}.key", mode: "0600" }
  tags: [nginx, ssl]

# =============================================================================
# GŁÓWNA KONFIGURACJA NGINX
# =============================================================================

- name: "Stwórz główną konfigurację Nginx z template"
  template:
    src: nginx.conf.j2
    dest: "{{ baserow_home }}/nginx/nginx.conf"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0644'
  become: true
  notify: reload nginx
  tags: [nginx, config]

- name: "Zastąp główną konfigurację nginx systemową"
  copy:
    src: "{{ baserow_home }}/nginx/nginx.conf"
    dest: "/etc/nginx/nginx.conf"
    remote_src: true
    owner: root
    group: root
    mode: '0644'
    backup: true
  become: true
  notify: reload nginx
  tags: [nginx, config]

# =============================================================================
# WERYFIKACJA I URUCHOMIENIE
# =============================================================================

- name: "Sprawdź składnię konfiguracji Nginx"
  shell: nginx -t
  become: true
  register: nginx_syntax_check
  changed_when: false
  tags: [nginx, verify]

- name: "Wyświetl wynik sprawdzenia składni"
  debug:
    msg: "Składnia Nginx: {{ 'OK' if nginx_syntax_check.rc == 0 else 'BŁĄD' }}"
  tags: [nginx, info]

- name: "Uruchom i włącz Nginx"
  service:
    name: nginx
    state: started
    enabled: true
  become: true
  tags: [nginx, start]

- name: "Sprawdź czy Nginx działa"
  uri:
    url: "http://localhost:80"
    method: GET
    timeout: 10
  register: nginx_health
  ignore_errors: true
  tags: [nginx, health]

# =============================================================================
# KONFIGURACJA LOGROTATE
# =============================================================================

- name: "Skonfiguruj logrotate dla Nginx"
  copy:
    content: |
      {{ baserow_home }}/logs/nginx/*.log {
          daily
          rotate 14
          compress
          delaycompress
          missingok
          notifempty
          create 0644 {{ baserow_user }} {{ baserow_group }}
          postrotate
              systemctl reload nginx > /dev/null 2>&1 || true
          endscript
      }
    dest: /etc/logrotate.d/baserow-nginx
    mode: '0644'
  become: true
  tags: [nginx, logrotate]

# =============================================================================
# PODSUMOWANIE
# =============================================================================

- name: "Podsumowanie konfiguracji Nginx"
  debug:
    msg:
      - "=== KONFIGURACJA NGINX ZAKOŃCZONA ==="
      - ""
      - "🌐 DOMENA: {{ baserow_domain }}"
      - "📁 KATALOG: {{ baserow_home }}/nginx"
      - "📄 KONFIGURACJA: /etc/nginx/nginx.conf"
      - ""
      - "📊 STATUS:"
      - "  Składnia: {{ 'OK' if nginx_syntax_check.rc == 0 else 'BŁĄD' }}"
      - "  HTTP: {{ '✅ OK' if nginx_health.status == 200 else '❌ BŁĄD' }}"
      - ""
      - "🔐 SSL:"
      - "  Certyfikat: {{ baserow_home }}/ssl/{{ baserow_domain }}.crt"
      - "  Klucz: {{ baserow_home }}/ssl/{{ baserow_domain }}.key"
      - "  Status: Self-signed (tymczasowy)"
      - ""
      - "📝 NASTĘPNE KROKI:"
      - "  1. Skonfiguruj Let's Encrypt: sudo certbot --nginx -d {{ baserow_domain }}"
      - "  2. Sprawdź logi: tail -f {{ baserow_home }}/logs/nginx/error.log"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Test: sudo nginx -t"
      - "  Reload: sudo systemctl reload nginx"
      - "  Restart: sudo systemctl restart nginx"
  tags: [nginx, info]
