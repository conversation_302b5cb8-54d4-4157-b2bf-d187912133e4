# Rola Ansible: security_hardening

Ta rola stosuje podstawowe i zaawansowane zabezpieczenia na serwerach z systemem Debian/Ubuntu.

## Główne zadania

- Aktualizuje wszystkie pakiety systemowe.
- Instaluje kluczowe pakiety bezpieczeństwa: `ufw`, `fail2ban`, `unattended-upgrades`.
- Konfiguruje firewall (UFW) z domyślną polityką blokowania ruchu przychodzącego.
- Otwiera port SSH (domyślnie 22/tcp).
- Wzmacnia konfigurację serwera SSH, blokując logowanie roota i logowanie hasłem.
- (<PERSON>) Konfiguruje `sysctl`, `auditd` i `aide`.

## Zależności

Brak.

## Zmienne

Na razie rola nie używa konfigurowalnych zmiennych. W przyszłości zostanie dodana zmienna `ufw_allowed_ports` do definiowania listy otwartych portów.

## P<PERSON><PERSON><PERSON><PERSON>

```yaml
- hosts: serwery
  roles:
     - security_hardening
