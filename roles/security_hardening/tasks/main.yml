#SPDX-License-Identifier: MIT-0
---
# tasks file for security_hardening

- name: Deploy AIDE configuration file
  ansible.builtin.copy:
    src: aide.conf
    dest: /etc/aide.conf
    owner: root
    group: root
    mode: '0600'

- name: Update apt package cache and upgrade packages
  ansible.builtin.apt:
    update_cache: true
    upgrade: dist
    cache_valid_time: 3600

- name: Install essential security packages
  ansible.builtin.apt:
    name:
      - ufw
      - fail2ban
      - unattended-upgrades
    state: present

- name: Set UFW default incoming policy to deny
  ansible.builtin.ufw:
    direction: incoming
    policy: deny

- name: Set UFW default outgoing policy to allow
  ansible.builtin.ufw:
    direction: outgoing
    policy: allow

- name: Allow SSH connections on UFW
  ansible.builtin.ufw:
    rule: allow
    name: OpenSSH

- name: Enable UFW
  ansible.builtin.ufw:
    state: enabled

- name: Harden SSH - Disable root login
  ansible.builtin.lineinfile:
    path: /etc/ssh/sshd_config
    regexp: '^#?PermitRootLogin'
    line: 'PermitRootLogin no'
    state: present
  notify: Restart sshd

- name: Harden SSH - Disable password authentication
  ansible.builtin.lineinfile:
    path: /etc/ssh/sshd_config
    regexp: '^#?PasswordAuthentication'
    line: 'PasswordAuthentication no'
    state: present
  notify: Restart sshd

- name: Harden kernel parameters (sysctl)
  ansible.builtin.copy:
    src: 99-hardening.conf
    dest: /etc/sysctl.d/99-hardening.conf
    owner: root
    group: root
    mode: '0644'
  notify: Reload sysctl

- name: Install intrusion detection tools
  ansible.builtin.apt:
    name:
      - aide
      - auditd
    state: present
  become: true

- name: Create AIDE database directory
  ansible.builtin.file:
    path: /var/lib/aide
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Check if AIDE database already exists
  ansible.builtin.stat:
    path: /var/lib/aide/aide.db
  register: aide_db

- name: Initialize new AIDE database if it does not exist
  ansible.builtin.command:
    cmd: /usr/bin/aide --config /etc/aide.conf --init
  when: not aide_db.stat.exists
  async: 7200
  poll: 15
  register: aide_init_result

- name: Activate the new AIDE database
  ansible.builtin.copy:
    src: /var/lib/aide/aide.db.new
    dest: /var/lib/aide/aide.db
    remote_src: true
    owner: root
    group: root
    mode: '0600'
  when: aide_init_result is defined and aide_init_result.changed

- name: Schedule daily AIDE integrity check
  ansible.builtin.cron:
    name: "AIDE daily integrity check"
    minute: "30"
    hour: "4"
    job: "/usr/bin/aide --config /etc/aide.conf --check"
