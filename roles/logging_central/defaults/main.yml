---
# <PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> dla roli logging_central
# Centralny serwer logowania: Grafana, Loki, Alertmanager

# =============================================================================
# KONFIGURACJA PODSTAWOWA
# =============================================================================
logging_central_user: "logging"
logging_central_group: "logging"
logging_central_home: "/opt/logging"
logging_central_data_dir: "{{ logging_central_home }}/data"
logging_central_config_dir: "{{ logging_central_home }}/config"

# =============================================================================
# KONFIGURACJA PORTÓW
# =============================================================================
grafana_port: 3001
loki_port: 3100
alertmanager_port: 9093
prometheus_port: 9090

# =============================================================================
# KONFIGURACJA KONTENERÓW (ARM64 compatible)
# =============================================================================
grafana_image: "grafana/grafana:latest"
loki_image: "grafana/loki:latest"
alertmanager_image: "prom/alertmanager:latest"
prometheus_image: "prom/prometheus:latest"

# =============================================================================
# KONFIGURACJA GRAFANA
# =============================================================================
grafana_admin_user: "admin"
grafana_admin_password: "{{ vault_grafana_admin_password | default('change_me_secure_password') }}"
grafana_domain: "grafana.{{ baserow_domain | default('localhost') }}"
grafana_root_url: "https://{{ grafana_domain }}"
grafana_database_type: "sqlite3"
grafana_database_path: "/var/lib/grafana/grafana.db"

# Security
grafana_security_admin_user: "{{ grafana_admin_user }}"
grafana_security_admin_password: "{{ grafana_admin_password }}"
grafana_security_secret_key: "{{ vault_grafana_secret_key | default('change_me_secret_key') }}"
grafana_security_disable_gravatar: true
grafana_security_cookie_secure: true
grafana_security_cookie_samesite: "strict"

# Authentication
grafana_auth_anonymous_enabled: false
grafana_auth_basic_enabled: true
grafana_auth_disable_login_form: false

# =============================================================================
# KONFIGURACJA LOKI
# =============================================================================
loki_auth_enabled: false
loki_server_grpc_listen_port: 9095
loki_server_http_listen_port: "{{ loki_port }}"
loki_ingester_lifecycler_ring_replication_factor: 1
loki_schema_config_from: "2020-10-24"
loki_storage_filesystem_directory: "/loki/chunks"
loki_compactor_working_directory: "/loki/compactor"
loki_limits_config_enforce_metric_name: false
loki_limits_config_reject_old_samples: true
loki_limits_config_reject_old_samples_max_age: "168h"
loki_limits_config_retention_period: "744h"  # 31 dni

# =============================================================================
# KONFIGURACJA ALERTMANAGER
# =============================================================================
alertmanager_global_smtp_smarthost: "{{ email_smtp_host | default('localhost:587') }}"
alertmanager_global_smtp_from: "{{ email_from_address | default('alertmanager@localhost') }}"
alertmanager_global_smtp_auth_username: "{{ email_smtp_user | default('') }}"
alertmanager_global_smtp_auth_password: "{{ email_smtp_password | default('') }}"
alertmanager_global_smtp_require_tls: true

# Routing
alertmanager_route_group_by: ['alertname']
alertmanager_route_group_wait: "10s"
alertmanager_route_group_interval: "10s"
alertmanager_route_repeat_interval: "1h"
alertmanager_route_receiver: "web.hook"

# Receivers
alertmanager_notification_email: "{{ backup_notification_email | default('admin@localhost') }}"

# =============================================================================
# KONFIGURACJA PROMETHEUS
# =============================================================================
prometheus_global_scrape_interval: "15s"
prometheus_global_evaluation_interval: "15s"
prometheus_scrape_timeout: "10s"
prometheus_retention_time: "30d"
prometheus_storage_tsdb_path: "/prometheus"
prometheus_storage_tsdb_retention_time: "{{ prometheus_retention_time }}"

# =============================================================================
# KONFIGURACJA FIREWALL
# =============================================================================
logging_firewall_ports:
  - "{{ grafana_port }}/tcp"
  - "{{ loki_port }}/tcp"
  - "{{ alertmanager_port }}/tcp"
  - "{{ prometheus_port }}/tcp"

# =============================================================================
# KONFIGURACJA BACKUP
# =============================================================================
logging_backup_enabled: true
logging_backup_schedule: "0 3 * * *"  # Codziennie o 3:00
logging_backup_retention_days: 7
logging_backup_dir: "{{ logging_central_home }}/backups"

# =============================================================================
# MONITORING I HEALTH CHECKS
# =============================================================================
logging_health_check_interval: "30s"
logging_health_check_timeout: "10s"
logging_health_check_retries: 3

# =============================================================================
# OPTYMALIZACJA ARM64
# =============================================================================
# Limity pamięci dla ARM64 (4GB RAM)
grafana_memory_limit: "256m"
loki_memory_limit: "512m"
alertmanager_memory_limit: "128m"
prometheus_memory_limit: "512m"

# Restart policy
restart_policy: "unless-stopped"