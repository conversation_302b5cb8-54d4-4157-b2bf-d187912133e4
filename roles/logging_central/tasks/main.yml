---
# Główne zadania dla roli logging_central
# In<PERSON><PERSON><PERSON>, <PERSON>, Alertmanager i Prometheus

- name: Utwórz grupę systemową dla logging
  group:
    name: "{{ logging_central_group }}"
    state: present
    system: true

- name: Utwórz użytkownika systemowego dla logging
  user:
    name: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    home: "{{ logging_central_home }}"
    shell: /bin/bash
    system: true
    create_home: true
    state: present

- name: Utwórz katalogi dla logging central
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0755'
  loop:
    - "{{ logging_central_home }}"
    - "{{ logging_central_data_dir }}"
    - "{{ logging_central_config_dir }}"
    - "{{ logging_central_data_dir }}/grafana"
    - "{{ logging_central_data_dir }}/loki"
    - "{{ logging_central_data_dir }}/alertmanager"
    - "{{ logging_central_data_dir }}/prometheus"
    - "{{ logging_backup_dir }}"

- name: <PERSON><PERSON><PERSON> konfiguracj<PERSON>ana
  template:
    src: grafana.ini.j2
    dest: "{{ logging_central_config_dir }}/grafana.ini"
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0644'
  notify:
    - restart logging containers

- name: Generuj konfigurację Loki
  template:
    src: loki-config.yml.j2
    dest: "{{ logging_central_config_dir }}/loki-config.yml"
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0644'
  notify:
    - restart logging containers

- name: Generuj konfigurację Alertmanager
  template:
    src: alertmanager.yml.j2
    dest: "{{ logging_central_config_dir }}/alertmanager.yml"
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0644'
  notify:
    - restart logging containers

- name: Generuj konfigurację Prometheus
  template:
    src: prometheus.yml.j2
    dest: "{{ logging_central_config_dir }}/prometheus.yml"
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0644'
  notify:
    - restart logging containers

- name: Generuj docker-compose.yml dla logging central
  template:
    src: docker-compose.yml.j2
    dest: "{{ logging_central_home }}/docker-compose.yml"
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0644'
  notify:
    - restart logging containers

- name: Sprawdź czy Docker jest zainstalowany
  command: docker --version
  register: docker_check
  ignore_errors: true
  changed_when: false

- name: Sprawdź czy Docker Compose jest zainstalowany
  command: docker-compose --version
  register: docker_compose_check
  ignore_errors: true
  changed_when: false

- name: Zainstaluj Docker jeśli nie jest zainstalowany
  include_role:
    name: baserow_deployment
    tasks_from: install_docker
  when: docker_check.rc != 0

- name: Uruchom kontenery logging central
  community.docker.docker_compose_v2:
    project_src: "{{ logging_central_home }}"
    project_name: "logging_central"
    state: present
    pull: always
  become_user: "{{ logging_central_user }}"
  become: true

- name: Konfiguruj firewall dla logging central
  ufw:
    rule: allow
    port: "{{ item.split('/')[0] }}"
    proto: "{{ item.split('/')[1] }}"
    comment: "Logging Central - {{ item }}"
  loop: "{{ logging_firewall_ports }}"
  when: ansible_distribution == 'Ubuntu'

- name: Dodaj zadanie cron dla backup logging
  cron:
    name: "Backup logging central"
    minute: "{{ logging_backup_schedule.split(' ')[1] }}"
    hour: "{{ logging_backup_schedule.split(' ')[2] }}"
    day: "{{ logging_backup_schedule.split(' ')[3] }}"
    month: "{{ logging_backup_schedule.split(' ')[4] }}"
    weekday: "{{ logging_backup_schedule.split(' ')[5] }}"
    job: "{{ logging_central_home }}/backup_logging.sh"
    user: "{{ logging_central_user }}"
    state: present
  when: logging_backup_enabled | bool

- name: Generuj skrypt backup dla logging
  template:
    src: backup_logging.sh.j2
    dest: "{{ logging_central_home }}/backup_logging.sh"
    owner: "{{ logging_central_user }}"
    group: "{{ logging_central_group }}"
    mode: '0755'
  when: logging_backup_enabled | bool

- name: Sprawdź status kontenerów logging
  docker_container_info:
    name: "{{ item }}"
  loop:
    - "logging_central_grafana_1"
    - "logging_central_loki_1"
    - "logging_central_alertmanager_1"
    - "logging_central_prometheus_1"
  register: container_status
  ignore_errors: true

- name: Wyświetl status kontenerów
  debug:
    msg: "Kontener {{ item.item }} jest {{ 'uruchomiony' if item.exists and item.container.State.Running else 'zatrzymany' }}"
  loop: "{{ container_status.results }}"
  when: item.exists is defined

- name: Sprawdź dostępność serwisów
  wait_for:
    host: "{{ ansible_default_ipv4.address }}"
    port: "{{ item }}"
    timeout: 30
  loop:
    - "{{ grafana_port }}"
    - "{{ loki_port }}"
    - "{{ alertmanager_port }}"
    - "{{ prometheus_port }}"
  ignore_errors: true