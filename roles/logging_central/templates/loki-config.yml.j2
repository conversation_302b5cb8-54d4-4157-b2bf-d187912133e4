# Konfiguracja Loki dla ARM64 - zoptymalizowana dla Baserow
auth_enabled: {{ loki_auth_enabled }}

server:
  http_listen_port: {{ loki_server_http_listen_port }}
  grpc_listen_port: {{ loki_server_grpc_listen_port }}

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: {{ loki_ingester_lifecycler_ring_replication_factor }}
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: {{ loki_schema_config_from }}
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://alertmanager:{{ alertmanager_port }}

# Optymalizacje dla ARM64
ingester:
  wal:
    enabled: true
    dir: /loki/wal
  lifecycler:
    ring:
      kvstore:
        store: inmemory
      replication_factor: {{ loki_ingester_lifecycler_ring_replication_factor }}
    final_sleep: 0s
  chunk_idle_period: 5m
  chunk_retain_period: 30s
  max_transfer_retries: 0
  chunk_target_size: 1048576
  chunk_encoding: snappy

storage_config:
  boltdb_shipper:
    active_index_directory: /loki/boltdb-shipper-active
    cache_location: /loki/boltdb-shipper-cache
    cache_ttl: 24h
    shared_store: filesystem
  filesystem:
    directory: {{ loki_storage_filesystem_directory }}

compactor:
  working_directory: {{ loki_compactor_working_directory }}
  shared_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 2h
  retention_delete_worker_count: 150

limits_config:
  retention_period: {{ loki_limits_config_retention_period }}
  enforce_metric_name: {{ loki_limits_config_enforce_metric_name }}
  reject_old_samples: {{ loki_limits_config_reject_old_samples }}
  reject_old_samples_max_age: {{ loki_limits_config_reject_old_samples_max_age }}
  ingestion_rate_mb: 4
  ingestion_burst_size_mb: 6
  max_query_series: 500
  max_query_parallelism: 32
  max_label_name_length: 1024
  max_label_value_length: 4096
  max_label_names_per_series: 30
  max_streams_per_user: 10000
  max_line_size: 256000
  max_entries_limit_per_query: 5000
  max_chunks_per_query: 2000000
  max_query_length: 721h
  max_concurrent_tail_requests: 10
  cardinality_limit: 100000
  max_cache_freshness_per_query: 10m
  split_queries_by_interval: 15m

chunk_store_config:
  max_look_back_period: 0s

table_manager:
  retention_deletes_enabled: true
  retention_period: {{ loki_limits_config_retention_period }}

# Konfiguracja dla logów Baserow
frontend:
  max_outstanding_per_tenant: 256
  compress_responses: true
  downstream_url: http://127.0.0.1:3100

frontend_worker:
  frontend_address: 127.0.0.1:9095
  parallelism: 10
  grpc_client_config:
    max_send_msg_size: 104857600

query_scheduler:
  max_outstanding_requests_per_tenant: 256

memberlist:
  join_members: []