# Konfiguracja Alertmanager dla ARM64 - zoptymalizowana dla Baserow
global:
  smtp_smarthost: {{ alertmanager_global_smtp_smarthost }}
  smtp_from: {{ alertmanager_global_smtp_from }}
  smtp_auth_username: {{ alertmanager_global_smtp_auth_username }}
  smtp_auth_password: {{ alertmanager_global_smtp_auth_password }}
  smtp_require_tls: {{ alertmanager_global_smtp_require_tls }}
  resolve_timeout: 5m

templates:
  - '/etc/alertmanager/templates/*.tmpl'

route:
  group_by: {{ alertmanager_route_group_by }}
  group_wait: {{ alertmanager_route_group_wait }}
  group_interval: {{ alertmanager_route_group_interval }}
  repeat_interval: {{ alertmanager_route_repeat_interval }}
  receiver: {{ alertmanager_route_receiver }}
  routes:
    - match:
        alertname: DeadMansSwitch
      receiver: deadmansswitch
      repeat_interval: 30s
    - match:
        severity: critical
      receiver: critical-alerts
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1m
    - match:
        severity: warning
      receiver: warning-alerts
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h
    - match:
        service: baserow
      receiver: baserow-alerts
      group_wait: 10s
      group_interval: 2m
      repeat_interval: 30m
    - match:
        service: logging
      receiver: logging-alerts
      group_wait: 15s
      group_interval: 5m
      repeat_interval: 1h

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
  - source_match:
      alertname: 'NodeDown'
    target_match_re:
      alertname: 'Node.*'
    equal: ['instance']

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/'
        send_resolved: true

  - name: 'deadmansswitch'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/deadmansswitch'
        send_resolved: false

  - name: 'critical-alerts'
    email_configs:
      - to: {{ alertmanager_notification_email }}
        from: {{ alertmanager_global_smtp_from }}
        subject: '🚨 KRYTYCZNY ALERT: {{ "{{ .GroupLabels.SortedPairs }}" }}'
        body: |
          {{ "{{ range .Alerts }}" }}
          🚨 **KRYTYCZNY ALERT**
          
          **Alert:** {{ "{{ .Annotations.summary }}" }}
          **Szczegóły:** {{ "{{ .Annotations.description }}" }}
          **Severity:** {{ "{{ .Labels.severity }}" }}
          **Instance:** {{ "{{ .Labels.instance }}" }}
          **Service:** {{ "{{ .Labels.service }}" }}
          **Czas wystąpienia:** {{ "{{ .StartsAt }}" }}
          
          **Labels:**
          {{ "{{ range .Labels.SortedPairs }}" }}
          - {{ "{{ .Name }}" }}: {{ "{{ .Value }}" }}
          {{ "{{ end }}" }}
          
          {{ "{{ if .Annotations.runbook_url }}" }}
          **Runbook:** {{ "{{ .Annotations.runbook_url }}" }}
          {{ "{{ end }}" }}
          
          **Grafana:** {{ grafana_root_url }}
          **Alertmanager:** http://{{ ansible_default_ipv4.address }}:{{ alertmanager_port }}
          {{ "{{ end }}" }}
        headers:
          Priority: 'high'
        html: |
          <html>
          <head>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .alert { border: 2px solid #ff0000; padding: 15px; margin: 10px 0; background-color: #ffe6e6; }
              .critical { background-color: #ffcccc; }
              .warning { background-color: #fff3cd; }
              .info { background-color: #d1ecf1; }
            </style>
          </head>
          <body>
            <h2>🚨 KRYTYCZNY ALERT</h2>
            {{ "{{ range .Alerts }}" }}
            <div class="alert critical">
              <h3>{{ "{{ .Annotations.summary }}" }}</h3>
              <p><strong>Szczegóły:</strong> {{ "{{ .Annotations.description }}" }}</p>
              <p><strong>Severity:</strong> {{ "{{ .Labels.severity }}" }}</p>
              <p><strong>Instance:</strong> {{ "{{ .Labels.instance }}" }}</p>
              <p><strong>Service:</strong> {{ "{{ .Labels.service }}" }}</p>
              <p><strong>Czas wystąpienia:</strong> {{ "{{ .StartsAt }}" }}</p>
              <p><strong>Grafana:</strong> <a href="{{ grafana_root_url }}">Dashboard</a></p>
            </div>
            {{ "{{ end }}" }}
          </body>
          </html>

  - name: 'warning-alerts'
    email_configs:
      - to: {{ alertmanager_notification_email }}
        from: {{ alertmanager_global_smtp_from }}
        subject: '⚠️ OSTRZEŻENIE: {{ "{{ .GroupLabels.SortedPairs }}" }}'
        body: |
          {{ "{{ range .Alerts }}" }}
          ⚠️ **OSTRZEŻENIE**
          
          **Alert:** {{ "{{ .Annotations.summary }}" }}
          **Szczegóły:** {{ "{{ .Annotations.description }}" }}
          **Severity:** {{ "{{ .Labels.severity }}" }}
          **Instance:** {{ "{{ .Labels.instance }}" }}
          **Service:** {{ "{{ .Labels.service }}" }}
          **Czas wystąpienia:** {{ "{{ .StartsAt }}" }}
          
          **Grafana:** {{ grafana_root_url }}
          {{ "{{ end }}" }}

  - name: 'baserow-alerts'
    email_configs:
      - to: {{ alertmanager_notification_email }}
        from: {{ alertmanager_global_smtp_from }}
        subject: '🔧 BASEROW ALERT: {{ "{{ .GroupLabels.SortedPairs }}" }}'
        body: |
          {{ "{{ range .Alerts }}" }}
          🔧 **BASEROW ALERT**
          
          **Alert:** {{ "{{ .Annotations.summary }}" }}
          **Szczegóły:** {{ "{{ .Annotations.description }}" }}
          **Severity:** {{ "{{ .Labels.severity }}" }}
          **Instance:** {{ "{{ .Labels.instance }}" }}
          **Czas wystąpienia:** {{ "{{ .StartsAt }}" }}
          
          **Baserow URL:** {{ baserow_public_url | default('https://baserow.localhost') }}
          **Grafana:** {{ grafana_root_url }}
          {{ "{{ end }}" }}

  - name: 'logging-alerts'
    email_configs:
      - to: {{ alertmanager_notification_email }}
        from: {{ alertmanager_global_smtp_from }}
        subject: '📊 LOGGING ALERT: {{ "{{ .GroupLabels.SortedPairs }}" }}'
        body: |
          {{ "{{ range .Alerts }}" }}
          📊 **LOGGING SYSTEM ALERT**
          
          **Alert:** {{ "{{ .Annotations.summary }}" }}
          **Szczegóły:** {{ "{{ .Annotations.description }}" }}
          **Severity:** {{ "{{ .Labels.severity }}" }}
          **Instance:** {{ "{{ .Labels.instance }}" }}
          **Czas wystąpienia:** {{ "{{ .StartsAt }}" }}
          
          **Grafana:** {{ grafana_root_url }}
          **Loki:** http://{{ ansible_default_ipv4.address }}:{{ loki_port }}
          {{ "{{ end }}" }}