#!/bin/bash
# Skrypt backup dla centralnego systemu logowania
# Generowany automatycznie przez Ansible

set -euo pipefail

# Zmienne konfiguracyjne
BACKUP_DIR="{{ logging_backup_dir }}"
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="logging_backup_${BACKUP_DATE}"
RETENTION_DAYS={{ logging_backup_retention_days }}
LOGGING_HOME="{{ logging_central_home }}"
COMPOSE_PROJECT="logging_central"

# Funkcje pomocnicze
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${BACKUP_DIR}/backup.log"
}

send_notification() {
    local subject="$1"
    local body="$2"
    local email="{{ alertmanager_notification_email }}"
    
    if command -v mail >/dev/null 2>&1; then
        echo "$body" | mail -s "$subject" "$email"
    else
        log_message "UWAGA: mail nie jest zainstalowany, nie można wysłać powiadomienia"
    fi
}

cleanup_old_backups() {
    log_message "Czyszczenie starych backupów starszych niż $RETENTION_DAYS dni"
    find "$BACKUP_DIR" -name "logging_backup_*" -type f -mtime +$RETENTION_DAYS -delete
    find "$BACKUP_DIR" -name "logging_backup_*" -type d -mtime +$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
}

# Główna funkcja backup
perform_backup() {
    log_message "Rozpoczynanie backup systemu logowania"
    
    # Tworzenie katalogu backup
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
    cd "$BACKUP_DIR/$BACKUP_NAME"
    
    # Backup konfiguracji
    log_message "Backup konfiguracji..."
    cp -r "$LOGGING_HOME/config" ./
    cp "$LOGGING_HOME/docker-compose.yml" ./
    
    # Backup danych Grafana
    log_message "Backup danych Grafana..."
    if [ -d "$LOGGING_HOME/data/grafana" ]; then
        tar -czf grafana_data.tar.gz -C "$LOGGING_HOME/data" grafana/
    fi
    
    # Backup danych Prometheus
    log_message "Backup danych Prometheus..."
    if [ -d "$LOGGING_HOME/data/prometheus" ]; then
        tar -czf prometheus_data.tar.gz -C "$LOGGING_HOME/data" prometheus/
    fi
    
    # Backup danych Loki (tylko konfiguracja, logi mają retencję)
    log_message "Backup konfiguracji Loki..."
    if [ -d "$LOGGING_HOME/data/loki" ]; then
        tar -czf loki_config.tar.gz -C "$LOGGING_HOME/data" loki/
    fi
    
    # Backup danych Alertmanager
    log_message "Backup danych Alertmanager..."
    if [ -d "$LOGGING_HOME/data/alertmanager" ]; then
        tar -czf alertmanager_data.tar.gz -C "$LOGGING_HOME/data" alertmanager/
    fi
    
    # Generowanie informacji o backup
    cat > backup_info.txt << EOF
Backup Information
==================
Date: $(date)
Backup Name: $BACKUP_NAME
Host: $(hostname)
User: $(whoami)
Logging Home: $LOGGING_HOME
Docker Compose Project: $COMPOSE_PROJECT

Container Status:
$(docker-compose -f "$LOGGING_HOME/docker-compose.yml" ps)

Disk Usage:
$(df -h "$LOGGING_HOME")

Backup Size:
$(du -sh "$BACKUP_DIR/$BACKUP_NAME")
EOF
    
    # Kompresja całego backup
    cd "$BACKUP_DIR"
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME/"
    rm -rf "$BACKUP_NAME/"
    
    # Weryfikacja backup
    if [ -f "${BACKUP_NAME}.tar.gz" ]; then
        BACKUP_SIZE=$(du -sh "${BACKUP_NAME}.tar.gz" | cut -f1)
        log_message "Backup zakończony pomyślnie. Rozmiar: $BACKUP_SIZE"
        send_notification "✅ Backup logging - sukces" "Backup systemu logowania zakończony pomyślnie.\nRozmiar: $BACKUP_SIZE\nData: $(date)\nHost: $(hostname)"
    else
        log_message "BŁĄD: Backup nie został utworzony"
        send_notification "❌ Backup logging - błąd" "Backup systemu logowania nie powiódł się.\nData: $(date)\nHost: $(hostname)"
        exit 1
    fi
}

# Sprawdzenie czy Docker jest dostępny
if ! command -v docker >/dev/null 2>&1; then
    log_message "BŁĄD: Docker nie jest zainstalowany"
    exit 1
fi

if ! command -v docker-compose >/dev/null 2>&1; then
    log_message "BŁĄD: Docker Compose nie jest zainstalowany"
    exit 1
fi

# Główna logika
log_message "=== ROZPOCZYNANIE BACKUP SYSTEMU LOGOWANIA ==="

# Sprawdzenie czy katalog backup istnieje
if [ ! -d "$BACKUP_DIR" ]; then
    mkdir -p "$BACKUP_DIR"
fi

# Wykonanie backup
perform_backup

# Czyszczenie starych backupów
cleanup_old_backups

log_message "=== BACKUP SYSTEMU LOGOWANIA ZAKOŃCZONY ==="