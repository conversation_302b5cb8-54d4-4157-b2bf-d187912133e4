# Konfiguracja Prometheus dla ARM64 - zoptymalizowana dla Baserow
global:
  scrape_interval: {{ prometheus_global_scrape_interval }}
  evaluation_interval: {{ prometheus_global_evaluation_interval }}
  scrape_timeout: {{ prometheus_scrape_timeout }}
  external_labels:
    cluster: 'baserow-cluster'
    replica: 'prometheus-1'

# Zasady alertów
rule_files:
  - "alert_rules.yml"

# Konfiguracja Alertmanager
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:{{ alertmanager_port }}
      scheme: http
      timeout: 10s
      api_version: v1

# Konfiguracja scrapowania
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:{{ prometheus_port }}']
    scrape_interval: 15s
    metrics_path: /metrics

  # Alertmanager
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:{{ alertmanager_port }}']
    scrape_interval: 15s
    metrics_path: /metrics

  # Grafana
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 30s
    metrics_path: /metrics

  # Loki
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:{{ loki_port }}']
    scrape_interval: 15s
    metrics_path: /metrics

  # Node Exporter - wszystkie serwery Baserow
  - job_name: 'node-exporter'
    static_configs:
      - targets:
{% for host in groups['serwery'] | default([]) %}
        - '{{ hostvars[host]['ansible_default_ipv4']['address'] | default(host) }}:9100'
{% endfor %}
    scrape_interval: 15s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: 127.0.0.1:9100

  # Docker containers metrics
  - job_name: 'docker'
    static_configs:
      - targets:
{% for host in groups['serwery'] | default([]) %}
        - '{{ hostvars[host]['ansible_default_ipv4']['address'] | default(host) }}:9323'
{% endfor %}
    scrape_interval: 30s
    metrics_path: /metrics

  # Baserow aplikacja - monitoring zdrowia
  - job_name: 'baserow-health'
    static_configs:
      - targets:
{% for host in groups['serwery'] | default([]) %}
        - '{{ hostvars[host]['ansible_default_ipv4']['address'] | default(host) }}:{{ baserow_port | default(3000) }}'
{% endfor %}
    scrape_interval: 30s
    metrics_path: /api/health
    scheme: https
    tls_config:
      insecure_skip_verify: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: baserow
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: instance
        replacement: '${1}'

  # PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      - targets:
{% for host in groups['serwery'] | default([]) %}
        - '{{ hostvars[host]['ansible_default_ipv4']['address'] | default(host) }}:9187'
{% endfor %}
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: postgresql
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: instance
        replacement: '${1}'

  # Redis Exporter
  - job_name: 'redis-exporter'
    static_configs:
      - targets:
{% for host in groups['serwery'] | default([]) %}
        - '{{ hostvars[host]['ansible_default_ipv4']['address'] | default(host) }}:9121'
{% endfor %}
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: redis
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: instance
        replacement: '${1}'

  # Nginx Exporter
  - job_name: 'nginx-exporter'
    static_configs:
      - targets:
{% for host in groups['serwery'] | default([]) %}
        - '{{ hostvars[host]['ansible_default_ipv4']['address'] | default(host) }}:9113'
{% endfor %}
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: nginx
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: instance
        replacement: '${1}'

  # Blackbox Exporter - monitoring dostępności
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - {{ baserow_public_url | default('https://baserow.localhost') }}
        - {{ grafana_root_url }}
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: 127.0.0.1:9115
      - source_labels: [__param_target]
        target_label: service
        replacement: web-check

  # Blackbox Exporter - monitoring SSL
  - job_name: 'blackbox-ssl'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
        - {{ baserow_domain | default('baserow.localhost') }}:443
        - {{ grafana_domain }}:443
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: 127.0.0.1:9115
      - source_labels: [__param_target]
        target_label: service
        replacement: ssl-check

# Konfiguracja remote write (opcjonalnie)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Konfiguracja storage
storage:
  tsdb:
    path: {{ prometheus_storage_tsdb_path }}
    retention.time: {{ prometheus_storage_tsdb_retention_time }}
    retention.size: "10GB"
    wal-compression: true