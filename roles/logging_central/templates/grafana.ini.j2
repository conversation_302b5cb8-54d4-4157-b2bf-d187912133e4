[DEFAULT]
# Konfigu<PERSON><PERSON> Grafana dla ARM64 - zoptymalizowana dla Baserow

[server]
# Protokół (http, https, h2, socket)
protocol = http
http_addr = 0.0.0.0
http_port = 3000
domain = {{ grafana_domain }}
root_url = {{ grafana_root_url }}
enable_gzip = true
cert_file =
cert_key =
socket =

[database]
# Typ bazy danych (mysql, postgres, sqlite3)
type = {{ grafana_database_type }}
host = 127.0.0.1:3306
name = grafana
user = root
password =
ssl_mode = disable
path = {{ grafana_database_path }}

[security]
# Ustawienia bezpieczeństwa
admin_user = {{ grafana_security_admin_user }}
admin_password = {{ grafana_security_admin_password }}
secret_key = {{ grafana_security_secret_key }}
login_remember_days = 7
cookie_username = grafana_user
cookie_remember_name = grafana_remember
disable_gravatar = {{ grafana_security_disable_gravatar }}
data_source_proxy_whitelist =
disable_brute_force_login_protection = false
cookie_secure = {{ grafana_security_cookie_secure }}
cookie_samesite = {{ grafana_security_cookie_samesite }}
allow_embedding = false

[snapshots]
# Snapshot sharing options
external_enabled = false
external_snapshot_url = https://snapshots-origin.raintank.io
external_snapshot_name = Publish to snapshot.raintank.io
public_mode = false

[auth]
# Ustawienia autoryzacji
login_cookie_name = grafana_session
login_maximum_inactive_lifetime_duration = 7d
login_maximum_lifetime_duration = 30d
token_rotation_interval_minutes = 10
disable_login_form = {{ grafana_auth_disable_login_form }}
disable_signout_menu = false
signout_redirect_url =
oauth_auto_login = false
oauth_state_cookie_max_age = 600

[auth.anonymous]
# Anonimowy dostęp
enabled = {{ grafana_auth_anonymous_enabled }}
org_name = Main Org.
org_role = Viewer

[auth.basic]
# Podstawowa autoryzacja HTTP
enabled = {{ grafana_auth_basic_enabled }}

[auth.ldap]
# LDAP autoryzacja
enabled = false
config_file = /etc/grafana/ldap.toml
allow_sign_up = true

[smtp]
# Konfiguracja SMTP
enabled = false
host = {{ alertmanager_global_smtp_smarthost }}
user = {{ alertmanager_global_smtp_auth_username }}
password = {{ alertmanager_global_smtp_auth_password }}
cert_file =
key_file =
skip_verify = false
from_address = {{ alertmanager_global_smtp_from }}
from_name = Grafana
ehlo_identity = dashboard.example.com

[emails]
# Szablony email
welcome_email_on_sign_up = false
templates_pattern = emails/*.html

[log]
# Logging
mode = console file
level = info
format = text
filters =

[log.console]
level = info
format = text

[log.file]
level = info
format = text
log_rotate = true
max_lines = 1000000
max_size_shift = 28
daily_rotate = true
max_days = 7

[alerting]
# Alerting
enabled = true
execute_alerts = true
error_or_timeout = alerting
nodata_or_nullvalues = no_data
concurrent_render_limit = 5
evaluation_timeout_seconds = 30
notification_timeout_seconds = 30
max_attempts = 3
min_interval_seconds = 1

[metrics]
# Metryki
enabled = true
basic_auth_username =
basic_auth_password =
interval_seconds = 10

[metrics.graphite]
# Grafite metryki
address =
prefix = prod.grafana.%(instance_name)s.

[tracing.jaeger]
# Jaeger tracing
address = localhost:6831
always_included_tag = tag1:value1
sampler_type = const
sampler_param = 1

[grafana_net]
url = https://grafana.net

[external_image_storage]
provider = local

[external_image_storage.s3]
bucket_url =
bucket =
region =
path =
access_key =
secret_key =

[external_image_storage.webdav]
url =
public_url =
username =
password =

[external_image_storage.gcs]
key_file =
bucket =
path =

[external_image_storage.azure_blob]
account_name =
account_key =
container_name =

[external_image_storage.local]
path = data/png

[rendering]
# Image rendering
server_url =
callback_url =
concurrent_render_request_limit = 30

[enterprise]
license_path =

[feature_toggles]
# Nowe funkcje
enable = 

[date_formats]
# Formaty dat
full_date = YYYY-MM-DD HH:mm:ss
interval_second = HH:mm:ss
interval_minute = HH:mm
interval_hour = MM/DD HH:mm
interval_day = MM/DD
interval_month = YYYY-MM
interval_year = YYYY

[panels]
# Panele
enable_alpha = false
disable_sanitize_html = false

[plugins]
# Wtyczki
enable_alpha = false
app_tls_skip_verify_insecure = false
allow_loading_unsigned_plugins = false