version: '3.8'

services:
  grafana:
    image: {{ grafana_image }}
    container_name: logging_central_grafana
    restart: {{ restart_policy }}
    ports:
      - "{{ grafana_port }}:3000"
    environment:
      - GF_SECURITY_ADMIN_USER={{ grafana_admin_user }}
      - GF_SECURITY_ADMIN_PASSWORD={{ grafana_admin_password }}
      - GF_SECURITY_SECRET_KEY={{ grafana_security_secret_key }}
      - GF_SECURITY_DISABLE_GRAVATAR={{ grafana_security_disable_gravatar }}
      - GF_SECURITY_COOKIE_SECURE={{ grafana_security_cookie_secure }}
      - GF_SECURITY_COOKIE_SAMESITE={{ grafana_security_cookie_samesite }}
      - GF_SERVER_ROOT_URL={{ grafana_root_url }}
      - GF_SERVER_DOMAIN={{ grafana_domain }}
      - GF_DATABASE_TYPE={{ grafana_database_type }}
      - GF_DATABASE_PATH={{ grafana_database_path }}
      - GF_AUTH_ANONYMOUS_ENABLED={{ grafana_auth_anonymous_enabled }}
      - GF_AUTH_BASIC_ENABLED={{ grafana_auth_basic_enabled }}
      - GF_AUTH_DISABLE_LOGIN_FORM={{ grafana_auth_disable_login_form }}
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - {{ logging_central_data_dir }}/grafana:/var/lib/grafana
      - {{ logging_central_config_dir }}/grafana.ini:/etc/grafana/grafana.ini
    user: "{{ ansible_user_uid | default('1000') }}:{{ ansible_user_gid | default('1000') }}"
    deploy:
      resources:
        limits:
          memory: {{ grafana_memory_limit }}
    depends_on:
      - loki
      - prometheus
    networks:
      - logging_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: {{ logging_health_check_interval }}
      timeout: {{ logging_health_check_timeout }}
      retries: {{ logging_health_check_retries }}

  loki:
    image: {{ loki_image }}
    container_name: logging_central_loki
    restart: {{ restart_policy }}
    ports:
      - "{{ loki_port }}:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - {{ logging_central_data_dir }}/loki:/loki
      - {{ logging_central_config_dir }}/loki-config.yml:/etc/loki/local-config.yaml
    user: "{{ ansible_user_uid | default('1000') }}:{{ ansible_user_gid | default('1000') }}"
    deploy:
      resources:
        limits:
          memory: {{ loki_memory_limit }}
    networks:
      - logging_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3100/ready || exit 1"]
      interval: {{ logging_health_check_interval }}
      timeout: {{ logging_health_check_timeout }}
      retries: {{ logging_health_check_retries }}

  prometheus:
    image: {{ prometheus_image }}
    container_name: logging_central_prometheus
    restart: {{ restart_policy }}
    ports:
      - "{{ prometheus_port }}:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path={{ prometheus_storage_tsdb_path }}'
      - '--storage.tsdb.retention.time={{ prometheus_storage_tsdb_retention_time }}'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - {{ logging_central_data_dir }}/prometheus:/prometheus
      - {{ logging_central_config_dir }}/prometheus.yml:/etc/prometheus/prometheus.yml
    user: "{{ ansible_user_uid | default('1000') }}:{{ ansible_user_gid | default('1000') }}"
    deploy:
      resources:
        limits:
          memory: {{ prometheus_memory_limit }}
    networks:
      - logging_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9090/-/healthy || exit 1"]
      interval: {{ logging_health_check_interval }}
      timeout: {{ logging_health_check_timeout }}
      retries: {{ logging_health_check_retries }}

  alertmanager:
    image: {{ alertmanager_image }}
    container_name: logging_central_alertmanager
    restart: {{ restart_policy }}
    ports:
      - "{{ alertmanager_port }}:9093"
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://{{ ansible_default_ipv4.address }}:{{ alertmanager_port }}'
    volumes:
      - {{ logging_central_data_dir }}/alertmanager:/alertmanager
      - {{ logging_central_config_dir }}/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    user: "{{ ansible_user_uid | default('1000') }}:{{ ansible_user_gid | default('1000') }}"
    deploy:
      resources:
        limits:
          memory: {{ alertmanager_memory_limit }}
    networks:
      - logging_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9093/-/healthy || exit 1"]
      interval: {{ logging_health_check_interval }}
      timeout: {{ logging_health_check_timeout }}
      retries: {{ logging_health_check_retries }}

networks:
  logging_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  grafana_data:
    driver: local
  loki_data:
    driver: local
  prometheus_data:
    driver: local
  alertmanager_data:
    driver: local