{"dashboard": {"id": null, "title": "System Monitoring - Baserow Infrastructure", "tags": ["baserow", "system", "monitoring"], "style": "dark", "timezone": "Europe/Warsaw", "refresh": "30s", "schemaVersion": 27, "version": 1, "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "{{instance}} - {{job}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "text": {}, "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "pluginVersion": "8.0.0", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}, "unit": "short"}}}, {"id": 2, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "{{instance}} CPU Usage", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "yAxes": [{"label": "Percent", "max": 100, "min": 0, "unit": "percent"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "nullPointMode": "null", "thresholds": [{"colorMode": "critical", "value": 80, "visible": true}]}, {"id": 3, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "((node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes) * 100", "legendFormat": "{{instance}} Memory Usage", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "yAxes": [{"label": "Percent", "max": 100, "min": 0, "unit": "percent"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "thresholds": [{"colorMode": "critical", "value": 85, "visible": true}]}, {"id": 4, "title": "Disk Usage", "type": "graph", "targets": [{"expr": "((node_filesystem_size_bytes{fstype!=\"tmpfs\"} - node_filesystem_avail_bytes{fstype!=\"tmpfs\"}) / node_filesystem_size_bytes{fstype!=\"tmpfs\"}) * 100", "legendFormat": "{{instance}} - {{mountpoint}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "yAxes": [{"label": "Percent", "max": 100, "min": 0, "unit": "percent"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "thresholds": [{"colorMode": "critical", "value": 80, "visible": true}]}, {"id": 5, "title": "Network Traffic", "type": "graph", "targets": [{"expr": "irate(node_network_receive_bytes_total{device!=\"lo\"}[5m])", "legendFormat": "{{instance}} - {{device}} RX", "refId": "A"}, {"expr": "irate(node_network_transmit_bytes_total{device!=\"lo\"}[5m])", "legendFormat": "{{instance}} - {{device}} TX", "refId": "B"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "yAxes": [{"label": "Bytes/sec", "unit": "Bps"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "annotations": {"list": []}, "templating": {"list": []}, "links": []}}