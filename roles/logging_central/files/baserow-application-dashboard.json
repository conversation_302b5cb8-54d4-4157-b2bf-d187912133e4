{"dashboard": {"id": null, "title": "Baserow Application Monitoring", "tags": ["baserow", "application", "postgres", "redis"], "style": "dark", "timezone": "Europe/Warsaw", "refresh": "30s", "schemaVersion": 27, "version": 1, "panels": [{"id": 1, "title": "Baserow Services Status", "type": "stat", "targets": [{"expr": "up{job=~\"baserow.*\"}", "legendFormat": "{{job}} - {{instance}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "text": {}, "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}, "unit": "short"}}}, {"id": 2, "title": "HTTP Response Times", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(nginx_http_request_duration_seconds_bucket{server_name=~\".*baserow.*\"}[5m])) by (le))", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, sum(rate(nginx_http_request_duration_seconds_bucket{server_name=~\".*baserow.*\"}[5m])) by (le))", "legendFormat": "50th percentile", "refId": "B"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "yAxes": [{"label": "Seconds", "unit": "s"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}}, {"id": 3, "title": "HTTP Request Rate", "type": "graph", "targets": [{"expr": "sum(rate(nginx_http_requests_total{server_name=~\".*baserow.*\"}[5m])) by (status)", "legendFormat": "{{status}} responses", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "yAxes": [{"label": "Requests/sec", "unit": "reqps"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}}, {"id": 4, "title": "PostgreSQL Connections", "type": "graph", "targets": [{"expr": "pg_stat_database_numbackends{datname=\"baserow\"}", "legendFormat": "Active connections", "refId": "A"}, {"expr": "pg_settings_max_connections", "legendFormat": "Max connections", "refId": "B"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "yAxes": [{"label": "Connections", "unit": "short"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "thresholds": [{"colorMode": "critical", "value": 70, "visible": true}]}, {"id": 5, "title": "PostgreSQL Query Performance", "type": "graph", "targets": [{"expr": "rate(pg_stat_database_tup_returned{datname=\"baserow\"}[5m])", "legendFormat": "Rows returned/sec", "refId": "A"}, {"expr": "rate(pg_stat_database_tup_fetched{datname=\"baserow\"}[5m])", "legendFormat": "Rows fetched/sec", "refId": "B"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "yAxes": [{"label": "Rows/sec", "unit": "rps"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}}, {"id": 6, "title": "Redis Memory Usage", "type": "graph", "targets": [{"expr": "redis_memory_used_bytes", "legendFormat": "Used memory", "refId": "A"}, {"expr": "redis_memory_max_bytes", "legendFormat": "Max memory", "refId": "B"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "yAxes": [{"label": "Bytes", "unit": "bytes"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}}, {"id": 7, "title": "Database Size", "type": "stat", "targets": [{"expr": "pg_database_size_bytes{datname=\"baserow\"}", "legendFormat": "Database size", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "text": {}, "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000000000}, {"color": "red", "value": 5000000000}]}, "unit": "bytes"}}}, {"id": 8, "title": "Error Rate", "type": "stat", "targets": [{"expr": "sum(rate(nginx_http_requests_total{server_name=~\".*baserow.*\",status=~\"5..\"}[5m])) / sum(rate(nginx_http_requests_total{server_name=~\".*baserow.*\"}[5m])) * 100", "legendFormat": "Error rate %", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "text": {}, "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "annotations": {"list": []}, "templating": {"list": []}, "links": []}}