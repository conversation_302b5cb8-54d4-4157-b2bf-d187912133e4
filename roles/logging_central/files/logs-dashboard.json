{"dashboard": {"id": null, "title": "Logs Dashboard - Baserow Infrastructure", "tags": ["baserow", "logs", "loki"], "style": "dark", "timezone": "Europe/Warsaw", "refresh": "30s", "schemaVersion": 27, "version": 1, "panels": [{"id": 1, "title": "Log Volume by Service", "type": "graph", "targets": [{"expr": "sum(rate({job=~\".*\"} |= \"\" [5m])) by (job)", "legendFormat": "{{job}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "yAxes": [{"label": "Logs/sec", "unit": "short"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "datasource": "<PERSON>"}, {"id": 2, "title": "<PERSON><PERSON><PERSON>", "type": "graph", "targets": [{"expr": "sum(rate({job=~\".*\"} |~ \"(?i)(error|exception|fail|fatal)\" [5m])) by (job)", "legendFormat": "{{job}} errors", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "yAxes": [{"label": "Errors/sec", "unit": "short"}], "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "datasource": "<PERSON>", "alert": {"conditions": [{"evaluator": {"params": [0.1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "High Error Rate", "noDataState": "no_data", "notifications": []}}, {"id": 3, "title": "System Logs", "type": "logs", "targets": [{"expr": "{job=\"syslog\"}", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "datasource": "<PERSON>"}, {"id": 4, "title": "Baserow Application Logs", "type": "logs", "targets": [{"expr": "{job=\"baserow\"}", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "datasource": "<PERSON>"}, {"id": 5, "title": "Nginx Access Logs", "type": "logs", "targets": [{"expr": "{job=\"nginx-access\"}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "datasource": "<PERSON>"}, {"id": 6, "title": "<PERSON><PERSON><PERSON>", "type": "logs", "targets": [{"expr": "{job=\"nginx-error\"}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "datasource": "<PERSON>"}, {"id": 7, "title": "Authentication Logs", "type": "logs", "targets": [{"expr": "{job=\"auth\"}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "datasource": "<PERSON>"}, {"id": 8, "title": "Kernel Logs", "type": "logs", "targets": [{"expr": "{job=\"kern\"}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}, "datasource": "<PERSON>"}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "annotations": {"list": []}, "templating": {"list": [{"allValue": null, "current": {"text": "All", "value": "$__all"}, "datasource": "<PERSON>", "definition": "label_values(job)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "job", "options": [], "query": "label_values(job)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"text": "All", "value": "$__all"}, "datasource": "<PERSON>", "definition": "label_values(host)", "hide": 0, "includeAll": true, "label": "Host", "multi": true, "name": "host", "options": [], "query": "label_values(host)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "links": []}}