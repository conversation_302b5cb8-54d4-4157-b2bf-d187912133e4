---
# <PERSON><PERSON><PERSON> dla roli logging_central

- name: restart logging containers
  community.docker.docker_compose_v2:
    project_src: "{{ logging_central_home }}"
    project_name: "logging_central"
    restarted: true
    pull: always
  become_user: "{{ logging_central_user }}"
  become: true

- name: reload grafana
  uri:
    url: "http://{{ ansible_default_ipv4.address }}:{{ grafana_port }}/api/admin/settings/reload"
    method: POST
    user: "{{ grafana_admin_user }}"
    password: "{{ grafana_admin_password }}"
    force_basic_auth: true
    status_code: 200
  ignore_errors: true

- name: reload prometheus
  uri:
    url: "http://{{ ansible_default_ipv4.address }}:{{ prometheus_port }}/-/reload"
    method: POST
    status_code: 200
  ignore_errors: true

- name: reload alertmanager
  uri:
    url: "http://{{ ansible_default_ipv4.address }}:{{ alertmanager_port }}/-/reload"
    method: POST
    status_code: 200
  ignore_errors: true

- name: restart grafana
  docker_container:
    name: "logging_central_grafana"
    restart: true
  ignore_errors: true

- name: restart loki
  docker_container:
    name: "logging_central_loki"
    restart: true
  ignore_errors: true

- name: restart prometheus
  docker_container:
    name: "logging_central_prometheus"
    restart: true
  ignore_errors: true

- name: restart alertmanager
  docker_container:
    name: "logging_central_alertmanager"
    restart: true
  ignore_errors: true

- name: check logging services
  wait_for:
    host: "{{ ansible_default_ipv4.address }}"
    port: "{{ item }}"
    timeout: 30
  loop:
    - "{{ grafana_port }}"
    - "{{ loki_port }}"
    - "{{ alertmanager_port }}"
    - "{{ prometheus_port }}"
  ignore_errors: true