---
# defaults file for baserow_app role

# Konfiguracja użytkownika
baserow_user: baserow
baserow_group: baserow
baserow_home: /opt/baserow

# Konfiguracja Baserow
baserow_domain: baserow.simetria.pl
baserow_port: 3000

# Konfiguracja obrazów Docker
baserow_postgres_image: "postgres:13"
baserow_redis_image: "redis:6.2"
baserow_backend_image: "baserow/backend:latest"
baserow_frontend_image: "baserow/web-frontend:latest"

# Konfiguracja bazy danych
baserow_db_name: baserow
baserow_db_user: baserow
baserow_db_password: "changeme" # This should be changed in production

# Konfiguracja aplikacji
baserow_secret_key: "changeme" # This should be changed in production
baserow_public_url: "https://{{ baserow_domain }}"
restart_policy: unless-stopped
baserow_backend_workers: 2

# Konfiguracja health checks
health_check_interval: 10s
health_check_timeout: 5s
health_check_retries: 5

# Konfiguracja optymalizacji
postgres_shared_buffers: "256MB"
postgres_effective_cache_size: "768MB"
postgres_work_mem: "4MB"
postgres_maintenance_work_mem: "64MB"
postgres_max_connections: 100
redis_maxmemory: "256mb"
redis_maxmemory_policy: "allkeys-lru"

# Konfiguracja email (domyślnie wyłączone)
email_smtp_enabled: false
email_smtp_host: ""
email_smtp_port: 587
email_smtp_user: ""
email_smtp_password: ""
email_from_address: "noreply@{{ baserow_domain }}"

# Tryb debugowania
debug_mode: false

# Konfiguracja Docker
log_max_size: 10m
log_max_files: 3
