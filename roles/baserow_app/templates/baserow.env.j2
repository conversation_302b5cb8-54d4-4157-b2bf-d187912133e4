# Baserow Environment Configuration
# Generated by Ansible - do not edit manually!

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://{{ baserow_db_user }}:{{ baserow_db_password }}@postgres:5432/{{ baserow_db_name }}
REDIS_URL=redis://redis:6379

# =============================================================================
# BASEROW CONFIGURATION
# =============================================================================
BASEROW_PUBLIC_URL={{ baserow_public_url }}
SECRET_KEY={{ baserow_secret_key }}
BASEROW_AMOUNT_OF_WORKERS={{ baserow_backend_workers }}

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
{% if email_smtp_enabled %}
EMAIL_SMTP=true
EMAIL_SMTP_HOST={{ email_smtp_host }}
EMAIL_SMTP_PORT={{ email_smtp_port }}
EMAIL_SMTP_USER={{ email_smtp_user }}
EMAIL_SMTP_PASSWORD={{ email_smtp_password }}
FROM_EMAIL={{ email_from_address }}
{% else %}
EMAIL_SMTP=false
{% endif %}

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ALLOWED_HOSTS={{ baserow_domain }},localhost,127.0.0.1
CSRF_TRUSTED_ORIGINS={{ baserow_public_url }}

# =============================================================================
# DEBUG CONFIGURATION
# =============================================================================
{% if debug_mode %}
DEBUG=true
{% else %}
DEBUG=false
{% endif %}

# =============================================================================
# PERFORMANCE OPTIMIZATION
# =============================================================================
BASEROW_TRIGGER_SYNC_TEMPLATES_AFTER_MIGRATION=false
BASEROW_SYNC_TEMPLATES_TIME_LIMIT=30
