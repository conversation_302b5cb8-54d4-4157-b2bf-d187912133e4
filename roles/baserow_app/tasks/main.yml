---
# Deployment aplik<PERSON><PERSON>row

- name: "Rozpoczęcie deployment Baserow"
  debug:
    msg:
      - "=== DEPLOYMENT BASEROW ==="
      - "Serwer: {{ ansible_hostname }}"
      - "Użytkownik: {{ baserow_user }}"
      - "Katalog: {{ baserow_home }}"
      - "Domena: {{ baserow_domain }}"
      - "Port: {{ baserow_port }}"
  tags: [baserow, info]

# =============================================================================
# KONFIGURACJA UŻYTKOWNIKA
# =============================================================================

- name: "<PERSON><PERSON><PERSON><PERSON> grupę baserow"
  group:
    name: "{{ baserow_group }}"
    gid: 1500
    state: present
  become: true
  tags: [baserow, user]

- name: "St<PERSON><PERSON>rz użytkownika baserow"
  user:
    name: "{{ baserow_user }}"
    uid: 1500
    group: "{{ baserow_group }}"
    groups: docker
    home: "{{ baserow_home }}"
    shell: /bin/bash
    create_home: true
    system: false
    state: present
  become: true
  tags: [baserow, user]

# =============================================================================
# STRUKTURA KATALOGÓW
# =============================================================================

- name: "Stwórz strukturę katalogów Baserow"
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0755'
  become: true
  loop:
    - "{{ baserow_home }}"
    - "{{ baserow_home }}/data"
    - "{{ baserow_home }}/data/postgres"
    - "{{ baserow_home }}/data/redis"
    - "{{ baserow_home }}/data/media"
    - "{{ baserow_home }}/logs"
    - "{{ baserow_home }}/logs/nginx"
    - "{{ baserow_home }}/logs/baserow"
    - "{{ baserow_home }}/config"
    - "{{ baserow_home }}/volumes"
    - "{{ baserow_home }}/volumes/postgres_data"
    - "{{ baserow_home }}/volumes/baserow_data"
    - "{{ baserow_home }}/volumes/redis_data"
    - "{{ baserow_home }}/volumes/nginx_cache"
  tags: [baserow, directories]

# =============================================================================
# KONFIGURACJA DOCKER COMPOSE
# =============================================================================

- name: "Zatrzymaj istniejące kontenery Baserow (jeśli istnieją)"
  shell: |
    cd {{ baserow_home }}
    if [ -f docker-compose.yml ]; then
      docker-compose down || true
    fi
  become_user: "{{ baserow_user }}"
  become: true
  ignore_errors: true
  tags: [baserow, stop]

- name: "Stwórz plik docker-compose.yml z template"
  template:
    src: docker-compose.yml.j2
    dest: "{{ baserow_home }}/docker-compose.yml"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0644'
  become: true
  notify: restart baserow
  tags: [baserow, config]

- name: "Stwórz plik .env z konfiguracją"
  template:
    src: baserow.env.j2
    dest: "{{ baserow_home }}/.env"
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0600'
  become: true
  notify: restart baserow
  tags: [baserow, config]

# =============================================================================
# URUCHOMIENIE BASEROW
# =============================================================================

- name: "Pobierz obrazy Docker"
  shell: |
    cd {{ baserow_home }}
    docker-compose pull
  become_user: "{{ baserow_user }}"
  become: true
  register: docker_pull_result
  tags: [baserow, pull]

- name: "Uruchom Baserow"
  shell: |
    cd {{ baserow_home }}
    docker-compose up -d
  become_user: "{{ baserow_user }}"
  become: true
  register: baserow_start_result
  tags: [baserow, start]

- name: "Wyświetl rezultat uruchomienia Baserow"
  debug:
    msg: "Uruchomienie Baserow: {{ 'SUKCES' if baserow_start_result.rc == 0 else 'BŁĄD' }}"
  tags: [baserow, info]

- name: "Poczekaj na uruchomienie kontenerów (30 sekund)"
  pause:
    seconds: 30
  tags: [baserow, wait]

# =============================================================================
# WERYFIKACJA
# =============================================================================

- name: "Sprawdź czy kontenery są uruchomione"
  shell: |
    cd {{ baserow_home }}
    docker-compose ps | grep -c "Up" || echo "0"
  become_user: "{{ baserow_user }}"
  become: true
  register: container_check
  ignore_errors: true
  tags: [baserow, verify]

- name: "Wyświetl wynik sprawdzenia kontenerów"
  debug:
    msg: "Liczba działających kontenerów: {{ container_check.stdout }}"
  tags: [baserow, info]

- name: "Sprawdź czy Baserow odpowiada"
  uri:
    url: "http://localhost:{{ baserow_port }}"
    method: GET
    timeout: 30
  register: baserow_health
  ignore_errors: true
  tags: [baserow, health]

# =============================================================================
# KONFIGURACJA ALIASÓW
# =============================================================================

- name: "Dodaj aliasy Baserow do .bashrc użytkownika"
  blockinfile:
    path: "{{ baserow_home }}/.bashrc"
    marker: "# {mark} BASEROW ALIASES"
    block: |
      # Baserow aliasy
      alias baserow-logs='docker compose -f {{ baserow_home }}/docker-compose.yml logs -f'
      alias baserow-status='docker compose -f {{ baserow_home }}/docker-compose.yml ps'
      alias baserow-restart='docker compose -f {{ baserow_home }}/docker-compose.yml restart'
      alias baserow-stop='docker compose -f {{ baserow_home }}/docker-compose.yml down'
      alias baserow-start='docker compose -f {{ baserow_home }}/docker-compose.yml up -d'
      
      # Zmienne środowiskowe
      export COMPOSE_FILE="{{ baserow_home }}/docker-compose.yml"
      
      # Funkcje pomocnicze
      baserow_health() {
          echo "=== Baserow Health Check ==="
          docker compose -f {{ baserow_home }}/docker-compose.yml ps
          echo ""
          echo "=== Disk Usage ==="
          du -sh {{ baserow_home }}/*
          echo ""
          echo "=== Memory Usage ==="
          free -h
      }
    owner: "{{ baserow_user }}"
    group: "{{ baserow_group }}"
    mode: '0644'
    create: true
  become: true
  tags: [baserow, aliases]

# =============================================================================
# PODSUMOWANIE
# =============================================================================

- name: "Podsumowanie deployment Baserow"
  debug:
    msg:
      - "=== DEPLOYMENT BASEROW ZAKOŃCZONY ==="
      - ""
      - "🏠 KATALOG: {{ baserow_home }}"
      - "👤 UŻYTKOWNIK: {{ baserow_user }}"
      - "🌐 DOMENA: {{ baserow_domain }}"
      - "🔌 PORT: {{ baserow_port }}"
      - ""
      - "📊 STATUS:"
      - "  Kontenery uruchomione: {{ container_check.stdout }}/4"
      - "  Baserow health: {{ '✅ OK' if baserow_health.status == 200 else '❌ BŁĄD' }}"
      - ""
      - "🔧 ZARZĄDZANIE:"
      - "  Start: cd {{ baserow_home }} && docker-compose up -d"
      - "  Stop: cd {{ baserow_home }} && docker-compose down"
      - "  Logi: cd {{ baserow_home }} && docker-compose logs -f"
      - "  Status: cd {{ baserow_home }} && docker-compose ps"
      - ""
      - "🌐 DOSTĘP:"
      - "  Lokalny: http://localhost:{{ baserow_port }}"
      - "  Publiczny: http://{{ baserow_domain }} (po konfiguracji Nginx)"
  tags: [baserow, info]
