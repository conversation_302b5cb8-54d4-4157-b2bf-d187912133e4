---
- name: <PERSON><PERSON><PERSON><PERSON> diagnoza wydajności serwera OVH
  hosts: serwery_legacy
  become: true
  gather_facts: true
  tasks:
    # ========================================
    # PODSTAWOWE INFORMACJE O SYSTEMIE
    # ========================================
    - name: Sprawdź podstawowe informacje o systemie
      ansible.builtin.shell: |
        echo "=== SYSTEM INFO ==="
        uname -a
        echo "=== UPTIME ==="
        uptime
        echo "=== LOAD AVERAGE ==="
        cat /proc/loadavg
      register: system_info
      tags: [system, basic]

    # ========================================
    # ANALIZA WYKORZYSTANIA DYSKU
    # ========================================
    - name: Sprawdź wykorzystanie dysku (df -h)
      ansible.builtin.shell: df -h
      register: disk_usage
      tags: [disk, usage]

    - name: Sprawdź wykorzystanie inodów
      ansible.builtin.shell: df -i
      register: inode_usage
      tags: [disk, inodes]

    - name: Znajdź największe katalogi (top 20)
      ansible.builtin.shell: |
        du -h --max-depth=2 / 2>/dev/null | sort -hr | head -20
      register: large_directories
      tags: [disk, large_dirs]

    - name: Sprawdź największe pliki (top 20)
      ansible.builtin.shell: |
        find / -type f -size +100M -exec ls -lh {} \; 2>/dev/null | sort -k5 -hr | head -20
      register: large_files
      tags: [disk, large_files]

    - name: Sprawdź logi systemowe (rozmiar)
      ansible.builtin.shell: |
        echo "=== LOG SIZES ==="
        du -sh /var/log/* 2>/dev/null | sort -hr | head -10
        echo "=== JOURNAL SIZE ==="
        journalctl --disk-usage
      register: log_sizes
      tags: [disk, logs]

    # ========================================
    # ANALIZA PAMIĘCI I CPU
    # ========================================
    - name: Sprawdź wykorzystanie pamięci
      ansible.builtin.shell: |
        echo "=== MEMORY INFO ==="
        free -h
        echo "=== MEMORY DETAILS ==="
        cat /proc/meminfo | head -20
      register: memory_info
      tags: [memory]

    - name: Sprawdź procesy zużywające najwięcej pamięci
      ansible.builtin.shell: |
        ps aux --sort=-%mem | head -20
      register: memory_processes
      tags: [memory, processes]

    - name: Sprawdź procesy zużywające najwięcej CPU
      ansible.builtin.shell: |
        ps aux --sort=-%cpu | head -20
      register: cpu_processes
      tags: [cpu, processes]

    # ========================================
    # ANALIZA DOCKER I KONTENERÓW
    # ========================================
    - name: Sprawdź wykorzystanie zasobów przez Docker
      ansible.builtin.shell: |
        echo "=== DOCKER SYSTEM INFO ==="
        docker system df 2>/dev/null || echo "Docker nie jest dostępny"
        echo "=== DOCKER STATS (snapshot) ==="
        timeout 5 docker stats --no-stream 2>/dev/null || echo "Brak uruchomionych kontenerów"
      register: docker_resources
      ignore_errors: true
      tags: [docker, resources]

    - name: Sprawdź kontenery Docker
      ansible.builtin.shell: |
        echo "=== RUNNING CONTAINERS ==="
        docker ps
        echo "=== ALL CONTAINERS ==="
        docker ps -a
      register: docker_containers
      ignore_errors: true
      tags: [docker, containers]

    - name: Sprawdź woluminy Docker
      ansible.builtin.shell: |
        echo "=== DOCKER VOLUMES ==="
        docker volume ls
        echo "=== VOLUME SIZES ==="
        docker system df -v 2>/dev/null | grep -A 20 "Local Volumes" || echo "Nie można pobrać szczegółów woluminów"
      register: docker_volumes
      ignore_errors: true
      tags: [docker, volumes]

    # ========================================
    # ANALIZA SIECI I POŁĄCZEŃ
    # ========================================
    - name: Sprawdź aktywne połączenia sieciowe
      ansible.builtin.shell: |
        echo "=== LISTENING PORTS ==="
        netstat -tlnp | head -20
        echo "=== ACTIVE CONNECTIONS COUNT ==="
        netstat -an | wc -l
        echo "=== SSH CONNECTIONS ==="
        netstat -tnp | grep :22 | wc -l
        netstat -tnp | grep :1988 | wc -l
      register: network_connections
      tags: [network, connections]

    # ========================================
    # ANALIZA USŁUG SYSTEMOWYCH
    # ========================================
    - name: Sprawdź status kluczowych usług
      ansible.builtin.shell: |
        echo "=== FAILED SERVICES ==="
        systemctl --failed --no-pager
        echo "=== DOCKER SERVICE ==="
        systemctl status docker --no-pager -l | head -10
        echo "=== SSH SERVICE ==="
        systemctl status ssh --no-pager -l | head -10
      register: services_status
      tags: [services, status]

    # ========================================
    # ANALIZA I/O DYSKU
    # ========================================
    - name: Sprawdź statystyki I/O dysku
      ansible.builtin.shell: |
        echo "=== IOSTAT (jeśli dostępne) ==="
        iostat -x 1 3 2>/dev/null || echo "iostat nie jest dostępne"
        echo "=== DISK I/O STATS ==="
        cat /proc/diskstats | head -10
      register: disk_io
      tags: [disk, io]

    # ========================================
    # WYŚWIETLENIE WYNIKÓW
    # ========================================
    - name: Wyświetl raport diagnostyczny
      ansible.builtin.debug:
        msg:
          - "=================================="
          - "RAPORT DIAGNOSTYCZNY SERWERA OVH"
          - "=================================="
          - ""
          - "=== INFORMACJE SYSTEMOWE ==="
          - "{{ system_info.stdout_lines }}"
          - ""
          - "=== WYKORZYSTANIE DYSKU ==="
          - "{{ disk_usage.stdout_lines }}"
          - ""
          - "=== WYKORZYSTANIE INODÓW ==="
          - "{{ inode_usage.stdout_lines }}"
          - ""
          - "=== NAJWIĘKSZE KATALOGI ==="
          - "{{ large_directories.stdout_lines }}"
          - ""
          - "=== NAJWIĘKSZE PLIKI ==="
          - "{{ large_files.stdout_lines }}"
          - ""
          - "=== ROZMIARY LOGÓW ==="
          - "{{ log_sizes.stdout_lines }}"
          - ""
          - "=== PAMIĘĆ ==="
          - "{{ memory_info.stdout_lines }}"
          - ""
          - "=== PROCESY - PAMIĘĆ ==="
          - "{{ memory_processes.stdout_lines }}"
          - ""
          - "=== PROCESY - CPU ==="
          - "{{ cpu_processes.stdout_lines }}"
          - ""
          - "=== DOCKER ZASOBY ==="
          - "{{ docker_resources.stdout_lines }}"
          - ""
          - "=== DOCKER KONTENERY ==="
          - "{{ docker_containers.stdout_lines }}"
          - ""
          - "=== DOCKER WOLUMINY ==="
          - "{{ docker_volumes.stdout_lines }}"
          - ""
          - "=== POŁĄCZENIA SIECIOWE ==="
          - "{{ network_connections.stdout_lines }}"
          - ""
          - "=== STATUS USŁUG ==="
          - "{{ services_status.stdout_lines }}"
          - ""
          - "=== I/O DYSKU ==="
          - "{{ disk_io.stdout_lines }}"
      tags: [summary, report]

    # ========================================
    # ZAPISANIE RAPORTU DO PLIKU
    # ========================================
    - name: Zapisz raport do pliku na serwerze
      ansible.builtin.copy:
        content: |
          RAPORT DIAGNOSTYCZNY SERWERA OVH
          Data: {{ ansible_date_time.iso8601 }}
          Host: {{ inventory_hostname }}
          
          === INFORMACJE SYSTEMOWE ===
          {{ system_info.stdout }}
          
          === WYKORZYSTANIE DYSKU ===
          {{ disk_usage.stdout }}
          
          === WYKORZYSTANIE INODÓW ===
          {{ inode_usage.stdout }}
          
          === NAJWIĘKSZE KATALOGI ===
          {{ large_directories.stdout }}
          
          === NAJWIĘKSZE PLIKI ===
          {{ large_files.stdout }}
          
          === ROZMIARY LOGÓW ===
          {{ log_sizes.stdout }}
          
          === PAMIĘĆ ===
          {{ memory_info.stdout }}
          
          === PROCESY - PAMIĘĆ ===
          {{ memory_processes.stdout }}
          
          === PROCESY - CPU ===
          {{ cpu_processes.stdout }}
          
          === DOCKER ZASOBY ===
          {{ docker_resources.stdout }}
          
          === DOCKER KONTENERY ===
          {{ docker_containers.stdout }}
          
          === DOCKER WOLUMINY ===
          {{ docker_volumes.stdout }}
          
          === POŁĄCZENIA SIECIOWE ===
          {{ network_connections.stdout }}
          
          === STATUS USŁUG ===
          {{ services_status.stdout }}
          
          === I/O DYSKU ===
          {{ disk_io.stdout }}
        dest: "/tmp/diagnostic_report_{{ ansible_date_time.epoch }}.txt"
        mode: '0644'
      tags: [save, report]
