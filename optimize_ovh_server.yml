---
- name: Optymalizacja serwera OVH - czyszczenie dysku i poprawa wydajności
  hosts: simetria-ovh
  become: true
  gather_facts: true
  vars:
    backup_dir: "/tmp/cleanup_backup_{{ ansible_date_time.epoch }}"
    
  tasks:
    # ========================================
    # PRZYGOTOWANIE I BACKUP
    # ========================================
    - name: Utwórz katalog na backup przed czyszczeniem
      ansible.builtin.file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'
      tags: [preparation, backup]

    - name: Sprawdź aktualne wykorzystanie dysku (przed czyszczeniem)
      ansible.builtin.shell: df -h /
      register: disk_before
      tags: [preparation, check]

    - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stan przed czyszczeniem
      ansible.builtin.debug:
        msg:
          - "=== STAN PRZED CZYSZCZENIEM ==="
          - "{{ disk_before.stdout_lines }}"
      tags: [preparation, check]

    # ========================================
    # CZYSZCZENIE DOCKER
    # ========================================
    - name: Zatrzymaj nieużywane kontenery Docker
      ansible.builtin.shell: |
        echo "=== KONTENERY PRZED CZYSZCZENIEM ==="
        docker ps -a
        echo "=== ZATRZYMYWANIE NIEUŻYWANYCH KONTENERÓW ==="
        docker container prune -f
      register: docker_containers_cleanup
      tags: [docker, cleanup]

    - name: Usuń nieużywane obrazy Docker
      ansible.builtin.shell: |
        echo "=== OBRAZY PRZED CZYSZCZENIEM ==="
        docker images
        echo "=== USUWANIE NIEUŻYWANYCH OBRAZÓW ==="
        docker image prune -a -f
      register: docker_images_cleanup
      tags: [docker, cleanup]

    - name: Usuń nieużywane woluminy Docker
      ansible.builtin.shell: |
        echo "=== WOLUMINY PRZED CZYSZCZENIEM ==="
        docker volume ls
        echo "=== USUWANIE NIEUŻYWANYCH WOLUMINÓW ==="
        docker volume prune -f
      register: docker_volumes_cleanup
      tags: [docker, cleanup]

    - name: Usuń cache budowania Docker
      ansible.builtin.shell: |
        echo "=== CACHE PRZED CZYSZCZENIEM ==="
        docker system df
        echo "=== USUWANIE CACHE BUDOWANIA ==="
        docker builder prune -a -f
      register: docker_cache_cleanup
      tags: [docker, cleanup]

    # ========================================
    # CZYSZCZENIE LOGÓW SYSTEMOWYCH
    # ========================================
    - name: Wyczyść stare logi systemowe (starsze niż 7 dni)
      ansible.builtin.shell: |
        echo "=== ROZMIAR LOGÓW PRZED CZYSZCZENIEM ==="
        du -sh /var/log/*
        echo "=== CZYSZCZENIE STARYCH LOGÓW ==="
        find /var/log -name "*.log.*" -mtime +7 -delete
        find /var/log -name "*.gz" -mtime +7 -delete
      register: logs_cleanup
      tags: [logs, cleanup]

    - name: Ogranicz rozmiar journald (pozostaw tylko ostatnie 7 dni)
      ansible.builtin.shell: |
        echo "=== ROZMIAR JOURNAL PRZED CZYSZCZENIEM ==="
        journalctl --disk-usage
        echo "=== CZYSZCZENIE JOURNAL ==="
        journalctl --vacuum-time=7d
        journalctl --vacuum-size=500M
        echo "=== ROZMIAR JOURNAL PO CZYSZCZENIU ==="
        journalctl --disk-usage
      register: journal_cleanup
      tags: [logs, cleanup]

    # ========================================
    # CZYSZCZENIE CACHE I TEMP
    # ========================================
    - name: Wyczyść cache APT
      ansible.builtin.shell: |
        echo "=== ROZMIAR CACHE APT PRZED CZYSZCZENIEM ==="
        du -sh /var/cache/apt/
        echo "=== CZYSZCZENIE CACHE APT ==="
        apt-get clean
        apt-get autoclean
        apt-get autoremove -y
        echo "=== ROZMIAR CACHE APT PO CZYSZCZENIU ==="
        du -sh /var/cache/apt/
      register: apt_cleanup
      tags: [cache, cleanup]

    - name: Wyczyść pliki tymczasowe
      ansible.builtin.shell: |
        echo "=== ROZMIAR /tmp PRZED CZYSZCZENIEM ==="
        du -sh /tmp/
        echo "=== CZYSZCZENIE PLIKÓW TYMCZASOWYCH ==="
        find /tmp -type f -atime +7 -delete 2>/dev/null || true
        find /var/tmp -type f -atime +7 -delete 2>/dev/null || true
        echo "=== ROZMIAR /tmp PO CZYSZCZENIU ==="
        du -sh /tmp/
      register: temp_cleanup
      tags: [temp, cleanup]

    # ========================================
    # CZYSZCZENIE SNAP
    # ========================================
    - name: Usuń stare wersje snap
      ansible.builtin.shell: |
        echo "=== SNAP PRZED CZYSZCZENIEM ==="
        snap list --all
        echo "=== USUWANIE STARYCH WERSJI SNAP ==="
        snap list --all | awk '/disabled/{print $1, $3}' | while read snapname revision; do
            snap remove "$snapname" --revision="$revision" || true
        done
        echo "=== SNAP PO CZYSZCZENIU ==="
        snap list --all
      register: snap_cleanup
      ignore_errors: true
      tags: [snap, cleanup]

    # ========================================
    # OPTYMALIZACJA KONFIGURACJI
    # ========================================
    - name: Skonfiguruj automatyczne czyszczenie logów
      ansible.builtin.lineinfile:
        path: /etc/systemd/journald.conf
        regexp: '^#?SystemMaxUse='
        line: 'SystemMaxUse=500M'
        backup: yes
      notify: restart journald
      tags: [config, optimization]

    - name: Skonfiguruj automatyczne czyszczenie APT
      ansible.builtin.copy:
        content: |
          APT::Periodic::Update-Package-Lists "1";
          APT::Periodic::Download-Upgradeable-Packages "1";
          APT::Periodic::AutocleanInterval "7";
          APT::Periodic::Unattended-Upgrade "1";
        dest: /etc/apt/apt.conf.d/20auto-upgrades
        mode: '0644'
      tags: [config, optimization]

    # ========================================
    # SPRAWDZENIE WYNIKÓW
    # ========================================
    - name: Sprawdź wykorzystanie dysku po czyszczeniu
      ansible.builtin.shell: |
        echo "=== WYKORZYSTANIE DYSKU PO CZYSZCZENIU ==="
        df -h /
        echo "=== NAJWIĘKSZE KATALOGI PO CZYSZCZENIU ==="
        du -h --max-depth=2 / 2>/dev/null | sort -hr | head -10
        echo "=== DOCKER SYSTEM INFO PO CZYSZCZENIU ==="
        docker system df
      register: disk_after
      tags: [check, summary]

    - name: Wyświetl raport optymalizacji
      ansible.builtin.debug:
        msg:
          - "=================================="
          - "RAPORT OPTYMALIZACJI SERWERA OVH"
          - "=================================="
          - ""
          - "=== STAN PRZED CZYSZCZENIEM ==="
          - "{{ disk_before.stdout_lines }}"
          - ""
          - "=== STAN PO CZYSZCZENIU ==="
          - "{{ disk_after.stdout_lines }}"
          - ""
          - "=== DOCKER CLEANUP ==="
          - "{{ docker_images_cleanup.stdout_lines[-5:] | default(['Brak informacji']) }}"
          - ""
          - "=== LOGS CLEANUP ==="
          - "{{ journal_cleanup.stdout_lines[-3:] | default(['Brak informacji']) }}"
          - ""
          - "=== APT CLEANUP ==="
          - "{{ apt_cleanup.stdout_lines[-3:] | default(['Brak informacji']) }}"
      tags: [summary, report]

    - name: Zapisz raport optymalizacji
      ansible.builtin.copy:
        content: |
          RAPORT OPTYMALIZACJI SERWERA OVH
          Data: {{ ansible_date_time.iso8601 }}
          Host: {{ inventory_hostname }}
          
          === STAN PRZED CZYSZCZENIEM ===
          {{ disk_before.stdout }}
          
          === STAN PO CZYSZCZENIU ===
          {{ disk_after.stdout }}
          
          === DOCKER CLEANUP ===
          {{ docker_containers_cleanup.stdout }}
          
          {{ docker_images_cleanup.stdout }}
          
          {{ docker_volumes_cleanup.stdout }}
          
          {{ docker_cache_cleanup.stdout }}
          
          === LOGS CLEANUP ===
          {{ logs_cleanup.stdout }}
          
          {{ journal_cleanup.stdout }}
          
          === APT CLEANUP ===
          {{ apt_cleanup.stdout }}
          
          === TEMP CLEANUP ===
          {{ temp_cleanup.stdout }}
          
          === SNAP CLEANUP ===
          {{ snap_cleanup.stdout }}
        dest: "/tmp/optimization_report_{{ ansible_date_time.epoch }}.txt"
        mode: '0644'
      tags: [save, report]

  handlers:
    - name: restart journald
      ansible.builtin.systemd:
        name: systemd-journald
        state: restarted
