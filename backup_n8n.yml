---
- name: Backup danych n8n przed aktualizacją
  hosts: simetria-ovh
  become: true
  gather_facts: true
  
  vars:
    backup_dir: "/opt/backups/n8n"
    backup_timestamp: "{{ ansible_date_time.epoch }}"
    backup_date: "{{ ansible_date_time.iso8601_basic_short }}"
    
  tasks:
    - name: "=== BACKUP N8N - START ==="
      debug:
        msg:
          - "Rozpoczynam backup n8n na serwerze {{ ansible_hostname }}"
          - "Data: {{ ansible_date_time.iso8601 }}"
          - "Katalog backup: {{ backup_dir }}"
          - "Timestamp: {{ backup_timestamp }}"

    # ==========================================================================
    # PRZYGOTOWANIE KATALOGU BACKUP
    # ==========================================================================
    
    - name: "Stwórz katalog backup"
      file:
        path: "{{ backup_dir }}/{{ backup_date }}"
        state: directory
        mode: '0755'
        owner: root
        group: root

    # ==========================================================================
    # SPRAWDZENIE STANU N8N PRZED BACKUP
    # ==========================================================================
    
    - name: "Sprawdź status kontenerów n8n"
      shell: docker ps --filter "name=n8n" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
      register: n8n_status_before
      ignore_errors: true

    - name: "Wyświetl status n8n przed backup"
      debug:
        msg: "{{ n8n_status_before.stdout_lines | default(['Brak kontenerów n8n']) }}"

    # ==========================================================================
    # BACKUP WOLUMINÓW DOCKER
    # ==========================================================================
    
    - name: "Lista woluminów n8n"
      shell: docker volume ls --filter "name=n8n" --format "{{.Name}}"
      register: n8n_volumes
      ignore_errors: true

    - name: "Wyświetl znalezione woluminy"
      debug:
        msg: "Woluminy do backup: {{ n8n_volumes.stdout_lines | default(['Brak woluminów']) }}"

    - name: "Backup woluminów n8n"
      shell: |
        for volume in {{ n8n_volumes.stdout_lines | join(' ') }}; do
          echo "=== Backup woluminu: $volume ==="
          docker run --rm \
            -v $volume:/source:ro \
            -v {{ backup_dir }}/{{ backup_date }}:/backup \
            alpine:latest \
            tar czf /backup/${volume}_{{ backup_timestamp }}.tar.gz -C /source .
          
          if [ $? -eq 0 ]; then
            echo "✓ Backup $volume zakończony pomyślnie"
            ls -lh {{ backup_dir }}/{{ backup_date }}/${volume}_{{ backup_timestamp }}.tar.gz
          else
            echo "✗ Błąd podczas backup $volume"
          fi
        done
      register: volume_backup_result
      when: n8n_volumes.stdout_lines is defined and n8n_volumes.stdout_lines | length > 0

    - name: "Wyświetl wyniki backup woluminów"
      debug:
        msg: "{{ volume_backup_result.stdout_lines | default(['Brak backup woluminów']) }}"

    # ==========================================================================
    # BACKUP KONFIGURACJI DOCKER-COMPOSE
    # ==========================================================================
    
    - name: "Znajdź pliki docker-compose z n8n"
      shell: find /home /opt /root -name "docker-compose.yml" -exec grep -l "n8n" {} \; 2>/dev/null
      register: compose_files
      ignore_errors: true

    - name: "Backup plików docker-compose"
      shell: |
        for file in {{ compose_files.stdout_lines | join(' ') }}; do
          if [ -f "$file" ]; then
            echo "=== Backup: $file ==="
            cp "$file" "{{ backup_dir }}/{{ backup_date }}/docker-compose_$(basename $(dirname $file))_{{ backup_timestamp }}.yml"
            echo "✓ Skopiowano: $file"
          fi
        done
      register: compose_backup_result
      when: compose_files.stdout_lines is defined and compose_files.stdout_lines | length > 0

    - name: "Wyświetl wyniki backup docker-compose"
      debug:
        msg: "{{ compose_backup_result.stdout_lines | default(['Brak plików docker-compose']) }}"

    # ==========================================================================
    # BACKUP ZMIENNYCH ŚRODOWISKOWYCH
    # ==========================================================================
    
    - name: "Backup zmiennych środowiskowych n8n"
      shell: |
        echo "=== Zmienne środowiskowe kontenerów n8n ===" > {{ backup_dir }}/{{ backup_date }}/n8n_env_{{ backup_timestamp }}.txt
        for container in $(docker ps --filter "name=n8n" --format "{{.Names}}"); do
          echo "--- Kontener: $container ---" >> {{ backup_dir }}/{{ backup_date }}/n8n_env_{{ backup_timestamp }}.txt
          docker inspect $container --format='{{range .Config.Env}}{{println .}}{{end}}' >> {{ backup_dir }}/{{ backup_date }}/n8n_env_{{ backup_timestamp }}.txt
          echo "" >> {{ backup_dir }}/{{ backup_date }}/n8n_env_{{ backup_timestamp }}.txt
        done
      register: env_backup_result
      ignore_errors: true

    # ==========================================================================
    # BACKUP INFORMACJI O OBRAZACH
    # ==========================================================================
    
    - name: "Backup informacji o obrazach Docker"
      shell: |
        echo "=== Obrazy Docker n8n ===" > {{ backup_dir }}/{{ backup_date }}/n8n_images_{{ backup_timestamp }}.txt
        docker images --filter "reference=*n8n*" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}" >> {{ backup_dir }}/{{ backup_date }}/n8n_images_{{ backup_timestamp }}.txt
      register: images_backup_result
      ignore_errors: true

    # ==========================================================================
    # PODSUMOWANIE BACKUP
    # ==========================================================================
    
    - name: "Lista plików backup"
      shell: ls -la {{ backup_dir }}/{{ backup_date }}/
      register: backup_files
      ignore_errors: true

    - name: "Sprawdź rozmiar backup"
      shell: du -sh {{ backup_dir }}/{{ backup_date }}/
      register: backup_size
      ignore_errors: true

    - name: "=== PODSUMOWANIE BACKUP ==="
      debug:
        msg:
          - "Backup n8n zakończony"
          - "Lokalizacja: {{ backup_dir }}/{{ backup_date }}"
          - "Rozmiar: {{ backup_size.stdout | default('Nieznany') }}"
          - "Pliki:"
          - "{{ backup_files.stdout_lines | default(['Brak plików']) }}"
          - ""
          - "NASTĘPNY KROK: Sprawdzenie konfiguracji n8n"
