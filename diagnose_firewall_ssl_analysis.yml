---
- name: "<PERSON><PERSON><PERSON> konfiguracji firewall, fail2ban i certyfikatów SSL na serwerze simetria-hetzner001"
  hosts: simetria-hetzner001
  become: yes
  gather_facts: yes
  vars:
    test_timestamp: "{{ ansible_date_time.iso8601 }}"
    baserow_domain: "baserow.simetria.pl"
    
  tasks:
    - name: "Wyświetl nagłówek analizy"
      debug:
        msg: |
          =================================================================
          ANALIZA KONFIGURACJI FIREWALL, FAIL2BAN I CERTYFIKATÓW SSL
          =================================================================
          
          Serwer: {{ inventory_hostname }} ({{ ansible_host }})
          Domena: {{ baserow_domain }}
          Data: {{ test_timestamp }}
          
          ZAKRES ANALIZY:
          1. Konfiguracja firewall (UFW/iptables)
          2. Konfiguracja fail2ban dla nginx
          3. Weryfikacja certyfikatów SSL
          4. Analiza OCSP stapling
          
          =================================================================

    # ==========================================================================
    # ANALIZA KONFIGURACJI FIREWALL
    # ==========================================================================
    
    - name: "TEST: Sprawdzenie statusu UFW"
      shell: |
        echo "=== STATUS UFW ==="
        ufw --force status verbose 2>/dev/null || echo "UFW nie jest dostępne lub nie jest zainstalowany"
        echo ""
        echo "=== REGUŁY UFW NUMBERED ==="
        ufw --force status numbered 2>/dev/null || echo "Nie można pobrać numerowanych reguł UFW"
      register: ufw_status_check
      ignore_errors: yes

    - name: "Wyświetl status UFW"
      debug:
        msg: |
          TEST: UFW Status and Rules Check
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ 'UFW aktywny' if 'Status: active' in ufw_status_check.stdout else 'UFW nieaktywny lub niedostępny' }}
          STATUS: {{ 'SUCCESS' if 'Status: active' in ufw_status_check.stdout else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ ufw_status_check.stdout }}

    - name: "TEST: Sprawdzenie reguł iptables dla portów 80 i 443"
      shell: |
        echo "=== REGUŁY IPTABLES - INPUT ==="
        iptables -L INPUT -n -v --line-numbers | grep -E "80|443" || echo "Brak reguł dla portów 80/443 w INPUT"
        echo ""
        echo "=== REGUŁY IPTABLES - FORWARD ==="
        iptables -L FORWARD -n -v --line-numbers | grep -E "80|443" || echo "Brak reguł dla portów 80/443 w FORWARD"
        echo ""
        echo "=== WSZYSTKIE REGUŁY ACCEPT/DROP dla portów HTTP/HTTPS ==="
        iptables -L -n | grep -E "(ACCEPT|DROP|REJECT).*dpt:(80|443)" || echo "Brak specyficznych reguł ACCEPT/DROP dla portów 80/443"
      register: iptables_rules_check
      ignore_errors: yes

    - name: "Wyświetl reguły iptables"
      debug:
        msg: |
          TEST: Iptables Rules for Ports 80/443
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono reguły iptables dla portów HTTP/HTTPS
          STATUS: {{ 'SUCCESS' if iptables_rules_check.rc == 0 else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ iptables_rules_check.stdout }}

    - name: "TEST: Sprawdzenie blokad IP w firewall"
      shell: |
        echo "=== ZABLOKOWANE IP w UFW ==="
        ufw --force status | grep -E "DENY|REJECT" || echo "Brak zablokowanych IP w UFW"
        echo ""
        echo "=== ZABLOKOWANE IP w iptables ==="
        iptables -L -n | grep -E "DROP|REJECT" | grep -v "127.0.0.1" | head -20 || echo "Brak zablokowanych IP w iptables"
        echo ""
        echo "=== SPRAWDZENIE WŁASNEGO IP ==="
        echo "IP połączenia SSH: $SSH_CLIENT" | awk '{print $1}' || echo "Nie można określić IP SSH"
        MY_IP=$(echo $SSH_CLIENT | awk '{print $1}')
        if [ ! -z "$MY_IP" ]; then
          echo "Sprawdzanie czy $MY_IP jest zablokowany:"
          iptables -L -n | grep "$MY_IP" || echo "IP $MY_IP nie jest zablokowany w iptables"
        fi
      register: blocked_ips_check
      ignore_errors: yes

    - name: "Wyświetl sprawdzenie blokad IP"
      debug:
        msg: |
          TEST: Blocked IP Addresses Check
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono zablokowane adresy IP
          STATUS: {{ 'SUCCESS' if blocked_ips_check.rc == 0 else 'PARTIAL' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ blocked_ips_check.stdout }}

    # ==========================================================================
    # ANALIZA KONFIGURACJI FAIL2BAN
    # ==========================================================================

    - name: "TEST: Sprawdzenie instalacji i statusu fail2ban"
      shell: |
        echo "=== STATUS USŁUGI FAIL2BAN ==="
        systemctl is-active fail2ban 2>/dev/null || echo "Fail2ban nie jest aktywny"
        systemctl is-enabled fail2ban 2>/dev/null || echo "Fail2ban nie jest włączony automatycznie"
        echo ""
        echo "=== WERSJA FAIL2BAN ==="
        fail2ban-server --version 2>/dev/null || echo "Fail2ban nie jest zainstalowany"
        echo ""
        echo "=== PLIKI KONFIGURACYJNE FAIL2BAN ==="
        find /etc/fail2ban -name "*.conf" -o -name "*.local" | head -10 || echo "Brak plików konfiguracyjnych fail2ban"
      register: fail2ban_status_check
      ignore_errors: yes

    - name: "Wyświetl status fail2ban"
      debug:
        msg: |
          TEST: Fail2ban Installation and Status
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ 'Fail2ban aktywny' if 'active' in fail2ban_status_check.stdout else 'Fail2ban nieaktywny lub niezainstalowany' }}
          STATUS: {{ 'SUCCESS' if 'active' in fail2ban_status_check.stdout else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ fail2ban_status_check.stdout }}

    - name: "TEST: Sprawdzenie konfiguracji jail dla nginx"
      shell: |
        echo "=== AKTYWNE JAIL W FAIL2BAN ==="
        fail2ban-client status 2>/dev/null || echo "Nie można pobrać statusu jail"
        echo ""
        echo "=== KONFIGURACJA NGINX JAIL ==="
        find /etc/fail2ban/jail.d -name "*nginx*" -exec cat {} \; 2>/dev/null || echo "Brak konfiguracji nginx jail"
        find /etc/fail2ban/jail.d -name "*baserow*" -exec cat {} \; 2>/dev/null || echo "Brak konfiguracji baserow jail"
        echo ""
        echo "=== STATUS NGINX-RELATED JAIL ==="
        fail2ban-client status nginx-http-auth 2>/dev/null || echo "Jail nginx-http-auth nie jest aktywny"
        fail2ban-client status nginx-limit-req 2>/dev/null || echo "Jail nginx-limit-req nie jest aktywny"
        fail2ban-client status nginx-badbots 2>/dev/null || echo "Jail nginx-badbots nie jest aktywny"
      register: fail2ban_nginx_check
      ignore_errors: yes

    - name: "Wyświetl konfigurację fail2ban dla nginx"
      debug:
        msg: |
          TEST: Fail2ban Nginx Configuration
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono konfigurację jail dla nginx
          STATUS: {{ 'SUCCESS' if 'nginx' in fail2ban_nginx_check.stdout else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ fail2ban_nginx_check.stdout }}

    - name: "TEST: Sprawdzenie logów fail2ban"
      shell: |
        echo "=== OSTATNIE WPISY W LOGU FAIL2BAN ==="
        tail -50 /var/log/fail2ban.log 2>/dev/null || echo "Brak dostępu do logów fail2ban"
        echo ""
        echo "=== ZABLOKOWANE IP W FAIL2BAN ==="
        fail2ban-client status 2>/dev/null | grep "Currently banned:" || echo "Brak informacji o zablokowanych IP"
        echo ""
        echo "=== STATYSTYKI JAIL ==="
        for jail in $(fail2ban-client status 2>/dev/null | grep "Jail list:" | cut -d: -f2 | tr ',' '\n'); do
          jail=$(echo $jail | xargs)
          if [ ! -z "$jail" ]; then
            echo "--- Jail: $jail ---"
            fail2ban-client status $jail 2>/dev/null || echo "Nie można pobrać statusu jail $jail"
          fi
        done
      register: fail2ban_logs_check
      ignore_errors: yes

    - name: "Wyświetl logi fail2ban"
      debug:
        msg: |
          TEST: Fail2ban Logs and Statistics
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono logi i statystyki fail2ban
          STATUS: {{ 'SUCCESS' if fail2ban_logs_check.rc == 0 else 'PARTIAL' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ fail2ban_logs_check.stdout }}

    # ==========================================================================
    # WERYFIKACJA CERTYFIKATÓW SSL
    # ==========================================================================

    - name: "TEST: Sprawdzenie ważności certyfikatu SSL"
      shell: |
        echo "=== INFORMACJE O CERTYFIKACIE SSL ==="
        if [ -f "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" ]; then
          echo "Ścieżka certyfikatu: /etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem"
          echo ""
          echo "=== SZCZEGÓŁY CERTYFIKATU ==="
          openssl x509 -in "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" -text -noout | grep -E "(Subject:|Issuer:|Not Before|Not After|DNS:|Subject Alternative Name)" || echo "Błąd odczytu certyfikatu"
          echo ""
          echo "=== DATA WAŻNOŚCI ==="
          openssl x509 -in "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" -enddate -noout || echo "Nie można pobrać daty ważności"
          echo ""
          echo "=== POZOSTAŁY CZAS WAŻNOŚCI ==="
          openssl x509 -in "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" -checkend 0 && echo "Certyfikat jest ważny" || echo "Certyfikat wygasł"
          openssl x509 -in "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" -checkend 2592000 && echo "Certyfikat ważny przez kolejne 30 dni" || echo "Certyfikat wygaśnie w ciągu 30 dni"
        else
          echo "BŁĄD: Certyfikat SSL nie został znaleziony w /etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem"
          echo ""
          echo "=== SPRAWDZENIE ALTERNATYWNYCH LOKALIZACJI ==="
          find /etc -name "*{{ baserow_domain }}*" -type f 2>/dev/null | grep -E "\.(crt|pem)$" || echo "Nie znaleziono certyfikatów w /etc"
        fi
      register: ssl_cert_check
      ignore_errors: yes

    - name: "Wyświetl informacje o certyfikacie SSL"
      debug:
        msg: |
          TEST: SSL Certificate Validity Check
          EXECUTED: {{ test_timestamp }}
          RESULT: {{ 'Certyfikat znaleziony i sprawdzony' if '/etc/letsencrypt/live' in ssl_cert_check.stdout else 'Certyfikat nie znaleziony' }}
          STATUS: {{ 'SUCCESS' if 'ważny' in ssl_cert_check.stdout else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ ssl_cert_check.stdout }}

    - name: "TEST: Sprawdzenie konfiguracji SSL w nginx"
      shell: |
        echo "=== KONFIGURACJA SSL W NGINX ==="
        nginx -T 2>/dev/null | grep -A 5 -B 5 "ssl_certificate" || echo "Brak konfiguracji SSL w nginx"
        echo ""
        echo "=== PROTOKOŁY SSL/TLS ==="
        nginx -T 2>/dev/null | grep "ssl_protocols" || echo "Brak konfiguracji protokołów SSL"
        echo ""
        echo "=== SZYFRY SSL ==="
        nginx -T 2>/dev/null | grep "ssl_ciphers" || echo "Brak konfiguracji szyfrów SSL"
        echo ""
        echo "=== SPRAWDZENIE ŚCIEŻEK CERTYFIKATÓW ==="
        nginx -T 2>/dev/null | grep -E "ssl_(certificate|certificate_key)" | while read line; do
          path=$(echo "$line" | awk '{print $2}' | tr -d ';')
          if [ -f "$path" ]; then
            echo "✓ Znaleziono: $path"
            ls -la "$path"
          else
            echo "✗ Brak pliku: $path"
          fi
        done
      register: nginx_ssl_check
      ignore_errors: yes

    - name: "Wyświetl konfigurację SSL w nginx"
      debug:
        msg: |
          TEST: Nginx SSL Configuration Check
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono konfigurację SSL w nginx
          STATUS: {{ 'SUCCESS' if 'ssl_certificate' in nginx_ssl_check.stdout else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ nginx_ssl_check.stdout }}

    - name: "TEST: Sprawdzenie OCSP stapling"
      shell: |
        echo "=== KONFIGURACJA OCSP STAPLING W NGINX ==="
        nginx -T 2>/dev/null | grep -E "ssl_stapling|ssl_trusted_certificate" || echo "Brak konfiguracji OCSP stapling"
        echo ""
        echo "=== TEST OCSP STAPLING ==="
        echo | openssl s_client -connect {{ baserow_domain }}:443 -servername {{ baserow_domain }} -status -verify_return_error 2>/dev/null | grep -A 10 "OCSP response:" || echo "Brak odpowiedzi OCSP lub błąd połączenia"
        echo ""
        echo "=== SPRAWDZENIE DOSTĘPNOŚCI OCSP RESPONDER ==="
        OCSP_URL=$(openssl x509 -in "/etc/letsencrypt/live/{{ baserow_domain }}/fullchain.pem" -text -noout 2>/dev/null | grep -A 1 "Authority Information Access" | grep OCSP | awk -F'URI:' '{print $2}' | tr -d ' ')
        if [ ! -z "$OCSP_URL" ]; then
          echo "OCSP URL: $OCSP_URL"
          curl -s -I -m 5 "$OCSP_URL" | head -3 || echo "Nie można połączyć się z OCSP responder"
        else
          echo "Nie znaleziono URL OCSP w certyfikacie"
        fi
      register: ocsp_check
      ignore_errors: yes

    - name: "Wyświetl sprawdzenie OCSP stapling"
      debug:
        msg: |
          TEST: OCSP Stapling Configuration Check
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono konfigurację OCSP stapling
          STATUS: {{ 'SUCCESS' if 'ssl_stapling' in ocsp_check.stdout else 'PARTIAL' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ ocsp_check.stdout }}

    - name: "TEST: Test SSL/TLS z zewnętrznej perspektywy"
      shell: |
        echo "=== TEST POŁĄCZENIA SSL ==="
        echo | openssl s_client -connect {{ baserow_domain }}:443 -servername {{ baserow_domain }} 2>/dev/null | grep -E "(Verify return code|subject=|issuer=)" || echo "Błąd połączenia SSL"
        echo ""
        echo "=== SPRAWDZENIE PROTOKOŁÓW TLS ==="
        for protocol in tls1_2 tls1_3; do
          echo "Test $protocol:"
          echo | openssl s_client -connect {{ baserow_domain }}:443 -servername {{ baserow_domain }} -$protocol 2>/dev/null | grep "Protocol" || echo "$protocol niedostępny"
        done
        echo ""
        echo "=== SPRAWDZENIE NAGŁÓWKÓW BEZPIECZEŃSTWA ==="
        curl -s -I -m 10 https://{{ baserow_domain }}/ | grep -iE "(strict-transport|x-frame|x-content-type)" || echo "Brak nagłówków bezpieczeństwa lub błąd połączenia"
      register: external_ssl_check
      ignore_errors: yes

    - name: "Wyświetl test SSL z zewnętrznej perspektywy"
      debug:
        msg: |
          TEST: External SSL/TLS Connection Test
          EXECUTED: {{ test_timestamp }}
          RESULT: Sprawdzono połączenie SSL z zewnętrznej perspektywy
          STATUS: {{ 'SUCCESS' if 'Verify return code: 0' in external_ssl_check.stdout else 'FAILED' }}
          CHANGES: Nie
          
          SZCZEGÓŁY:
          {{ external_ssl_check.stdout }}

    # ==========================================================================
    # PODSUMOWANIE ANALIZY
    # ==========================================================================

    - name: "PODSUMOWANIE: Analiza konfiguracji firewall, fail2ban i SSL"
      debug:
        msg: |
          ==========================================
          PODSUMOWANIE ANALIZY KONFIGURACJI
          ==========================================
          
          Data wykonania: {{ test_timestamp }}
          Serwer: {{ inventory_hostname }} ({{ ansible_host }})
          Domena: {{ baserow_domain }}
          
          WYNIKI ANALIZY:
          
          1. FIREWALL (UFW/iptables):
             - Status UFW: {{ 'Aktywny' if 'Status: active' in ufw_status_check.stdout else 'Nieaktywny' }}
             - Porty 80/443: {{ 'Skonfigurowane' if '80\|443' in iptables_rules_check.stdout else 'Wymagają sprawdzenia' }}
             - Blokady IP: {{ 'Sprawdzone' if blocked_ips_check.rc == 0 else 'Błąd sprawdzenia' }}
          
          2. FAIL2BAN:
             - Status usługi: {{ 'Aktywny' if 'active' in fail2ban_status_check.stdout else 'Nieaktywny' }}
             - Konfiguracja nginx: {{ 'Znaleziona' if 'nginx' in fail2ban_nginx_check.stdout else 'Brak lub błąd' }}
             - Aktywne jail: {{ 'Sprawdzone' if fail2ban_logs_check.rc == 0 else 'Błąd sprawdzenia' }}
          
          3. CERTYFIKATY SSL:
             - Certyfikat główny: {{ 'Znaleziony' if '/etc/letsencrypt/live' in ssl_cert_check.stdout else 'Brak' }}
             - Konfiguracja nginx: {{ 'Poprawna' if 'ssl_certificate' in nginx_ssl_check.stdout else 'Błędna' }}
             - OCSP stapling: {{ 'Skonfigurowane' if 'ssl_stapling' in ocsp_check.stdout else 'Brak' }}
             - Test zewnętrzny: {{ 'Poprawny' if 'Verify return code: 0' in external_ssl_check.stdout else 'Błędny' }}
          
          GŁÓWNE PROBLEMY KONFIGURACYJNE:
          {% if 'Status: active' not in ufw_status_check.stdout %}
          - UFW nie jest aktywny - może powodować problemy z dostępem
          {% endif %}
          {% if 'active' not in fail2ban_status_check.stdout %}
          - Fail2ban nie jest aktywny - brak ochrony przed atakami
          {% endif %}
          {% if '/etc/letsencrypt/live' not in ssl_cert_check.stdout %}
          - Certyfikat SSL nie został znaleziony - HTTPS nie będzie działać
          {% endif %}
          {% if 'ssl_certificate' not in nginx_ssl_check.stdout %}
          - Nginx nie ma poprawnej konfiguracji SSL
          {% endif %}
          {% if 'Verify return code: 0' not in external_ssl_check.stdout %}
          - Problem z zewnętrznym dostępem SSL - może powodować ERR_CONNECTION_REFUSED
          {% endif %}
          
          DIAGNOZA ZAKOŃCZONA - BEZ WPROWADZANIA ZMIAN
          
          Następne kroki: Analiza wyników w celu identyfikacji głównych
          przyczyn problemów z ERR_CONNECTION_REFUSED.