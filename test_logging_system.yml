---
- name: Test logging system integration
  hosts: baserow_servers
  become: yes
  gather_facts: yes
  
  vars:
    ansible_ssh_timeout: 30
    ansible_command_timeout: 300
    log_test_dir: /tmp/test_logging
    
  tasks:
    - name: Ensure Docker is running
      systemd:
        name: docker
        state: started
        enabled: yes
        
    - name: Create test logging directory
      file:
        path: "{{ log_test_dir }}"
        state: directory
        mode: '0755'
        
    - name: Test log file creation
      shell: |
        # Create test log files
        echo "$(date '+%Y-%m-%d %H:%M:%S') INFO Test log entry 1" > {{ log_test_dir }}/test_app.log
        echo "$(date '+%Y-%m-%d %H:%M:%S') ERROR Test error entry" >> {{ log_test_dir }}/test_app.log
        echo "$(date '+%Y-%m-%d %H:%M:%S') DEBUG Test debug entry" >> {{ log_test_dir }}/test_app.log
        
        # Create nginx-like log
        echo "127.0.0.1 - - [$(date '+%d/%b/%Y:%H:%M:%S %z')] \"GET /test HTTP/1.1\" 200 1234 \"-\" \"Test-Agent/1.0\"" > {{ log_test_dir }}/test_nginx.log
        
        # Create postgres-like log
        echo "$(date '+%Y-%m-%d %H:%M:%S') UTC [123]: [1-1] LOG: test database connection" > {{ log_test_dir }}/test_postgres.log
        
        echo "✅ Test log files created successfully"
      register: log_creation
      
    - name: Display log creation results
      debug:
        var: log_creation.stdout_lines
        
    - name: Test log parsing and filtering
      shell: |
        cd {{ log_test_dir }}
        
        # Test log level filtering
        ERROR_COUNT=$(grep -c "ERROR" test_app.log)
        INFO_COUNT=$(grep -c "INFO" test_app.log)
        DEBUG_COUNT=$(grep -c "DEBUG" test_app.log)
        
        echo "📊 Error logs: $ERROR_COUNT"
        echo "📊 Info logs: $INFO_COUNT"
        echo "📊 Debug logs: $DEBUG_COUNT"
        
        if [ $ERROR_COUNT -eq 1 ] && [ $INFO_COUNT -eq 1 ] && [ $DEBUG_COUNT -eq 1 ]; then
          echo "✅ Log level filtering: PASSED"
        else
          echo "❌ Log level filtering: FAILED"
          exit 1
        fi
        
        # Test nginx log parsing
        if grep -q "GET /test HTTP/1.1" test_nginx.log; then
          echo "✅ Nginx log format: PASSED"
        else
          echo "❌ Nginx log format: FAILED"
          exit 1
        fi
        
        # Test postgres log parsing
        if grep -q "LOG: test database connection" test_postgres.log; then
          echo "✅ Postgres log format: PASSED"
        else
          echo "❌ Postgres log format: FAILED"
          exit 1
        fi
      register: log_parsing
      
    - name: Display log parsing results
      debug:
        var: log_parsing.stdout_lines
        
    - name: Test log rotation simulation
      shell: |
        cd {{ log_test_dir }}
        
        # Simulate log rotation
        cp test_app.log test_app.log.1
        echo "$(date '+%Y-%m-%d %H:%M:%S') INFO Log after rotation" > test_app.log
        
        # Compress old log
        gzip test_app.log.1
        
        # Check if rotation worked
        if [ -f test_app.log.1.gz ] && [ -f test_app.log ]; then
          echo "✅ Log rotation simulation: PASSED"
        else
          echo "❌ Log rotation simulation: FAILED"
          exit 1
        fi
        
        # Check if new log is being written
        if grep -q "Log after rotation" test_app.log; then
          echo "✅ Log writing after rotation: PASSED"
        else
          echo "❌ Log writing after rotation: FAILED"
          exit 1
        fi
      register: log_rotation
      
    - name: Display log rotation results
      debug:
        var: log_rotation.stdout_lines
        
    - name: Test log aggregation simulation
      shell: |
        cd {{ log_test_dir }}
        
        # Simulate log aggregation
        {
          echo "=== APPLICATION LOGS ==="
          cat test_app.log
          echo ""
          echo "=== NGINX LOGS ==="
          cat test_nginx.log
          echo ""
          echo "=== POSTGRES LOGS ==="
          cat test_postgres.log
        } > aggregated_logs.txt
        
        # Check if aggregation worked
        if grep -q "APPLICATION LOGS" aggregated_logs.txt && \
           grep -q "NGINX LOGS" aggregated_logs.txt && \
           grep -q "POSTGRES LOGS" aggregated_logs.txt; then
          echo "✅ Log aggregation: PASSED"
        else
          echo "❌ Log aggregation: FAILED"
          exit 1
        fi
        
        # Count total log entries
        TOTAL_ENTRIES=$(grep -c "$(date '+%Y-%m-%d')" aggregated_logs.txt || echo "0")
        echo "📊 Total log entries aggregated: $TOTAL_ENTRIES"
        
        if [ $TOTAL_ENTRIES -gt 0 ]; then
          echo "✅ Log entry counting: PASSED"
        else
          echo "❌ Log entry counting: FAILED"
          exit 1
        fi
      register: log_aggregation
      
    - name: Display log aggregation results
      debug:
        var: log_aggregation.stdout_lines
        
    - name: Test log monitoring simulation
      shell: |
        cd {{ log_test_dir }}
        
        # Simulate log monitoring - check for errors
        ERROR_LOGS=$(grep -n "ERROR" test_app.log || echo "")
        if [ -n "$ERROR_LOGS" ]; then
          echo "🚨 Error detected in logs:"
          echo "$ERROR_LOGS"
          echo "✅ Error detection: PASSED"
        else
          echo "✅ No errors found in monitoring (this is also valid)"
        fi
        
        # Check log file sizes
        APP_LOG_SIZE=$(du -h test_app.log | cut -f1)
        NGINX_LOG_SIZE=$(du -h test_nginx.log | cut -f1)
        POSTGRES_LOG_SIZE=$(du -h test_postgres.log | cut -f1)
        
        echo "📊 Application log size: $APP_LOG_SIZE"
        echo "📊 Nginx log size: $NGINX_LOG_SIZE"
        echo "📊 Postgres log size: $POSTGRES_LOG_SIZE"
        
        # Test log content verification
        TOTAL_LINES=$(wc -l < test_app.log)
        echo "📊 Total lines in app log: $TOTAL_LINES"
        
        if [ $TOTAL_LINES -gt 0 ]; then
          echo "✅ Log monitoring simulation: PASSED"
        else
          echo "❌ Log monitoring simulation: FAILED"
          exit 1
        fi
      register: log_monitoring
      
    - name: Display log monitoring results
      debug:
        var: log_monitoring.stdout_lines
        
    - name: Test log cleanup simulation
      shell: |
        cd {{ log_test_dir }}
        
        # Simulate cleanup of old logs (older than 7 days)
        find . -name "*.log.*" -type f -mtime +7 -delete 2>/dev/null || true
        
        # Create a very old log file for testing
        touch -t 202001010000 old_test.log
        
        # Test cleanup
        if [ -f old_test.log ]; then
          rm old_test.log
          echo "✅ Old log cleanup: PASSED"
        else
          echo "❌ Old log cleanup: FAILED"
          exit 1
        fi
        
        # Check remaining log files
        REMAINING_LOGS=$(ls -1 *.log 2>/dev/null | wc -l)
        echo "📊 Remaining log files: $REMAINING_LOGS"
        
        if [ $REMAINING_LOGS -gt 0 ]; then
          echo "✅ Log cleanup simulation: PASSED"
        else
          echo "❌ Log cleanup simulation: FAILED"
          exit 1
        fi
      register: log_cleanup
      
    - name: Display log cleanup results
      debug:
        var: log_cleanup.stdout_lines
        
    - name: Display final test results
      debug:
        msg:
          - "🎯 WYNIKI TESTÓW SYSTEMU LOGOWANIA:"
          - "✅ Test tworzenia logów: PASSED"
          - "✅ Test parsowania logów: PASSED"
          - "✅ Test rotacji logów: PASSED"
          - "✅ Test agregacji logów: PASSED"
          - "✅ Test monitoringu logów: PASSED"
          - "✅ Test czyszczenia logów: PASSED"
          - "📊 Wszystkie testy systemu logowania: UKOŃCZONE POMYŚLNIE"
          
    - name: Cleanup test files
      file:
        path: "{{ log_test_dir }}"
        state: absent
      ignore_errors: yes