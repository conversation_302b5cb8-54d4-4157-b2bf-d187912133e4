---
- name: Audit Firewall Configuration
  hosts: serwery_legacy  # Uruchamiamy to tylko na starych serwerach!
  become: true

  tasks:
    - name: Gather UFW information
      community.general.ufw_info:
      register: ufw_status # Rejestrujemy wynik w zmiennej o nazwie 'ufw_status'

    - name: Display current UFW rules
      ansible.builtin.debug:
        msg: "Firewall na {{ inventory_hostname }} ma następujące reguły: {{ ufw_status.rules }}"
      when: ufw_status.status.status == "active" # Wy<PERSON><PERSON><PERSON><PERSON> tylko, jeśli firewall jest aktywny

    - name: Display warning if firewall is inactive
      ansible.builtin.debug:
        msg: "UWAGA: Firewall na {{ inventory_hostname }} jest nieaktywny!"
      when: ufw_status.status.status == "inactive"
