#!/bin/bash

# ========================================
# SKRYPT AWARYJNEGO CZYSZCZENIA SERWERA OVH
# ========================================
# Ten skrypt należy uruchomić bezpośrednio na serwerze jako root
# sudo bash emergency_cleanup.sh

echo "=================================="
echo "AWARYJNE CZYSZCZENIE SERWERA OVH"
echo "=================================="
echo "Data: $(date)"
echo ""

# Sprawdź aktualne wykorzystanie dysku
echo "=== STAN PRZED CZYSZCZENIEM ==="
df -h /
echo ""

# ========================================
# 1. SZYBKIE CZYSZCZENIE DOCKER
# ========================================
echo "=== 1. CZYSZCZENIE DOCKER ==="
echo "Sprawdzanie Docker..."
if command -v docker &> /dev/null; then
    echo "Docker system info przed czyszczeniem:"
    docker system df
    echo ""
    
    echo "Usuwanie nieużywanych kontenerów..."
    docker container prune -f
    
    echo "Usuwanie nieużywanych obrazów..."
    docker image prune -a -f
    
    echo "Usuwanie nieużywanych woluminów..."
    docker volume prune -f
    
    echo "Usuwanie cache budowania..."
    docker builder prune -a -f
    
    echo "Docker system info po czyszczeniu:"
    docker system df
else
    echo "Docker nie jest zainstalowany lub niedostępny"
fi
echo ""

# ========================================
# 2. CZYSZCZENIE LOGÓW SYSTEMOWYCH
# ========================================
echo "=== 2. CZYSZCZENIE LOGÓW ==="
echo "Rozmiar logów przed czyszczeniem:"
du -sh /var/log/ 2>/dev/null || echo "Nie można sprawdzić rozmiaru logów"

echo "Czyszczenie starych logów (starsze niż 7 dni)..."
find /var/log -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
find /var/log -name "*.gz" -mtime +7 -delete 2>/dev/null || true

echo "Sprawdzanie rozmiaru journal..."
journalctl --disk-usage

echo "Czyszczenie journal (pozostaw tylko ostatnie 3 dni)..."
journalctl --vacuum-time=3d
journalctl --vacuum-size=200M

echo "Rozmiar journal po czyszczeniu:"
journalctl --disk-usage
echo ""

# ========================================
# 3. CZYSZCZENIE APT CACHE
# ========================================
echo "=== 3. CZYSZCZENIE APT CACHE ==="
echo "Rozmiar cache APT przed czyszczeniem:"
du -sh /var/cache/apt/ 2>/dev/null || echo "Nie można sprawdzić cache APT"

echo "Czyszczenie cache APT..."
apt-get clean
apt-get autoclean
apt-get autoremove -y

echo "Rozmiar cache APT po czyszczeniu:"
du -sh /var/cache/apt/ 2>/dev/null || echo "Nie można sprawdzić cache APT"
echo ""

# ========================================
# 4. CZYSZCZENIE PLIKÓW TYMCZASOWYCH
# ========================================
echo "=== 4. CZYSZCZENIE PLIKÓW TYMCZASOWYCH ==="
echo "Rozmiar /tmp przed czyszczeniem:"
du -sh /tmp/ 2>/dev/null || echo "Nie można sprawdzić /tmp"

echo "Czyszczenie starych plików tymczasowych..."
find /tmp -type f -atime +3 -delete 2>/dev/null || true
find /var/tmp -type f -atime +3 -delete 2>/dev/null || true

echo "Rozmiar /tmp po czyszczeniu:"
du -sh /tmp/ 2>/dev/null || echo "Nie można sprawdzić /tmp"
echo ""

# ========================================
# 5. CZYSZCZENIE SNAP
# ========================================
echo "=== 5. CZYSZCZENIE SNAP ==="
if command -v snap &> /dev/null; then
    echo "Usuwanie starych wersji snap..."
    snap list --all | awk '/disabled/{print $1, $3}' | while read snapname revision; do
        echo "Usuwanie $snapname revision $revision"
        snap remove "$snapname" --revision="$revision" 2>/dev/null || true
    done
else
    echo "Snap nie jest zainstalowany"
fi
echo ""

# ========================================
# 6. ZNAJDŹ NAJWIĘKSZE PLIKI
# ========================================
echo "=== 6. NAJWIĘKSZE PLIKI (>100MB) ==="
echo "Szukanie największych plików..."
find / -type f -size +100M -exec ls -lh {} \; 2>/dev/null | sort -k5 -hr | head -10
echo ""

# ========================================
# 7. SPRAWDŹ WYNIKI
# ========================================
echo "=== WYNIKI CZYSZCZENIA ==="
echo "Stan dysku po czyszczeniu:"
df -h /
echo ""

echo "Największe katalogi po czyszczeniu:"
du -h --max-depth=2 / 2>/dev/null | sort -hr | head -10
echo ""

# ========================================
# 8. REKOMENDACJE
# ========================================
echo "=== REKOMENDACJE ==="
echo "1. Sprawdź czy można usunąć stare backupy z /home/<USER>/backup/"
echo "2. Rozważ przeniesienie danych do zewnętrznego storage"
echo "3. Skonfiguruj automatyczne czyszczenie logów"
echo "4. Monitoruj wykorzystanie dysku regularnie"
echo ""

echo "=== KONIEC CZYSZCZENIA ==="
echo "Data zakończenia: $(date)"
