---
# ============================================================================
# PLAYBOOK KONTROLI ZDROWIA SERWERÓW
# ============================================================================
# Cel: Kompleksowa analiza zdrowia wszystkich serwerów i aplikacji
# Wykrywa problemy, analizuje logi, sprawdza wydajność
# Opcjonalnie używa AI (Claude/Gemini) do analizy logów
# ============================================================================

- name: "Informacje o kontroli zdrowia"
  hosts: localhost
  gather_facts: false
  vars:
    health_timestamp: "{{ ansible_date_time.epoch }}"
    health_date: "{{ ansible_date_time.iso8601_basic_short }}"
    health_report_dir: "/tmp/health_check_{{ health_date }}"
    ai_analysis_enabled: "{{ ai_analysis | default(false) }}"
    
  tasks:
    - name: "Rozpoczęcie kontroli zdrowia serwerów"
      debug:
        msg:
          - "=== KONTROLA ZDROWIA WSZYSTKICH SERWERÓW ==="
          - "Data: {{ ansible_date_time.iso8601 }}"
          - "Raport: {{ health_report_dir }}"
          - "AI Analiza: {{ 'WŁĄCZONA' if ai_analysis_enabled else 'WYŁĄCZONA' }}"
          - ""
          - "SPRAWDZANE ELEMENTY:"
          - "🔍 Zasoby systemowe (CPU, RAM, dysk)"
          - "🔍 Kluczowe usługi i procesy"
          - "🔍 Aplikacje i kontenery Docker"
          - "🔍 Logi systemowe i aplikacji"
          - "🔍 Bezpieczeństwo i dostępność"
          - "🔍 Wydajność i bottlenecki"
      tags: [info]

    - name: "Stwórz katalog raportu zdrowia"
      file:
        path: "{{ health_report_dir }}"
        state: directory
        mode: '0755'
      delegate_to: localhost
      tags: [setup]

# ============================================================================
# KONTROLA ZDROWIA SERWERÓW NOWYCH
# ============================================================================

- name: "Kontrola zdrowia serwerów nowych"
  hosts: serwery_nowe
  gather_facts: true
  become: true
  
  vars:
    server_type: "nowy"
    health_timestamp: "{{ hostvars['localhost']['health_timestamp'] }}"
    health_date: "{{ hostvars['localhost']['health_date'] }}"
    health_report_dir: "{{ hostvars['localhost']['health_report_dir'] }}"
    ai_analysis_enabled: "{{ hostvars['localhost']['ai_analysis_enabled'] }}"
    
  tasks:
    - name: "=== KONTROLA ZDROWIA SERWERA NOWEGO ==="
      debug:
        msg:
          - "Sprawdzam zdrowie serwera: {{ inventory_hostname }}"
          - "Typ: {{ server_type }}"
          - "System: {{ ansible_distribution }} {{ ansible_distribution_version }}"
      tags: [info]

    # ========================================================================
    # ANALIZA ZASOBÓW SYSTEMOWYCH
    # ========================================================================
    
    - name: "Analiza zasobów systemowych"
      shell: |
        echo "=== ANALIZA ZASOBÓW SYSTEMOWYCH ==="
        
        # CPU
        echo "CPU Load Average:"
        uptime | awk -F'load average:' '{print $2}'
        echo "CPU Usage (top 5 procesów):"
        ps aux --sort=-%cpu | head -6
        
        # Pamięć
        echo ""
        echo "=== PAMIĘĆ ==="
        free -h
        echo ""
        echo "Procesy zużywające najwięcej pamięci:"
        ps aux --sort=-%mem | head -6
        
        # Dysk
        echo ""
        echo "=== DYSKI ==="
        df -h | grep -E "^/dev"
        echo ""
        echo "Największe katalogi w /:"
        du -sh /* 2>/dev/null | sort -hr | head -10
        
        # I/O
        echo ""
        echo "=== I/O STATISTICS ==="
        iostat -x 1 1 2>/dev/null | tail -n +4 || echo "iostat niedostępny"
        
        # Network
        echo ""
        echo "=== SIEĆ ==="
        ss -tuln | grep LISTEN | head -10
        echo "Aktywne połączenia:"
        ss -tun | wc -l
      register: resources_analysis
      tags: [resources]

    # ========================================================================
    # SPRAWDZENIE KLUCZOWYCH USŁUG
    # ========================================================================
    
    - name: "Sprawdzenie kluczowych usług"
      shell: |
        echo "=== KLUCZOWE USŁUGI ==="
        
        # Systemd services
        for service in ssh nginx docker systemd-resolved systemd-timesyncd; do
          status=$(systemctl is-active $service 2>/dev/null || echo "UNKNOWN")
          echo "$service: $status"
          if [ "$status" != "active" ] && [ "$status" != "UNKNOWN" ]; then
            echo "  ⚠️ Problem z usługą $service"
            systemctl status $service --no-pager -l | head -5
          fi
        done
        
        echo ""
        echo "=== DOCKER HEALTH ==="
        if command -v docker >/dev/null 2>&1; then
          echo "Docker daemon: $(systemctl is-active docker)"
          echo "Docker info:"
          docker info --format "{{.ServerVersion}}" 2>/dev/null || echo "Docker niedostępny"
          
          echo ""
          echo "Kontenery z problemami:"
          docker ps -a --filter "status=exited" --filter "status=dead" --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Brak"
          
          echo ""
          echo "Health checks kontenerów:"
          docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(unhealthy|starting)" || echo "Wszystkie kontenery zdrowe"
        else
          echo "Docker nie jest zainstalowany"
        fi
      register: services_health
      tags: [services]

    # ========================================================================
    # ANALIZA APLIKACJI BASEROW
    # ========================================================================
    
    - name: "Analiza aplikacji Baserow"
      shell: |
        echo "=== ANALIZA BASEROW ==="
        
        if [ -d "/opt/baserow" ]; then
          cd /opt/baserow
          
          echo "Status kontenerów Baserow:"
          docker-compose ps 2>/dev/null || echo "docker-compose niedostępny"
          
          echo ""
          echo "=== BASEROW BACKEND ==="
          if docker ps | grep -q baserow.*backend; then
            echo "Backend działa"
            echo "Logi backend (ostatnie 10 linii):"
            docker logs $(docker ps | grep baserow.*backend | awk '{print $1}') --tail 10 2>/dev/null || echo "Nie można pobrać logów"
          else
            echo "⚠️ Backend nie działa"
          fi
          
          echo ""
          echo "=== BASEROW FRONTEND ==="
          if docker ps | grep -q baserow.*frontend; then
            echo "Frontend działa"
          else
            echo "⚠️ Frontend nie działa"
          fi
          
          echo ""
          echo "=== BASEROW DATABASE ==="
          if docker ps | grep -q postgres; then
            echo "PostgreSQL działa"
            # Test połączenia z bazą
            docker exec $(docker ps | grep postgres | awk '{print $1}') pg_isready -U baserow 2>/dev/null && echo "✅ Baza dostępna" || echo "⚠️ Problem z bazą"
          else
            echo "⚠️ PostgreSQL nie działa"
          fi
          
          echo ""
          echo "=== BASEROW REDIS ==="
          if docker ps | grep -q redis; then
            echo "Redis działa"
            docker exec $(docker ps | grep redis | awk '{print $1}') redis-cli ping 2>/dev/null && echo "✅ Redis odpowiada" || echo "⚠️ Problem z Redis"
          else
            echo "⚠️ Redis nie działa"
          fi
          
          echo ""
          echo "=== BASEROW NGINX ==="
          if docker ps | grep -q nginx; then
            echo "Nginx działa"
            # Test HTTP
            curl -s -o /dev/null -w "%{http_code}" http://localhost 2>/dev/null | grep -q "200\|301\|302" && echo "✅ HTTP odpowiada" || echo "⚠️ Problem z HTTP"
          else
            echo "⚠️ Nginx nie działa"
          fi
          
        else
          echo "Baserow nie jest zainstalowany"
        fi
      register: baserow_health
      tags: [applications]

    # ========================================================================
    # ANALIZA LOGÓW SYSTEMOWYCH
    # ========================================================================
    
    - name: "Analiza logów systemowych"
      shell: |
        echo "=== ANALIZA LOGÓW SYSTEMOWYCH ==="
        
        echo "Błędy w ostatnich 24h:"
        error_count=$(journalctl --since "24 hours ago" --priority=err | wc -l)
        echo "Liczba błędów: $error_count"
        
        if [ $error_count -gt 0 ]; then
          echo ""
          echo "Ostatnie błędy (top 10):"
          journalctl --since "24 hours ago" --priority=err --no-pager | tail -10
        fi
        
        echo ""
        echo "Ostrzeżenia w ostatnich 24h:"
        warning_count=$(journalctl --since "24 hours ago" --priority=warning | wc -l)
        echo "Liczba ostrzeżeń: $warning_count"
        
        echo ""
        echo "=== LOGI DOCKER ==="
        if command -v docker >/dev/null 2>&1; then
          echo "Błędy w logach Docker (ostatnie 24h):"
          journalctl -u docker --since "24 hours ago" --priority=err | wc -l
          
          echo ""
          echo "Problematyczne kontenery (błędy w logach):"
          for container in $(docker ps --format "{{.Names}}" 2>/dev/null); do
            error_lines=$(docker logs $container --since 24h 2>&1 | grep -i -E "error|exception|failed|fatal" | wc -l)
            if [ $error_lines -gt 0 ]; then
              echo "⚠️ $container: $error_lines błędów"
              docker logs $container --since 24h 2>&1 | grep -i -E "error|exception|failed|fatal" | tail -3
            fi
          done
        fi
        
        echo ""
        echo "=== LOGI NGINX ==="
        if [ -f "/var/log/nginx/error.log" ]; then
          echo "Błędy Nginx (ostatnie 24h):"
          find /var/log/nginx/ -name "error.log*" -newermt "24 hours ago" -exec grep -h . {} \; | wc -l
        else
          echo "Logi Nginx niedostępne"
        fi
      register: logs_analysis
      tags: [logs]

    # ========================================================================
    # ANALIZA WYDAJNOŚCI
    # ========================================================================
    
    - name: "Analiza wydajności"
      shell: |
        echo "=== ANALIZA WYDAJNOŚCI ==="
        
        # Load average analysis
        load_1min=$(uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | tr -d ' ')
        cpu_cores=$(nproc)
        echo "Load 1min: $load_1min (CPU cores: $cpu_cores)"
        
        # Memory usage
        mem_used=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        echo "Wykorzystanie pamięci: ${mem_used}%"
        
        # Disk usage
        echo "Wykorzystanie dysku:"
        df -h | grep -E "^/dev" | awk '{print $5 " " $6}' | while read usage mount; do
          usage_num=$(echo $usage | tr -d '%')
          if [ $usage_num -gt 80 ]; then
            echo "⚠️ $mount: $usage (WYSOKIE)"
          else
            echo "✅ $mount: $usage"
          fi
        done
        
        # Network connections
        echo ""
        echo "Połączenia sieciowe:"
        ss -tun | wc -l
        
        # Top processes by CPU
        echo ""
        echo "Top procesy (CPU):"
        ps aux --sort=-%cpu | head -5 | awk '{print $11 " " $3 "%"}'
        
        # Top processes by Memory
        echo ""
        echo "Top procesy (RAM):"
        ps aux --sort=-%mem | head -5 | awk '{print $11 " " $4 "%"}'
      register: performance_analysis
      tags: [performance]

    # ========================================================================
    # ZAPIS RAPORTU ZDROWIA
    # ========================================================================
    
    - name: "Zapisz raport zdrowia serwera nowego"
      copy:
        content: |
          # RAPORT ZDROWIA: {{ inventory_hostname }}
          **Typ:** {{ server_type }}
          **Data:** {{ ansible_date_time.iso8601 }}
          **System:** {{ ansible_distribution }} {{ ansible_distribution_version }}
          
          ## 📊 Zasoby Systemowe
          ```
          {{ resources_analysis.stdout }}
          ```
          
          ## 🔧 Kluczowe Usługi
          ```
          {{ services_health.stdout }}
          ```
          
          ## 📱 Aplikacje (Baserow)
          ```
          {{ baserow_health.stdout }}
          ```
          
          ## 📋 Analiza Logów
          ```
          {{ logs_analysis.stdout }}
          ```
          
          ## ⚡ Wydajność
          ```
          {{ performance_analysis.stdout }}
          ```
          
          ---
          *Raport wygenerowany automatycznie*
        dest: "{{ health_report_dir }}/health_{{ inventory_hostname }}_{{ server_type }}.md"
      delegate_to: localhost
      tags: [report]

  tags: [new_servers]

# ============================================================================
# KONTROLA ZDROWIA SERWERÓW LEGACY
# ============================================================================

- name: "Kontrola zdrowia serwerów legacy"
  hosts: serwery_legacy
  gather_facts: true
  become: true
  
  vars:
    server_type: "legacy"
    health_timestamp: "{{ hostvars['localhost']['health_timestamp'] }}"
    health_date: "{{ hostvars['localhost']['health_date'] }}"
    health_report_dir: "{{ hostvars['localhost']['health_report_dir'] }}"
    
  tasks:
    - name: "=== KONTROLA ZDROWIA SERWERA LEGACY ==="
      debug:
        msg:
          - "Sprawdzam zdrowie serwera: {{ inventory_hostname }}"
          - "Typ: {{ server_type }}"
          - "Port SSH: {{ ansible_port | default(22) }}"
      tags: [info]

    # ========================================================================
    # ANALIZA ZASOBÓW LEGACY
    # ========================================================================
    
    - name: "Analiza zasobów systemowych (legacy)"
      shell: |
        echo "=== ANALIZA ZASOBÓW (LEGACY) ==="
        
        echo "CPU Load:"
        uptime | awk -F'load average:' '{print $2}'
        
        echo ""
        echo "Pamięć:"
        free -h
        
        echo ""
        echo "Dyski:"
        df -h | head -10
        
        echo ""
        echo "Top procesy:"
        ps aux --sort=-%cpu | head -5
      register: legacy_resources
      ignore_errors: true
      tags: [resources]

    # ========================================================================
    # APLIKACJE SPECYFICZNE LEGACY
    # ========================================================================
    
    - name: "Sprawdzenie aplikacji specyficznych (legacy)"
      shell: |
        echo "=== APLIKACJE LEGACY ==="
        echo "Serwer: {{ inventory_hostname }}"
        
        if [ "{{ inventory_hostname }}" = "simetria-ovh" ]; then
          echo ""
          echo "=== N8N HEALTH CHECK ==="
          if [ -d "/home/<USER>/projekty/n8n" ]; then
            cd /home/<USER>/projekty/n8n
            
            echo "Status kontenerów n8n:"
            docker-compose ps 2>/dev/null || echo "docker-compose niedostępny"
            
            echo ""
            echo "N8N Health:"
            if docker ps | grep -q "n8n.*Up"; then
              echo "✅ N8N działa"
              # Test HTTP
              curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:5678 2>/dev/null | grep -q "200" && echo "✅ N8N HTTP OK" || echo "⚠️ N8N HTTP problem"
              
              echo "Wersja N8N:"
              docker exec n8n n8n --version 2>/dev/null || echo "Nie można sprawdzić wersji"
              
              echo ""
              echo "Logi N8N (ostatnie 5 linii):"
              docker logs n8n --tail 5 2>/dev/null || echo "Nie można pobrać logów"
            else
              echo "⚠️ N8N nie działa"
            fi
            
            echo ""
            echo "PostgreSQL Health:"
            if docker ps | grep -q "n8n_postgres.*Up"; then
              echo "✅ PostgreSQL działa"
              docker exec n8n_postgres pg_isready -U n8n_user 2>/dev/null && echo "✅ DB dostępna" || echo "⚠️ DB problem"
            else
              echo "⚠️ PostgreSQL nie działa"
            fi
          else
            echo "⚠️ Katalog n8n nie istnieje"
          fi
          
          echo ""
          echo "=== QDRANT HEALTH CHECK ==="
          if docker ps | grep -q qdrant; then
            echo "✅ Qdrant działa"
            # Test Qdrant API
            curl -s http://127.0.0.1:6333/collections 2>/dev/null | grep -q "\[\]" && echo "✅ Qdrant API OK" || echo "⚠️ Qdrant API problem"
          else
            echo "⚠️ Qdrant nie działa"
          fi
          
        elif [ "{{ inventory_hostname }}" = "sms-server" ]; then
          echo ""
          echo "=== SMS SERVER HEALTH CHECK ==="
          
          echo "CloudFlare Tunnel:"
          systemctl is-active cloudflared 2>/dev/null && echo "✅ CloudFlare Tunnel aktywny" || echo "⚠️ CloudFlare Tunnel nieaktywny"
          
          echo ""
          echo "Procesy SMS:"
          ps aux | grep -i sms | grep -v grep | wc -l
          
          echo ""
          echo "Połączenia sieciowe:"
          ss -tuln | grep LISTEN | wc -l
        fi
      register: legacy_apps_health
      ignore_errors: true
      tags: [applications]

    # ========================================================================
    # ANALIZA LOGÓW LEGACY
    # ========================================================================
    
    - name: "Analiza logów (legacy)"
      shell: |
        echo "=== ANALIZA LOGÓW (LEGACY) ==="
        
        echo "Błędy systemowe (24h):"
        journalctl --since "24 hours ago" --priority=err 2>/dev/null | wc -l || echo "Nie można sprawdzić"
        
        echo ""
        echo "Logi SSH (nieudane logowania):"
        journalctl -u ssh --since "24 hours ago" 2>/dev/null | grep "Failed password" | wc -l || echo "Nie można sprawdzić"
        
        if [ "{{ inventory_hostname }}" = "simetria-ovh" ]; then
          echo ""
          echo "=== LOGI N8N ==="
          if docker ps | grep -q n8n; then
            echo "Błędy w logach n8n (24h):"
            docker logs n8n --since 24h 2>&1 | grep -i -E "error|exception|failed" | wc -l || echo "0"
            
            echo "Ostatnie błędy n8n:"
            docker logs n8n --since 24h 2>&1 | grep -i -E "error|exception|failed" | tail -3 || echo "Brak błędów"
          fi
        fi
      register: legacy_logs
      ignore_errors: true
      tags: [logs]

    # ========================================================================
    # ZAPIS RAPORTU LEGACY
    # ========================================================================
    
    - name: "Zapisz raport zdrowia serwera legacy"
      copy:
        content: |
          # RAPORT ZDROWIA: {{ inventory_hostname }}
          **Typ:** {{ server_type }}
          **Data:** {{ ansible_date_time.iso8601 }}
          **System:** {{ ansible_distribution }} {{ ansible_distribution_version }}
          **Port SSH:** {{ ansible_port | default(22) }}
          
          ## 📊 Zasoby Systemowe
          ```
          {{ legacy_resources.stdout }}
          ```
          
          ## 📱 Aplikacje Specyficzne
          ```
          {{ legacy_apps_health.stdout }}
          ```
          
          ## 📋 Analiza Logów
          ```
          {{ legacy_logs.stdout }}
          ```
          
          ---
          *Raport wygenerowany automatycznie*
        dest: "{{ health_report_dir }}/health_{{ inventory_hostname }}_{{ server_type }}.md"
      delegate_to: localhost
      tags: [report]

  tags: [legacy_servers]

# ============================================================================
# AI ANALIZA LOGÓW (OPCJONALNA)
# ============================================================================

- name: "AI Analiza logów (jeśli włączona)"
  hosts: localhost
  gather_facts: false
  vars:
    health_report_dir: "{{ hostvars['localhost']['health_report_dir'] }}"
    ai_analysis_enabled: "{{ hostvars['localhost']['ai_analysis_enabled'] }}"

  tasks:
    - name: "Sprawdź czy AI analiza jest włączona"
      debug:
        msg: "AI Analiza: {{ 'WŁĄCZONA' if ai_analysis_enabled else 'WYŁĄCZONA' }}"
      tags: [ai]

    - name: "Zbierz problematyczne logi do analizy AI"
      shell: |
        echo "=== ZBIERANIE LOGÓW DO ANALIZY AI ==="

        # Znajdź wszystkie raporty zdrowia
        for report in {{ health_report_dir }}/health_*.md; do
          if [ -f "$report" ]; then
            echo "Analizuję: $report"

            # Wyciągnij sekcje z błędami
            grep -A 10 -B 2 -i "error\|warning\|failed\|problem\|⚠️" "$report" || echo "Brak problemów w $report"
          fi
        done
      register: logs_for_ai
      when: ai_analysis_enabled
      tags: [ai]

    - name: "Analiza AI z Claude (jeśli dostępne)"
      shell: |
        if command -v claude >/dev/null 2>&1; then
          echo "=== ANALIZA AI Z CLAUDE ==="
          echo "{{ logs_for_ai.stdout }}" | claude "Przeanalizuj te logi serwerów i wskaż najważniejsze problemy oraz rekomendacje naprawcze. Odpowiedz po polsku." > {{ health_report_dir }}/ai_analysis_claude.txt 2>/dev/null || echo "Błąd analizy Claude"
          echo "✅ Analiza Claude zapisana w ai_analysis_claude.txt"
        else
          echo "Claude CLI niedostępny"
        fi
      register: claude_analysis
      when: ai_analysis_enabled
      ignore_errors: true
      tags: [ai]

    - name: "Analiza AI z Gemini (jeśli dostępne)"
      shell: |
        if command -v gemini >/dev/null 2>&1; then
          echo "=== ANALIZA AI Z GEMINI ==="
          echo "{{ logs_for_ai.stdout }}" | gemini "Przeanalizuj te logi serwerów i wskaż najważniejsze problemy oraz rekomendacje naprawcze. Odpowiedz po polsku." > {{ health_report_dir }}/ai_analysis_gemini.txt 2>/dev/null || echo "Błąd analizy Gemini"
          echo "✅ Analiza Gemini zapisana w ai_analysis_gemini.txt"
        else
          echo "Gemini CLI niedostępny"
        fi
      register: gemini_analysis
      when: ai_analysis_enabled
      ignore_errors: true
      tags: [ai]

    - name: "Wyniki analizy AI"
      debug:
        msg:
          - "{{ claude_analysis.stdout_lines | default(['Claude niedostępny']) }}"
          - "{{ gemini_analysis.stdout_lines | default(['Gemini niedostępny']) }}"
      when: ai_analysis_enabled
      tags: [ai]

# ============================================================================
# GENEROWANIE RAPORTU ZDROWIA
# ============================================================================

- name: "Generowanie raportu zdrowia"
  hosts: localhost
  gather_facts: false
  vars:
    health_timestamp: "{{ hostvars['localhost']['health_timestamp'] }}"
    health_date: "{{ hostvars['localhost']['health_date'] }}"
    health_report_dir: "{{ hostvars['localhost']['health_report_dir'] }}"
    ai_analysis_enabled: "{{ hostvars['localhost']['ai_analysis_enabled'] }}"

  tasks:
    - name: "Analiza wszystkich raportów zdrowia"
      shell: |
        echo "=== ANALIZA RAPORTÓW ZDROWIA ==="

        total_servers=0
        healthy_servers=0
        warning_servers=0
        critical_servers=0

        for report in {{ health_report_dir }}/health_*.md; do
          if [ -f "$report" ]; then
            total_servers=$((total_servers + 1))
            server_name=$(basename "$report" | sed 's/health_//g' | sed 's/\.md//g')

            # Sprawdź czy są problemy
            if grep -q "⚠️\|ERROR\|FAILED\|problem" "$report"; then
              if grep -q "CRITICAL\|FATAL\|nie działa" "$report"; then
                critical_servers=$((critical_servers + 1))
                echo "🔴 $server_name: KRYTYCZNY"
              else
                warning_servers=$((warning_servers + 1))
                echo "🟡 $server_name: OSTRZEŻENIA"
              fi
            else
              healthy_servers=$((healthy_servers + 1))
              echo "🟢 $server_name: ZDROWY"
            fi
          fi
        done

        echo ""
        echo "=== PODSUMOWANIE ==="
        echo "Łącznie serwerów: $total_servers"
        echo "Zdrowe: $healthy_servers"
        echo "Ostrzeżenia: $warning_servers"
        echo "Krytyczne: $critical_servers"

        # Określ ogólny status
        if [ $critical_servers -gt 0 ]; then
          echo "OGÓLNY STATUS: 🔴 KRYTYCZNY"
        elif [ $warning_servers -gt 0 ]; then
          echo "OGÓLNY STATUS: 🟡 OSTRZEŻENIA"
        else
          echo "OGÓLNY STATUS: 🟢 ZDROWY"
        fi
      register: health_summary
      tags: [summary]

    - name: "Generuj raport zdrowia HTML"
      copy:
        content: |
          <!DOCTYPE html>
          <html>
          <head>
              <title>Raport Zdrowia Serwerów - {{ health_date }}</title>
              <meta charset="UTF-8">
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; }
                  .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
                  .summary { margin: 20px 0; padding: 15px; border-radius: 5px; }
                  .healthy { background: #d4edda; border-left: 5px solid #28a745; }
                  .warning { background: #fff3cd; border-left: 5px solid #ffc107; }
                  .critical { background: #f8d7da; border-left: 5px solid #dc3545; }
                  .server-report { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                  .status-healthy { color: #28a745; }
                  .status-warning { color: #ffc107; }
                  .status-critical { color: #dc3545; }
                  pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
                  .ai-analysis { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
              </style>
          </head>
          <body>
              <div class="header">
                  <h1>🏥 Raport Zdrowia Serwerów</h1>
                  <p><strong>Data generowania:</strong> {{ ansible_date_time.iso8601 }}</p>
                  <p><strong>Timestamp:</strong> {{ health_timestamp }}</p>
                  <p><strong>AI Analiza:</strong> {{ 'Włączona' if ai_analysis_enabled else 'Wyłączona' }}</p>
              </div>

              <div class="summary">
                  <h2>📊 Podsumowanie Zdrowia</h2>
                  <pre>{{ health_summary.stdout }}</pre>
              </div>

              <h2>📁 Szczegółowe Raporty Zdrowia</h2>
              <p>Szczegółowe raporty dla każdego serwera:</p>
              <ul>
                  <li><code>health_simetria-hetzner001_nowy.md</code> - Serwer Hetzner (nowy)</li>
                  <li><code>health_simetria-ovh_legacy.md</code> - Serwer OVH (legacy)</li>
                  <li><code>health_sms-server_legacy.md</code> - SMS Server (legacy)</li>
              </ul>

              {% if ai_analysis_enabled %}
              <div class="ai-analysis">
                  <h2>🤖 Analiza AI</h2>
                  <p>Pliki z analizą AI (jeśli dostępne):</p>
                  <ul>
                      <li><code>ai_analysis_claude.txt</code> - Analiza Claude</li>
                      <li><code>ai_analysis_gemini.txt</code> - Analiza Gemini</li>
                  </ul>
              </div>
              {% endif %}

              <h2>🔧 Rekomendowane Działania</h2>
              <ol>
                  <li>Przejrzyj szczegółowe raporty zdrowia każdego serwera</li>
                  <li>Sprawdź serwery z ostrzeżeniami lub problemami krytycznymi</li>
                  <li>Wykonaj aktualizacje jeśli potrzeba: <code>ansible-playbook update_all_servers.yml</code></li>
                  <li>W razie problemów uruchom diagnostykę specyficzną dla serwera</li>
                  {% if ai_analysis_enabled %}
                  <li>Przejrzyj rekomendacje AI w plikach analizy</li>
                  {% endif %}
              </ol>

              <h2>🚨 Alerty</h2>
              <ul>
                  <li>🔴 <strong>Krytyczne:</strong> Wymagają natychmiastowej interwencji</li>
                  <li>🟡 <strong>Ostrzeżenia:</strong> Wymagają monitorowania</li>
                  <li>🟢 <strong>Zdrowe:</strong> Działają poprawnie</li>
              </ul>

              <hr>
              <p><small>Raport wygenerowany automatycznie przez Ansible Health Check</small></p>
          </body>
          </html>
        dest: "{{ health_report_dir }}/health_check_report.html"
      tags: [report]

    - name: "=== PODSUMOWANIE KONTROLI ZDROWIA ==="
      debug:
        msg:
          - "Kontrola zdrowia wszystkich serwerów zakończona"
          - ""
          - "WYNIKI:"
          - "{{ health_summary.stdout_lines }}"
          - ""
          - "RAPORTY WYGENEROWANE:"
          - "📁 Katalog: {{ health_report_dir }}"
          - "📄 Raport HTML: health_check_report.html"
          - "📄 Raporty szczegółowe: health_*.md"
          - "{% if ai_analysis_enabled %}🤖 Analiza AI: ai_analysis_*.txt{% endif %}"
          - ""
          - "NASTĘPNE KROKI:"
          - "1. Otwórz raport HTML: open {{ health_report_dir }}/health_check_report.html"
          - "2. Sprawdź serwery z problemami"
          - "3. Wykonaj naprawcze działania"
          - ""
          - "KOMENDY:"
          - "# Otwórz raport"
          - "open {{ health_report_dir }}/health_check_report.html"
          - "# Aktualizacja serwerów"
          - "ansible-playbook update_all_servers.yml"
          - "# Ponowna kontrola zdrowia"
          - "ansible-playbook health_check.yml"
      tags: [summary]
